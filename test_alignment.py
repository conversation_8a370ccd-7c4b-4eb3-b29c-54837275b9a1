#!/usr/bin/env python3
"""
Test script for Two Point Alignment Plugin
This script tests the mathematical functions of the alignment plugin
"""

import sys
import os
import math

# Add bCNC directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'bCNC'))

def test_transformation():
    """Test the transformation calculations"""
    print("Testing Two Point Alignment Transformation...")
    
    # Test case 1: Simple translation (no rotation, no scaling)
    print("\n=== Test 1: Simple Translation ===")
    
    # Reference points
    p1x, p1y = 0.0, 0.0
    p2x, p2y = 10.0, 0.0
    
    # Target points (move 5 units right, 3 units up)
    t1x, t1y = 5.0, 3.0
    t2x, t2y = 15.0, 3.0
    
    # Calculate transformation
    ref_vector = (p2x - p1x, p2y - p1y)
    target_vector = (t2x - t1x, t2y - t1y)
    
    ref_length = math.sqrt(ref_vector[0]**2 + ref_vector[1]**2)
    target_length = math.sqrt(target_vector[0]**2 + target_vector[1]**2)
    
    scale = target_length / ref_length
    
    ref_angle = math.atan2(ref_vector[1], ref_vector[0])
    target_angle = math.atan2(target_vector[1], target_vector[0])
    rotation = target_angle - ref_angle
    
    cos_r = math.cos(rotation)
    sin_r = math.sin(rotation)
    
    new_p1x = p1x * scale * cos_r - p1y * scale * sin_r
    new_p1y = p1x * scale * sin_r + p1y * scale * cos_r
    
    tx = t1x - new_p1x
    ty = t1y - new_p1y
    
    print(f"Scale: {scale:.4f}")
    print(f"Rotation: {math.degrees(rotation):.2f}°")
    print(f"Translation: X={tx:.3f}, Y={ty:.3f}")
    
    # Test transformation on a point
    test_x, test_y = 5.0, 5.0
    new_x = test_x * scale * cos_r - test_y * scale * sin_r + tx
    new_y = test_x * scale * sin_r + test_y * scale * cos_r + ty
    
    print(f"Point ({test_x}, {test_y}) transforms to ({new_x:.3f}, {new_y:.3f})")
    
    # Test case 2: 45-degree rotation
    print("\n=== Test 2: 45-degree Rotation ===")
    
    # Reference points (horizontal line)
    p1x, p1y = 0.0, 0.0
    p2x, p2y = 10.0, 0.0
    
    # Target points (45-degree diagonal)
    t1x, t1y = 0.0, 0.0
    t2x, t2y = 7.071, 7.071  # 10 * cos(45°), 10 * sin(45°)
    
    # Calculate transformation
    ref_vector = (p2x - p1x, p2y - p1y)
    target_vector = (t2x - t1x, t2y - t1y)
    
    ref_length = math.sqrt(ref_vector[0]**2 + ref_vector[1]**2)
    target_length = math.sqrt(target_vector[0]**2 + target_vector[1]**2)
    
    scale = target_length / ref_length
    
    ref_angle = math.atan2(ref_vector[1], ref_vector[0])
    target_angle = math.atan2(target_vector[1], target_vector[0])
    rotation = target_angle - ref_angle
    
    print(f"Scale: {scale:.4f}")
    print(f"Rotation: {math.degrees(rotation):.2f}°")
    
    # Test case 3: Scaling
    print("\n=== Test 3: Scaling (2x) ===")
    
    # Reference points
    p1x, p1y = 0.0, 0.0
    p2x, p2y = 5.0, 0.0
    
    # Target points (double the distance)
    t1x, t1y = 0.0, 0.0
    t2x, t2y = 10.0, 0.0
    
    # Calculate transformation
    ref_vector = (p2x - p1x, p2y - p1y)
    target_vector = (t2x - t1x, t2y - t1y)
    
    ref_length = math.sqrt(ref_vector[0]**2 + ref_vector[1]**2)
    target_length = math.sqrt(target_vector[0]**2 + target_vector[1]**2)
    
    scale = target_length / ref_length
    
    print(f"Scale: {scale:.4f}")
    print(f"Expected: 2.0000")
    
    print("\n=== All Tests Completed ===")

def test_gcode_parsing():
    """Test G-code line parsing and transformation"""
    print("\n=== Testing G-code Parsing ===")
    
    # Sample G-code lines
    test_lines = [
        "G0 X10.0 Y20.0",
        "G1 X15.5 Y25.3 F1000",
        "G2 X20 Y30 I5 J5",
        "(This is a comment)",
        "M3 S1000",
        "G0 Z5.0",
        ""
    ]
    
    # Simple transformation parameters (translate by 5,3)
    scale_factor = 1.0
    rotation_angle = 0.0
    translation_x = 5.0
    translation_y = 3.0
    
    cos_r = math.cos(rotation_angle)
    sin_r = math.sin(rotation_angle)
    
    for line in test_lines:
        print(f"Original: '{line}'")
        
        # Skip comments and empty lines
        if line.strip().startswith('(') or line.strip().startswith(';') or not line.strip():
            print(f"Transformed: '{line}' (unchanged)")
            continue
            
        # Parse the line
        parts = line.split()
        new_parts = []
        
        x, y, z = None, None, None
        
        for part in parts:
            part = part.strip()
            if part.upper().startswith('X'):
                try:
                    x = float(part[1:])
                except:
                    new_parts.append(part)
            elif part.upper().startswith('Y'):
                try:
                    y = float(part[1:])
                except:
                    new_parts.append(part)
            elif part.upper().startswith('Z'):
                try:
                    z = float(part[1:])
                    new_parts.append(part)  # Keep Z unchanged
                except:
                    new_parts.append(part)
            else:
                new_parts.append(part)
        
        # Apply transformation
        if x is not None or y is not None:
            orig_x = x if x is not None else 0.0
            orig_y = y if y is not None else 0.0
            
            new_x = (orig_x * scale_factor * cos_r - 
                     orig_y * scale_factor * sin_r + 
                     translation_x)
            new_y = (orig_x * scale_factor * sin_r + 
                     orig_y * scale_factor * cos_r + 
                     translation_y)
            
            if x is not None:
                new_parts.append(f"X{new_x:.4f}")
            if y is not None:
                new_parts.append(f"Y{new_y:.4f}")
        
        transformed_line = " ".join(new_parts)
        print(f"Transformed: '{transformed_line}'")
        print()

if __name__ == "__main__":
    test_transformation()
    test_gcode_parsing()
    print("\nTest completed successfully!")
