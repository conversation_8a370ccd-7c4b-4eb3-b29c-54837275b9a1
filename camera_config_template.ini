# Camera Configuration Template for bCNC
# Copy these settings to your bCNC configuration

[Camera]
# Camera device number (0 = first camera, 1 = second camera, etc.)
# Set to 0 to enable the first camera
aligncam = 0

# Camera resolution
aligncam_width = 640
aligncam_height = 480

# Camera rotation angle (0, 90, 180, 270 degrees)
# Use this if your camera image is rotated
aligncam_angle = 0

# Reference circle radius in units (mm or inches)
aligncam_r = 3.175

# Camera scale (pixels per unit)
# Adjust this based on your camera zoom and distance
aligncam_scale = 10.0

# Camera anchor position
# Options: Center, N, S, E, W, NE, NW, SE, SW
aligncam_anchor = Center

# Camera offset from spindle (in units)
# Measure the distance between camera center and spindle center
aligncam_dx = 0
aligncam_dy = 0
aligncam_z = 0

# Fine adjustments
aligncam_rotation = 0
aligncam_xcenter = 0
aligncam_ycenter = 0

# Web camera settings (for pendant interface)
webcam = 0
webcam_width = 640
webcam_height = 480
webcam_angle = 0

# Two Point Alignment Plugin Settings
[TwoPointAlignment]
# Saved reference points (automatically updated by plugin)
point1_x = 0.0
point1_y = 0.0
point2_x = 0.0
point2_y = 0.0

# Instructions:
# 1. Copy the [Camera] section to your bCNC.ini file
# 2. Adjust aligncam to your camera number (usually 0)
# 3. Adjust resolution if needed
# 4. Save and restart bCNC
# 5. Test camera in Probe → Camera → Switch To
