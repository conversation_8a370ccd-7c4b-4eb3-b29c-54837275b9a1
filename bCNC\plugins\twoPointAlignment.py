# Author: AI Assistant
# Date: 2025-01-18
# Two Point Alignment Plugin for bCNC

import math
import tkinter as tk
from tkinter import *
from tkinter import messagebox
import tkExtra

from CNC import CNC, Block
from ToolsPage import Plugin
import Utils

__author__ = "AI Assistant"
__name__ = _("Two Point Alignment")
__version__ = "1.0"


class Tool(Plugin):
    __doc__ = _(
        """Align G-code using two reference points from camera"""
    )

    def __init__(self, master):
        Plugin.__init__(self, master, "TwoPointAlignment")
        self.icon = "setsquare32"  # Using alignment icon
        self.group = "CAM"
        self.oneshot = False
        
        # Variables for storing alignment points
        self.point1_x = 0.0
        self.point1_y = 0.0
        self.point2_x = 0.0
        self.point2_y = 0.0
        self.target1_x = 0.0
        self.target1_y = 0.0
        self.target2_x = 0.0
        self.target2_y = 0.0
        
        # GUI variables
        self.variables = [
            ("name", "db", "", _("Name")),
            ("", "", "", ""),  # Separator
            ("", "label", _("Reference Points (Current Position)"), ""),
            ("point1_x", "float", 0.0, _("Point 1 X")),
            ("point1_y", "float", 0.0, _("Point 1 Y")),
            ("point2_x", "float", 0.0, _("Point 2 X")),
            ("point2_y", "float", 0.0, _("Point 2 Y")),
            ("", "", "", ""),  # Separator
            ("", "label", _("Target Points (Desired Position)"), ""),
            ("target1_x", "float", 0.0, _("Target 1 X")),
            ("target1_y", "float", 0.0, _("Target 1 Y")),
            ("target2_x", "float", 0.0, _("Target 2 X")),
            ("target2_y", "float", 0.0, _("Target 2 Y")),
        ]
        
        # Add buttons
        self.buttons.append("capture1")  # Capture Point 1
        self.buttons.append("capture2")  # Capture Point 2
        self.buttons.append("calculate") # Calculate alignment
        self.buttons.append("apply")     # Apply alignment
        self.buttons.append("save_ref")  # Save reference points
        self.buttons.append("load_ref")  # Load reference points

    # ----------------------------------------------------------------------
    def capture1(self, app):
        """Capture current position as Point 1"""
        try:
            self["point1_x"] = CNC.vars["wx"]
            self["point1_y"] = CNC.vars["wy"]
            app.setStatus(_("Point 1 captured: X={:.3f}, Y={:.3f}").format(
                self["point1_x"], self["point1_y"]))
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def capture2(self, app):
        """Capture current position as Point 2"""
        try:
            self["point2_x"] = CNC.vars["wx"]
            self["point2_y"] = CNC.vars["wy"]
            app.setStatus(_("Point 2 captured: X={:.3f}, Y={:.3f}").format(
                self["point2_x"], self["point2_y"]))
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def save_ref(self, app):
        """Save current reference points to configuration"""
        try:
            Utils.setFloat("TwoPointAlignment", "point1_x", float(self["point1_x"]))
            Utils.setFloat("TwoPointAlignment", "point1_y", float(self["point1_y"]))
            Utils.setFloat("TwoPointAlignment", "point2_x", float(self["point2_x"]))
            Utils.setFloat("TwoPointAlignment", "point2_y", float(self["point2_y"]))
            app.setStatus(_("Reference points saved"))
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def load_ref(self, app):
        """Load reference points from configuration"""
        try:
            self["point1_x"] = Utils.getFloat("TwoPointAlignment", "point1_x", 0.0)
            self["point1_y"] = Utils.getFloat("TwoPointAlignment", "point1_y", 0.0)
            self["point2_x"] = Utils.getFloat("TwoPointAlignment", "point2_x", 0.0)
            self["point2_y"] = Utils.getFloat("TwoPointAlignment", "point2_y", 0.0)
            app.setStatus(_("Reference points loaded"))
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def calculate(self, app):
        """Calculate alignment transformation"""
        try:
            # Get reference points
            p1x, p1y = float(self["point1_x"]), float(self["point1_y"])
            p2x, p2y = float(self["point2_x"]), float(self["point2_y"])
            
            # Get target points
            t1x, t1y = float(self["target1_x"]), float(self["target1_y"])
            t2x, t2y = float(self["target2_x"]), float(self["target2_y"])
            
            # Calculate vectors
            ref_vector = (p2x - p1x, p2y - p1y)
            target_vector = (t2x - t1x, t2y - t1y)
            
            # Calculate lengths
            ref_length = math.sqrt(ref_vector[0]**2 + ref_vector[1]**2)
            target_length = math.sqrt(target_vector[0]**2 + target_vector[1]**2)
            
            if ref_length == 0 or target_length == 0:
                messagebox.showerror(_("Error"), 
                    _("Points cannot be identical. Please capture different points."))
                return
            
            # Calculate scale factor
            scale = target_length / ref_length
            
            # Calculate rotation angle
            ref_angle = math.atan2(ref_vector[1], ref_vector[0])
            target_angle = math.atan2(target_vector[1], target_vector[0])
            rotation = target_angle - ref_angle
            
            # Calculate translation
            # After rotation and scaling, point 1 should align with target 1
            cos_r = math.cos(rotation)
            sin_r = math.sin(rotation)
            
            # Rotated and scaled point 1
            new_p1x = p1x * scale * cos_r - p1y * scale * sin_r
            new_p1y = p1x * scale * sin_r + p1y * scale * cos_r
            
            # Translation needed
            tx = t1x - new_p1x
            ty = t1y - new_p1y
            
            # Store transformation parameters
            self.scale_factor = scale
            self.rotation_angle = rotation
            self.translation_x = tx
            self.translation_y = ty
            
            # Display results
            app.setStatus(_("Alignment calculated - Scale: {:.4f}, Rotation: {:.2f}°, Translation: X={:.3f}, Y={:.3f}").format(
                scale, math.degrees(rotation), tx, ty))
                
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def apply(self, app):
        """Apply alignment transformation to selected blocks"""
        if not hasattr(self, 'scale_factor'):
            messagebox.showerror(_("Error"), 
                _("Please calculate alignment first"))
            return
            
        try:
            # Get selected blocks
            selected_blocks = app.editor.getSelectedBlocks()
            if not selected_blocks:
                messagebox.showwarning(_("Warning"), 
                    _("Please select blocks to align"))
                return
            
            # Apply transformation to each selected block
            new_blocks = []
            for bid in selected_blocks:
                block = app.gcode[bid]
                new_block = Block(block.name() + "_aligned")
                
                for line in block:
                    new_line = self.transform_gcode_line(line)
                    new_block.append(new_line)
                
                new_blocks.append(new_block)
            
            # Insert new blocks
            active = app.editor.getActive()
            app.gcode.insBlocks(active, new_blocks, "Two Point Alignment")
            app.refresh()
            app.setStatus(_("Alignment applied to {} blocks").format(len(selected_blocks)))
            
        except Exception as e:
            messagebox.showerror(_("Error"), str(e))

    # ----------------------------------------------------------------------
    def transform_gcode_line(self, line):
        """Transform a single G-code line using 2D transformation matrix"""
        # Skip comments and empty lines
        if line.strip().startswith('(') or line.strip().startswith(';') or not line.strip():
            return line

        # Parse the line to extract coordinates
        parts = line.split()
        new_parts = []

        x, y, z = None, None, None

        for part in parts:
            part = part.strip()
            if part.upper().startswith('X'):
                try:
                    x = float(part[1:])
                except:
                    new_parts.append(part)
            elif part.upper().startswith('Y'):
                try:
                    y = float(part[1:])
                except:
                    new_parts.append(part)
            elif part.upper().startswith('Z'):
                try:
                    z = float(part[1:])
                    new_parts.append(part)  # Keep Z unchanged
                except:
                    new_parts.append(part)
            else:
                new_parts.append(part)

        # Apply 2D transformation if coordinates found
        if x is not None or y is not None:
            # Use 0 for missing coordinates
            orig_x = x if x is not None else 0.0
            orig_y = y if y is not None else 0.0

            # Apply 2D transformation matrix
            # [x']   [scale*cos(θ)  -scale*sin(θ)  tx] [x]
            # [y'] = [scale*sin(θ)   scale*cos(θ)  ty] [y]
            # [1 ]   [     0              0         1 ] [1]

            cos_r = math.cos(self.rotation_angle)
            sin_r = math.sin(self.rotation_angle)

            new_x = (orig_x * self.scale_factor * cos_r -
                     orig_y * self.scale_factor * sin_r +
                     self.translation_x)
            new_y = (orig_x * self.scale_factor * sin_r +
                     orig_y * self.scale_factor * cos_r +
                     self.translation_y)

            # Add transformed coordinates only if they were present in original
            if x is not None:
                new_parts.append(f"X{new_x:.{CNC.digits}f}")
            if y is not None:
                new_parts.append(f"Y{new_y:.{CNC.digits}f}")

        return " ".join(new_parts)

    # ----------------------------------------------------------------------
    def execute(self, app):
        """Main execute function - same as calculate for compatibility"""
        self.calculate(app)

    # ----------------------------------------------------------------------
    def processCommand(self, app, command):
        """Process commands from web interface"""
        if command.startswith("(CAPTURE_POINT_1_"):
            # Extract coordinates from command
            parts = command.replace("(", "").replace(")", "").split("_")
            try:
                x = float(parts[3][1:])  # Remove 'X' prefix
                y = float(parts[4][1:])  # Remove 'Y' prefix
                self["point1_x"] = x
                self["point1_y"] = y
                app.setStatus(_("Point 1 captured from web: X={:.3f}, Y={:.3f}").format(x, y))
            except:
                pass
        elif command.startswith("(CAPTURE_POINT_2_"):
            # Extract coordinates from command
            parts = command.replace("(", "").replace(")", "").split("_")
            try:
                x = float(parts[3][1:])  # Remove 'X' prefix
                y = float(parts[4][1:])  # Remove 'Y' prefix
                self["point2_x"] = x
                self["point2_y"] = y
                app.setStatus(_("Point 2 captured from web: X={:.3f}, Y={:.3f}").format(x, y))
            except:
                pass
        elif command == "(CALCULATE_ALIGNMENT)":
            self.calculate(app)
        elif command == "(APPLY_ALIGNMENT)":
            self.apply(app)
