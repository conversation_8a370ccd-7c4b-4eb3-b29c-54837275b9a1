# 🎯 التعليمات النهائية - تشغيل bCNC مع ميزة المحاذاة بين النقطتين

## 📋 **ملخص ما تم إنجازه**

✅ **تم إضافة ميزة المحاذاة بين النقطتين**  
✅ **تم إنشاء واجهة كاميرا محسنة**  
✅ **تم تحديث إعدادات الكاميرا**  
✅ **تم إنشاء أدوات المساعدة والاختبار**  

## 🚀 **خطوات التشغيل السريع**

### الخطوة 1: تثبيت المتطلبات
```bash
# شغل هذا الملف لتثبيت المتطلبات تلقائياً
install_camera_requirements.bat
```

### الخطوة 2: تشغيل البرنامج
```bash
# استخدم الملف المحسن للتشغيل
run_bCNC_with_camera.bat

# أو شغل مباشرة من مجلد bCNC
cd bCNC
python __main__.py
```

### الخطوة 3: تفعيل الكاميرا
1. **في البرنامج**: اذهب إلى `Probe` → `Camera`
2. **اضغط زر "Switch To"** لتفعيل الكاميرا
3. **يجب أن ترى صورة الكاميرا** في منطقة الرسم

### الخطوة 4: استخدام المحاذاة
1. **اضغط زر "Two Point Alignment"** في قسم Camera
2. **اتبع التعليمات** في النافذة المنبثقة

## 🎥 **استخدام ميزة المحاذاة بين النقطتين**

### السيناريو: قطعة عمل مائلة
```
المشكلة: قطعة العمل موضوعة بزاوية 15° ومزاحة عن الموضع المطلوب

الحل:
1. حرك الآلة إلى نقطة مرجعية على القطعة (مثل الزاوية)
2. اضغط "Capture Point 1"
3. حرك إلى نقطة أخرى على القطعة (مثل الحافة المقابلة)
4. اضغط "Capture Point 2"
5. حدد النقاط المستهدفة (حيث تريد أن تكون القطعة)
6. اضغط "Calculate" ثم "Apply"

النتيجة: G-code محاذى تماماً مع القطعة!
```

## 🔧 **الملفات والأدوات المتاحة**

### ملفات التشغيل:
- `run_bCNC_with_camera.bat` - تشغيل البرنامج مع دعم الكاميرا
- `install_camera_requirements.bat` - تثبيت المتطلبات

### ملفات الاختبار:
- `test_camera.py` - اختبار الكاميرات المتاحة
- `test_alignment.py` - اختبار خوارزميات المحاذاة

### ملفات الإعدادات:
- `camera_config_template.ini` - قالب إعدادات الكاميرا
- `bCNC/bCNC.ini` - تم تحديثه بإعدادات الكاميرا

### ملفات التوثيق:
- `TWO_POINT_ALIGNMENT_README.md` - دليل مفصل للميزة
- `QUICK_START_GUIDE.md` - دليل البدء السريع
- `CAMERA_SETUP_GUIDE.md` - دليل إعداد الكاميرا
- `INSTALL_CAMERA_REQUIREMENTS.md` - دليل حل المشاكل

## 🎯 **الميزات الجديدة**

### 1. إضافة المحاذاة (`bCNC/plugins/twoPointAlignment.py`)
- التقاط نقطتين مرجعيتين
- حساب التحويل (دوران، تكبير، إزاحة)
- تطبيق التحويل على G-code
- حفظ واستدعاء النقاط المرجعية

### 2. واجهة الكاميرا المحسنة (`bCNC/ProbePage.py`)
- زر "Two Point Alignment" في قسم Camera
- فتح الإضافة مباشرة

### 3. واجهة الويب المحسنة (`bCNC/pendant/camera.html`)
- أزرار التحكم في المحاذاة
- عرض النقاط المحفوظة
- تحكم من الهاتف الذكي

## 🔍 **اختبار النظام**

### اختبار الكاميرا:
```bash
python test_camera.py
```

### اختبار المحاذاة:
```bash
python test_alignment.py
```

### اختبار البرنامج:
1. شغل `run_bCNC_with_camera.bat`
2. اذهب إلى Probe → Camera
3. اضغط "Switch To"
4. ابحث عن زر "Two Point Alignment"

## ⚠️ **حل المشاكل الشائعة**

### مشكلة: "OpenCV not found"
```bash
pip install opencv-python==********
```

### مشكلة: "numpy version conflict"
```bash
pip uninstall numpy
pip install numpy==1.21.6
```

### مشكلة: الكاميرا لا تعمل
1. تأكد من توصيل الكاميرا
2. جرب أرقام كاميرا مختلفة (0, 1, 2)
3. أغلق البرامج الأخرى التي تستخدم الكاميرا

### مشكلة: زر المحاذاة غير مرئي
1. تأكد من تثبيت OpenCV
2. أعد تشغيل البرنامج
3. تحقق من وجود ملف الإضافة

## 🎉 **الاستمتاع بالميزة الجديدة!**

الآن يمكنك:
- ✅ محاذاة قطع العمل المائلة تلقائياً
- ✅ توفير الوقت في الإعداد
- ✅ تقليل الأخطاء والهدر
- ✅ العمل بدقة أكبر
- ✅ استخدام الكاميرا للتحكم الدقيق

---
**تم بواسطة**: AI Assistant  
**التاريخ**: 2025-01-18  
**الإصدار**: 1.0  

**استمتع بالبرمجة والتصنيع الدقيق! 🎯**
