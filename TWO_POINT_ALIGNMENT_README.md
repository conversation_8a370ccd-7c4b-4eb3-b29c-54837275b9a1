# Two Point Alignment Plugin for bCNC

## Overview
This plugin allows you to align G-code using two reference points captured from the camera. It's perfect for aligning workpieces that are not perfectly positioned on your CNC machine.

## Features
- ✅ Capture two reference points using current machine position
- ✅ Define target positions for alignment
- ✅ Calculate rotation, scaling, and translation automatically
- ✅ Apply transformation to selected G-code blocks
- ✅ Save and load reference points
- ✅ Web interface support for camera control
- ✅ Visual feedback and status messages

## How to Use

### Method 1: Using the Desktop Interface

1. **Open the Plugin**:
   - Go to the Camera section in the Probe tab
   - Click the "Two Point Alignment" button
   - Or go to Tools → Plugins → Two Point Alignment

2. **Capture Reference Points**:
   - Move your machine to the first reference point on your workpiece
   - Click "Capture Point 1" button
   - Move to the second reference point
   - Click "Capture Point 2" button

3. **Set Target Points**:
   - Enter the desired X,Y coordinates for where Point 1 should be
   - Enter the desired X,Y coordinates for where Point 2 should be

4. **Calculate and Apply**:
   - Click "Calculate" to compute the transformation
   - Select the G-code blocks you want to align
   - Click "Apply" to create aligned copies

### Method 2: Using the Web Interface

1. **Access Camera Interface**:
   - Open your browser and go to the pendant interface
   - Navigate to the camera page

2. **Use Alignment Controls**:
   - Move machine to first point and click "Capture Point 1"
   - Move to second point and click "Capture Point 2"
   - Click "Calculate" to compute alignment
   - Click "Apply" to transform selected blocks

## Mathematical Background

The plugin uses 2D transformation matrices to perform:

1. **Scaling**: Adjusts size based on distance between points
2. **Rotation**: Rotates to match the angle between reference and target vectors
3. **Translation**: Moves to align the first point with its target

The transformation matrix is:
```
[x']   [scale*cos(θ)  -scale*sin(θ)  tx] [x]
[y'] = [scale*sin(θ)   scale*cos(θ)  ty] [y]
[1 ]   [     0              0         1 ] [1]
```

## Tips for Best Results

1. **Choose Good Reference Points**:
   - Use points that are far apart for better accuracy
   - Choose easily identifiable features on your workpiece

2. **Camera Setup**:
   - Ensure your camera is properly calibrated
   - Use good lighting for clear visibility

3. **Workflow**:
   - Always test with a simple G-code first
   - Save reference points if you need to repeat the alignment

## Troubleshooting

- **"Points cannot be identical"**: Make sure you capture two different points
- **"Please calculate alignment first"**: Run the calculate step before applying
- **"Please select blocks to align"**: Select G-code blocks in the editor first

## Files Modified/Added

- `bCNC/plugins/twoPointAlignment.py` - Main plugin file
- `bCNC/ProbePage.py` - Added alignment button to camera interface
- `bCNC/pendant/camera.html` - Added web interface controls

## Version History

- v1.0 - Initial release with basic two-point alignment functionality
