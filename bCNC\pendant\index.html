<html>
<head>
<title>bCNC Pendant</title>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="stylesheet" type="text/css" href="pure-min.css" />
<script type="text/javascript" src="jquery-2.1.4.min.js" ></script>
<script type="text/javascript" src="fastclick.js"></script>

<script type="text/javascript">
	/* set up all functions */
	var rpmmax = 12000;
	var xmlhttp = false;
	var WCS = ["G54", "G55", "G56", "G57", "G58", "G59", "G28", "G30", "G92"];
	var WCSVAL = ["wcsx", "wcsy", "wcsz"];

	function findWcs(x)
	{
		for (i=0; i<WCS.length; i++)
		if (WCS[i]==x) return i;
		return -1;
	} // findWcs

	function getState()
	{
		$.ajax({
			url: '/state',
			dataType: 'json',
			success: function(result,status,xhr) {
				$('#state').html(result.state);
				$('#state').bgColor = result.color;
				$('#msg').html(result.msg);
				$('#x').html(result.wx);
				$('#y').html(result.wy);
				$('#z').html(result.wz);
				//parse $G response
				for (k=0; k<result.G.length; k++) {
					if (WCS.indexOf(result.G[k]) > -1)
						$('#wcs').val(result.G[k]);
					else if (result.G[k] == "M8")
						$('#coolant').val("On");
					else if (result.G[k] == "M3")
						$('#spindle').val("100%");
				}
			}
		});
		var canvas = $("#cnc-canvas");
		if (canvas.prop('complete')){
			canvas.attr(
				'src',
				'/canvas?' + new Date().getTime()
			);
		}
	} // getState

	/* parse once configuration */
	function getConfig()
	{
		$.ajax({
			url: '/config',
			dataType: 'json',
			success: function(result,status,xhr) {
				rpmmax = result.rpmmax;
			}
		});
	} // getConfig

	function vibrate(t)
	{ //safari has issues with 'notification'
		// if ("vibrate" in navigator) {
		//  navigator.vibrate(t);
		// } else if ("vibrate" in notification) {
		//  navigator.notification.vibrate(t);
		// }
	} // vibrate

	function sendCmd(command)
	{
		$.ajax({
			url: '/send',
			data: { cmd: command }
		});
		vibrate(500);
	}

	/*is this necessary when sendCmd would do the same thing?*/
	function sendGcode(command)
	{
		$.ajax({
			url: '/send',
			data: { gcode: command }
		});

		vibrate(50);
	} // sendCmd

	function setWcs()
	{
		wcs = $('#wcs').val();
		p = findWcs(wcs);

		if (p<6)
			cmd = "G10L20P"+(p+1);
		else
		if (p==6)
			cmd = "G28.1";
		else
		if (p==7)
			cmd = "G30.1";
		else
		if (p==8)
			cmd = "G92";

		for (i=0; i<WCSVAL.length; i++) {
			x = $("#"+WCSVAL[i]).val();
			if (x != "") {
				cmd += "XYZ"[i] + x;
				$("#"+WCSVAL[i]).val("");
				}
		}
		sendGcode(cmd+"\n$#\n$G");
	} // setWcs

	function wcsChange()
	{
		sendGcode($('#wcs option:selected').text() + "\n$G");
	} // wcsChange

	function spindleChange()
	{
		rpm = $('#spindle option:selected').text();
		if (rpm=="Off")
			sendGcode("M5");
		else {
			getConfig();
			rpm = (parseInt(rpm) * rpmmax)/100;
			sendGcode("M3 S"+rpm);
		}
	} // spindleChange

	function coolantChange()
	{
		coolant = $('#coolant option:selected').text();
		if (coolant=="Off")
			sendGcode("M9");
		else
		if (coolant == "On")
			sendGcode("M8");
	} // spindleChange

	function sendMove(command)
	{
		gcode = "G91G0";
		step = $('#step option:selected').text();
		switch (command) {
			case 'O':
				gcode = "G90G0X0Y0Z0";
				break;
			case 'XO':
				gcode = "G90G0X0";
				break;
			case 'YO':
				gcode = "G90G0Y0";
				break;
			case 'ZO':
				gcode = "G90G0Z0";
				break;
			case 'XdYu':
				gcode += "X-" + step + "Y" + step;
				break;
			case 'Yu':
				gcode += "Y" + step;
				break;
			case 'XuYu':
				gcode += "X" + step + "Y" + step;
				break;
			case 'Xu':
				gcode += "X" + step;
				break;
			case 'Xd':
				gcode += "X-" + step;
				break;
			case 'XdYd':
				gcode += "X-" + step + "Y-" + step;
				break;
			case 'Yd':
				gcode += "Y-" + step;
				break;
			case 'XuYd':
				gcode += "X" + step + "Y-" + step;
				break;
			case 'Zu':
				gcode += "Z" + step;
				break;
			case 'Zd':
				gcode += "Z-" + step;
				break;
			default:
				//handle for errors here.
				break;
		}
		sendGcode(gcode);
		sendGcode("G90");
	} // sendMove

	function fileChange()
	{
		var formData = new FormData($('#upload-file')[0]);
		$.ajax({
			url: '/upload',  //Server script to process data
			data: formData,
			type: 'POST',
			cache: false,
			contentType: false,
			processData: false
		});
	}


	/* PERFORM THESE ACTIONS ONCE THE PAGE HAS LOADED */
	$(document).ready(function ()
	{
		//set up fast click to handle mobile browser delay
		FastClick.attach(document.body);

		sendGcode("$#\n$G\n");
		getConfig();
		setInterval(getState, 1000);
		/* ASSIGN FUNCTIONS TO UI ELEMENTS */
	});
</script>

<style type="text/css">
	.command {
		width:100%;
		height:32px;
	}
	.fullwide {
		width:100%;
	}
	.large {
		text-align: center;
		font-size: 22px;
		color: DarkBlue;
	}
	.medium {
		text-align: center;
		font-size: 18px;
		color: DarkGreen;
	}
	.border > div {
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		border:1px solid #999;
	}
	.hdr {
		font-weight:bold;
		font-size: 1.1em;
		height:30px;
	}
	.direction {
		width: 100%;
		height: 64px;
	}
	.bold {
	    font-weight:bold;
	}
	input[type="number"] {
		width: 15%;
		box-sizing: border-box;
		-webkit-box-sizing:border-box;
		-moz-box-sizing: border-box;
	}
	img.canvas {
		display: block;
		margin: 0 auto;
		object-fit: scale-down;
		//max-height: 600px;
		//width: 100%;
		//height: auto;
	}
</style>
</head>

<body>
	<h1><center>bCNC Pendant</center></h1>

	<!-- CONTROL BUTTONS & STATUS INFO -->
	<div class="pure-g">
		<div class="pure-u-1-3"><button id="btn-home" onclick="sendCmd('HOME');" class="button command">Home</button></div>
		<div class="pure-u-1-3"><button id="btn-unlock" onclick="sendCmd('UNLOCK');" class="button command">Unlock</button></div>
		<div class="pure-u-1-3"><button id="btn-reset" onclick="sendCmd('RESET');" class="button command">Reset</button></div>
	</div>
	<br />
	<div class="pure-g border">
		<div class="pure-u-1 large border" id="state" style="border-bottom:0px;"><span>Status</span>&nbsp;</div>
	</div>
	<div class="pure-g border">
		<div class="pure-u-1 medium border" id="msg" style="border-bottom:0px;"><span>Message</span>&nbsp;</div>
	</div>

	<div class="pure-g border">
		<div class="pure-u-1-3 large">X: <span id='x'>&nbsp;</span></div>
		<div class="pure-u-1-3 large">Y: <span id='y'>&nbsp;</span></div>
		<div class="pure-u-1-3 large">Z: <span id='z'>&nbsp;</span></div>
	</div>
	<br />
	<div class="pure-g">
		<div class="pure-u-1-3"><button id="btn-run" onclick="sendCmd('RUN');" class="button command">Run</button></div>
		<div class="pure-u-1-3"><button id="btn-pause" onclick="sendCmd('PAUSE');" class="button command">Pause</button></div>
		<div class="pure-u-1-3"><button id="btn-stop" onclick="sendCmd('STOP');" class="button command">Stop</button></div>
	</div>

	<div class="pure-g border">
		<img src="/canvas" id="cnc-canvas" class="canvas" />
	</div>

	<br />
	<!-- MACHINE CONTROL SECTION -->
	<div class="pure-g"><div class="pure-u-1 hdr">Machine Control</div></div>

	<div class="pure-g">
		<div class="pure-u-1-4">
		Spindle:
			<select id="spindle" onchange="spindleChange();">
				<option value="Off" selected>Off</option>
				<option value="25%">25%</option>
				<option value="50%">50%</option>
				<option value="75%">75%</option>
				<option value="100%">100%</option>
			</select>
		</div>
	<div class="pure-u-1-4">
		Coolant:
			<select id="coolant" onchange="coolantChange();">
				<option value="Off" selected>Off</option>
				<option value="On">On</option>
			</select>
	</div>
    </div>
	<br />

	<div class="pure-g">
		<div class="pure-u-1-2">
			<span style="padding-top:2px;">Command:</span>
			<input id="cmd" style="width:75%"/>
			<button onclick="sendCmd(encodeURIComponent($('#cmd').val())); $('#cmd').val('');" class="button">
				Send
			</button>
		</div>
		<div class="pure-u-1-2">
			<form id="upload-file" style="width:75%" enctype="multipart/form-data">
			Select file to upload:
			<input name="file" type="file" onchange="fileChange();"/>
		</form>
		</div>
	</div>

	<br />
		<!-- WORK COORDINATE SYSTEM SECTION -->
		<div class="pure-g"><div class="pure-u-1 hdr">Work Coordinates</div></div>
			<div class="pure-g">
			<div class="pure-u-1">
			<select id="wcs" onchange="wcsChange();">
				<option value="G54" selected>G54</option>
				<option value="G55">G55</option>
				<option value="G56">G56</option>
				<option value="G57">G57</option>
				<option value="G58">G58</option>
				<option value="G59">G59</option>
				<option value="G28">G28</option>
				<option value="G30">G30</option>
				<option value="G92">G92</option>
			</select>

			<span style="padding-left: 10px; padding-top:2px;">X: </span><input id="wcsx" type="number" size="1"/>
			<span style="padding-left: 10px; padding-top:2px;">Y: </span><input id="wcsy" type="number" size="1"/>
			<span style="padding-left: 10px; padding-top:2px;">Z: </span><input id="wcsz" type="number" size="1"/>
			<button onclick="setWcs();" class="button">Set</button>
			</div>
		    </div>

	<br />
	<!-- JOGGING SECTION -->
	<div class="pure-g"><div class="pure-u-1 hdr">Jogging</div></div>

		<div class="pure-g">
		<div class="pure-u-1-6 bold" style="text-align:center;"><button onclick="sendMove('ZO')" class="button direction">Z 0</button></div>
		<div class="pure-u-1-3">&nbsp;</div>
		<div class="pure-u-1-6 bold" style="text-align:center;"><button onclick="sendMove('YO')" class="button direction">Y 0</button></div>
		</div>
	</div>

	<div class="pure-g">
		<div class="pure-u-1-6"><button onclick="sendMove('Zu')" class="button direction">&#x25B2;</button></div>
		<div class="pure-u-1-6">&nbsp;</div>
		<div class="pure-u-1-6"><button onclick="sendMove('XdYu')" class="button direction">&#x25F8;</button></div>
		<div class="pure-u-1-6"><button onclick="sendMove('Yu')"   class="button direction">&#x25B2;</button></div>
		<div class="pure-u-1-6"><button onclick="sendMove('XuYu')" class="button direction">&#x25F9;</button></div>
	</div>

	<div class="pure-g">
		<div class="pure-u-1-6" align="center">
			<select id="step" width="100%" style="margin-top:21px;">
				<option value="0.001">0.001</option>
				<option value="0.005">0.005</option>
				<option value="0.01">0.01</option>
				<option value="0.02">0.02</option>
				<option value="0.05">0.05</option>
				<option value="0.1">0.1</option>
				<option value="0.2">0.2</option>
				<option value="0.5">0.5</option>
				<option value="1" selected>1</option>
				<option value="2">2</option>
				<option value="5">5</option>
				<option value="10">10</option>
				<option value="20">20</option>
				<option value="50">50</option>
				<option value="100">100</option>
				<option value="500">500</option>
			</select>
		</div>

		<div class="pure-u-1-6 bold" style="text-align:center;"><button onclick="sendMove('XO')" class="button direction">X 0</button></div>
			<div class="pure-u-1-6"><button onclick="sendMove('Xd')" class="button direction">&#x25C0;</button></div>
			<div class="pure-u-1-6" align="center"><button onclick="sendMove('O')" class="button direction">&#x25EF;</button></div>
			<div class="pure-u-1-6"><button onclick="sendMove('Xu')" class="button direction">&#x25b6;</button></div>
		</div>
		<div class="pure-g">
			<div class="pure-u-1-6"><button onclick="sendMove('Zd')"   class="button direction">&#x25BC;</button></div>
			<div class="pure-u-1-6">&nbsp;</div>
			<div class="pure-u-1-6"><button onclick="sendMove('XdYd')" class="button direction">&#x25FA;</button></div>
			<div class="pure-u-1-6"><button onclick="sendMove('Yd')"   class="button direction">&#x25BC;</button></div>
			<div class="pure-u-1-6"><button onclick="sendMove('XuYd')" class="button direction">&#x25FF;</button></div>
		</div>
	</div>
</body>
</html>
