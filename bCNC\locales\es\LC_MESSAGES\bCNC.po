# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR ORGANIZATION
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-19 19:55+0200\n"
"PO-Revision-Date: 2022-07-19 20:16+0200\n"
"Last-Translator: @SteveMoto [GitHub]\n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: pygettext.py 1.5\n"
"X-Generator: Poedit 3.0.1\n"

#: bCNC/controllers/_GenericGRBL.py:51
msgid ""
"(Compile Option) Grbl '$' setting value exceeds the maximum step rate "
"supported."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:55
msgid ""
"(Grbl-Mega Only) Build info or startup line exceeded EEPROM line length "
"limit."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:69
msgid ""
"A G-code command implicitly or explicitly requires XYZ axis words in the "
"block, but none were detected."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:75
msgid ""
"A G-code command was sent, but is missing some required P or L value words "
"in the line."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:87
msgid ""
"A G2 or G3 arc was commanded but there are no XYZ axis words in the selected "
"plane to trace the arc."
msgstr ""
"Al comando G2 ó G3 le faltan las referencias XYZ necesarias para poder "
"trazar un arco."

#: bCNC/controllers/_GenericGRBL.py:96
msgid ""
"A G2 or G3 arc, traced with the offset definition, is missing the IJK offset "
"word in the selected plane to trace the arc."
msgstr ""
"Falta la definición del offset IJK en un comando de arco (G2 ó G3), por lo "
"que no puede ser trazado."

#: bCNC/controllers/_GenericGRBL.py:93
msgid ""
"A G2 or G3 arc, traced with the radius definition, had a mathematical error "
"when computing the arc geometry. Try either breaking up the arc into semi-"
"circles or quadrants, or redefine them with the arc offset definition."
msgstr ""

#: bCNC/ToolsPage.py:824
msgid ""
"Add additional margin/offset around islands to compensate for endmill "
"radius. This is automaticaly done for all islands if they are marked as tabs."
msgstr ""

#: bCNC/ProbePage.py:714
msgid ""
"Add an orientation marker. Jog first the machine to the marker position and "
"then click on canvas to add the marker."
msgstr ""
"Añadir un marcador de orientación. Primero mueva la máquina a la posición de "
"marcador, y después haga click sobre el espacio de trabajo para añadirlo."

#: bCNC/controllers/_GenericGRBL.py:16
msgid ""
"Alarm is an emergency state. Something has gone terribly wrong when these "
"occur. Typically, they are caused by limit error when the machine has moved "
"or wants to move outside the machine space and crash into something. They "
"also report problems if Grbl is lost and can't guarantee positioning or a "
"probe command has failed. Once in alarm-mode, Grbl will lock out and shut "
"down everything until the user issues a reset. Even after a reset, Grbl will "
"remain in alarm-mode, block all G-code from being executed, but allows the "
"user to override the alarm manually. This is to ensure the user knows and "
"acknowledges the problem and has taken steps to fix or account for it."
msgstr ""
"Las alarmas son el estado de emergencia del programa. Cuando éstas aparecen "
"es que algo ha ido terriblemente mal. Aparecen normalmente cuando la CNC "
"trata de moverse fuera de los límites de trabajo (p.e. contacto imprevisto "
"con los interruptores final de carrera) y el programa se detiene para evitar "
"colisiones. También ocurren cuando Grbl pierde alguna referencia y no puede "
"garantizar un posicionamiento correcto, o cuando falla un comando de sondeo. "
"Cuando salta una alarma, Grbl se bloquea completamente y desconecta todo "
"(motores, motor spindle, etc) hasta que el operario lo reinicie. Por "
"seguridad, Grbl permanecerá en modo alarma incluso después del reinicio, lo "
"que evita que se ejecute nuevo g-code; pero en este caso ya es posible "
"desactivar manualmente el estado de alarma. Éste procedimiento trata de "
"asegurar que el operario tiene conocimiento del problema y ha tomado los "
"pasos necesarios para solucionarlo."

#: bCNC/controllers/_GenericGRBL.py:33
msgid ""
"All is good! Everything in the last line was understood by Grbl and was "
"successfully processed and executed."
msgstr ""
"¡Todo bien! Grbl confirma que ha procesado y ejecutado todo el g-code "
"correctamente."

#: bCNC/plugins/linearize.py:50
msgid ""
"Also subdivide the lines. Otherwise only arcs and splines will be subdivided"
msgstr ""

#: bCNC/bmain.py:2418
msgid ""
"Autolevel/probe information already exists.\n"
"Delete it?"
msgstr ""
"Ya hay cargada información de sondeo/auto-nivelado\n"
"¿Desea borrarla?"

#: bCNC/plugins/slicemesh.py:276
msgid ""
"Calculating distance {} of {} (SciPy not installed => using SLOW fallback "
"method)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:505
msgid ""
"Currently there is some weird behaviour sometimes when trying to link "
"segments of pocket internally, so it can be disabled using this option. This "
"workaround should be fixed and removed in future."
msgstr ""

#: bCNC/ToolsPage.py:797
msgid ""
"Do the last cut once more in opposite direction. Helix bottom is disabled in "
"such case."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:168
msgid ""
"Door closed and resuming. Restoring from park, if applicable. Reset will "
"throw an alarm."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:165
msgid ""
"Door opened. Hold (or parking retract) in-progress. Reset will throw an "
"alarm."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:131
msgid ""
"G-code motion target exceeds machine travel. Machine position safely "
"retained. Alarm may be unlocked."
msgstr ""

#: bCNC/bmain.py:2526
#, fuzzy
#| msgid ""
#| "Gcode file {} was changed since editing started\n"
#| "Reload new version?"
msgid ""
"Gcode file {} was changed since editing started\n"
"Reload new version?"
msgstr ""
"El documento g-code ha cambiado en disco ¿Desea cargar la nueva versión "
"(desde {})?"

#: bCNC/ToolsPage.py:1027
msgid ""
"Generate pocket after profiling? Useful for making pockets with overcuts."
msgstr ""

#: bCNC/plugins/sketch.py:368
msgid ""
"Generated Sketch size W={} x H={} x distance={}, Total line:{}, Total length:"
"{}"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:43
msgid ""
"Grbl '$' command cannot be used unless Grbl is IDLE. Ensures smooth "
"operation during a job."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:19
msgid ""
"Grbl is in g-code check mode. If you send g-code to it, it will only check "
"it without actualy doing any motion. You can exit this by $C command (Or "
"equivalent button in terminal tab)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:26
msgid ""
"Grbl is in queue state. This also means you have relatively old GRBL "
"version, there are even 0.9 versions newer than this."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:23
msgid ""
"Grbl is in sleep mode. Motors are disabled, so you can move them manualy. "
"That also means that your machine might have lost the position (or "
"microsteps) and you may need to re-zero. Perform reset+unlock (or stop) to "
"wake Grbl again."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:78
msgid ""
"Grbl supports six work coordinate systems G54-G59. G59.1, G59.2, and G59.3 "
"are not supported."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:128
msgid ""
"Hard limit triggered. Machine position is likely lost due to sudden and "
"immediate halt. Re-homing is highly recommended."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:148
msgid ""
"Homing fail. Could not find limit switch within search distance. Defined as "
"1.5 * max_travel on search and 5 * pulloff on locate phases."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:158
msgid ""
"Homing fail. Could not find second limit switch for auto squared axis within "
"search distances. Try increasing max travel, decreasing pull-off distance, "
"or check wiring. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:145
msgid ""
"Homing fail. Cycle failed to clear limit switch when pulling off. Try "
"increasing pull-off setting or check wiring."
msgstr ""

#: bCNC/bmain.py:231
msgid ""
"MDI Command line: Accept g-code commands or macro commands (RESET/HOME...) "
"or editor commands (move,inkscape, round...) [Space or Ctrl-Space]"
msgstr ""
"Línea de comandos: Terminal que acepta comandos g-code, macros (RESET/"
"HOME...), o comandos del editor (move, inkscape, round...) [Espacio o Ctrl-"
"Espacio]"

#: bCNC/plugins/linearize.py:41
msgid ""
"Maximal length of resulting lines, smaller number means more precise output "
"and longer g-code. Length will be automaticaly truncated to be even across "
"whole subdivided segment."
msgstr ""

#: bCNC/ControlPage.py:1045
msgid ""
"Move to Origin.\n"
"User configurable button.\n"
"Right click to configure."
msgstr ""
"Mover al origen.\n"
"Puede configurar el botón haciendo click derecho."

#: bCNC/ControlPage.py:609 bCNC/ControlPage.py:916
msgid ""
"No info available.\n"
"Please contact the author."
msgstr ""
"No hay información disponible.\n"
"Por favor, contacte con el autor."

#: bCNC/plugins/endmilloffset.py:454
msgid ""
"Number of contours (Custom offset count) : indicates the number of contours "
"if custom selected. MAX:"
msgstr ""

#: bCNC/ProbePage.py:904
msgid ""
"Please verify that the probe is connected.\n"
"\n"
"Show this message again?"
msgstr ""
"Verifique que la sonda se encuentra conectada.\n"
"\n"
"¿Mostrar de nuevo el mensaje?"

#: bCNC/controllers/_GenericGRBL.py:140
msgid ""
"Probe fail. Probe did not contact the workpiece within the programmed travel "
"for G38.2 and G38.4."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:137
msgid ""
"Probe fail. The probe is not in the expected initial state before starting "
"probe cycle, where G38.2 and G38.3 is not triggered and G38.4 and G38.5 is "
"triggered."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:134
msgid ""
"Reset while in motion. Grbl cannot guarantee position. Lost steps are "
"likely. Re-homing is highly recommended."
msgstr ""

#: bCNC/ProbePage.py:211
msgid ""
"Set current XY location as autoleveling Z-zero (recalculate probed data to "
"be relative to this XY origin point)"
msgstr ""

#: bCNC/ControlPage.py:251
msgid ""
"Show current state of the machine\n"
"Click to see details\n"
"Right-Click to clear alarm/errors"
msgstr ""
"Muestra el estado actual de la máquina\n"
"Haga click para ver más detalles\n"
"Click derecho para limpiar las alarmas y errores"

#: bCNC/plugins/dragknife.py:91
msgid ""
"Simulation is currently approximated by using lots of short lines. This is "
"the length of these lines."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:110
msgid ""
"Spindle not running when motion commanded in CSS or spindle sync mode. "
"(grblHAL)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:487 bCNC/plugins/endmilloffset.py:496
msgid ""
"Tabs are always ignored. You can select if all islands are active, none, or "
"only selected"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:102
msgid ""
"The G43.1 dynamic tool length offset command cannot apply an offset to an "
"axis other than its configured axis. The Grbl default axis is the Z-axis."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:81
msgid ""
"The G53 G-code command requires either a G0 seek or G1 feed motion mode to "
"be active. A different motion was active."
msgstr ""
"El comando G53 necesita que se encuentre activo un modo de movimiento G0 ó "
"G1, pero había otro diferente."

#: bCNC/controllers/_GenericGRBL.py:90
msgid ""
"The motion command has an invalid target. G2, G3, and G38.2 generates this "
"error, if the arc is impossible to generate or if the probe target is the "
"current position."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:84
msgid ""
"There are unused axis words in the block and G80 motion mode cancel is "
"active."
msgstr ""
"Hay referencias de eje sin usar dentro del bloque, y está activo el comando "
"G80 (Cancel Modal Motion)."

#: bCNC/controllers/_GenericGRBL.py:99
msgid ""
"There are unused, leftover G-code words that aren't used by any command in "
"the block."
msgstr ""
"Hay trozos de g-code que no son usados por ningún comando dentro del bloque."

#: bCNC/Utils.py:709
#, fuzzy
#| msgid ""
#| "There was an error sending the report\n"
#| "Code=%d {}"
msgid ""
"There was an error sending the report\n"
"Code={} {}"
msgstr ""
"Hubo un error al enviar el informe\n"
"Código={} {}"

#: bCNC/plugins/endmilloffset.py:471
msgid ""
"This can be used to switch between Conventional and Climb milling. If unsure "
"use Convetional (default)."
msgstr ""

#: bCNC/plugins/drillmark.py:23
msgid ""
"This plugin is for creating drilling marks with a laser engraver\n"
"        for manual drilling"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:105
msgid ""
"Tool number greater than max supported value or undefined tool selected. "
"(grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:65
msgid ""
"Two G-code commands that both require the use of the XYZ axis words were "
"detected in the block."
msgstr ""

#: bCNC/plugins/dragknife.py:82
msgid ""
"Use this option to simulate cuting of dragknife path. Resulting shape will "
"reflect what shape will actuall be cut. This should reverse the dragknife "
"procedure and give you back the original shape from g-code that was "
"previously processed for dragknife."
msgstr ""

#: bCNC/bmain.py:2722
msgid ""
"\n"
"Would you like open it locally?"
msgstr ""
"\n"
"¿Desea verlo en el navegador?"

#: bCNC/bmain.py:888
msgid ""
"bCNC/\tAn advanced fully featured\n"
"\tg-code sender for GRBL."
msgstr ""
"bCNC/\tInterfaz avanzada para el manejo de\n"
"\tmáquinas CNC basadas en GRBL."

#: bCNC/plugins/dragknife.py:72
msgid ""
"direction that knife blade is facing before and after cut. Eg.: if you set "
"this to X+, then the knifes rotation axis should be on the right side of the "
"tip. Meaning that the knife is ready to cut towards right immediately "
"without pivoting. If you cut multiple shapes in single operation, it's "
"important to have this set consistently across all of them."
msgstr ""

#: bCNC/plugins/arcfit.py:47
msgid ""
"how precisely must line fit. set to 0 to disable line fitting, but at least "
"some line fitting (0.001 to 0.01) might be needed to fix arcs, so they can "
"be fit"
msgstr ""

#: bCNC/plugins/endmilloffset.py:445
msgid ""
"indicates the number of profile passes (single,custom number,full pocket)"
msgstr ""

#: bCNC/ToolsPage.py:783
msgid ""
"positive value = relative to tool diameter (5 to 10 probably makes sense), "
"negative = absolute ramp distance (you probably don't need this). Also note "
"that ramp can't currently be shorter than affected g-code segment."
msgstr ""

#: bCNC/plugins/dragknife.py:63
msgid ""
"retract to this height for pivots (useful for thick materials, you should "
"enter number slightly lower than material thickness)"
msgstr ""

#: bCNC/CNCCanvas.py:2003
#, fuzzy
#| msgid "     line: {}\n"
msgid "     line: {}\n"
msgstr "     línea: {}\n"

#: bCNC/bmain.py:1195 bCNC/bmain.py:1276
msgid "# Blocks:"
msgstr "N⁰ Bloques:"

# I think this might be an typo error in the bCNC code
#: bCNC/TerminalPage.py:182
msgid "$ Display build information of Grbl"
msgstr "$ Mostrar información de compilación de Grbl"

#: bCNC/TerminalPage.py:103
msgid "$# Display parameters of Grbl"
msgstr "$# Mostrar los parámetros de Grbl"

#: bCNC/TerminalPage.py:88
msgid "$$ Display settings of Grbl"
msgstr "$$ Mostrar los ajustes de Grbl"

#: bCNC/ToolsPage.py:1173
msgid "$0 Step pulse time [us]"
msgstr ""

#: bCNC/ToolsPage.py:1174
msgid "$1 Step idle delay [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1180
msgid "$10 Status report [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1195
msgid "$100 X steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1196
msgid "$101 Y steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1197
msgid "$102 Z steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1181
msgid "$11 Junction deviation [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1198
msgid "$110 X max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1199
msgid "$111 Y max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1200
msgid "$112 Z max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1182
msgid "$12 Arc tolerance [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1201
msgid "$120 X acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1202
msgid "$121 Y acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1203
msgid "$122 Z acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1183
msgid "$13 Report inches"
msgstr ""

#: bCNC/ToolsPage.py:1204
msgid "$130 X max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1205
msgid "$131 Y max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1206
msgid "$132 Z max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1207
msgid "$140 X homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1208
msgid "$141 Y homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1209
msgid "$142 Z homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1175
msgid "$2 Step port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1184
msgid "$20 Soft limits"
msgstr ""

#: bCNC/ToolsPage.py:1185
msgid "$21 Hard limits"
msgstr ""

#: bCNC/ToolsPage.py:1186
msgid "$22 Homing cycle"
msgstr ""

#: bCNC/ToolsPage.py:1187
msgid "$23 Homing direction invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1188
msgid "$24 Homing feed [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1189
msgid "$25 Homing seek [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1190
msgid "$26 Homing debounce [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1191
msgid "$27 Homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1176
msgid "$3 Direction port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1192
msgid "$30 Max spindle speed [RPM]"
msgstr ""

#: bCNC/ToolsPage.py:1193
msgid "$31 Min spindle speed [RPM]"
msgstr ""

#: bCNC/ToolsPage.py:1194
msgid "$32 Laser mode enable"
msgstr ""

#: bCNC/ToolsPage.py:1177
msgid "$4 Step enable invert"
msgstr ""

#: bCNC/ToolsPage.py:1178
msgid "$5 Limit pins invert"
msgstr ""

#: bCNC/ToolsPage.py:1179
msgid "$6 Probe pin invert"
msgstr ""

#: bCNC/TerminalPage.py:165
msgid "$C Enable/Disable checking of gcode"
msgstr "$C Habilitar/Deshabilitar comprobación de g-code"

#: bCNC/TerminalPage.py:117
msgid "$G Display state of Grbl"
msgstr "$G Mostrar el estado de Grbl"

#: bCNC/TerminalPage.py:133
msgid "$I Display build information of Grbl"
msgstr "$I Mostrar información de compilación de Grbl"

#: bCNC/TerminalPage.py:148
msgid "$N Display startup configuration of Grbl"
msgstr "$N Mostrar configuración de inicio de Grbl"

#: bCNC/bmain.py:2450
#, fuzzy
#| msgid "'{}' loaded"
msgid "'{}' loaded"
msgstr "'{}' cargado"

#: bCNC/bmain.py:2446
#, fuzzy
msgid "'{}' reloaded at '{}'"
msgstr "'{}' cargado"

#: bCNC/bmain.py:2459
#, fuzzy
#| msgid "'{}' saved"
msgid "'{}' saved"
msgstr "'{}' guardado"

#: bCNC/EditorPage.py:376
msgid "(Un)Comment selected lines"
msgstr "Comentar/Descomentar las líneas seleccionadas"

#: bCNC/controllers/_GenericGRBL.py:120
msgid "(grblHAL internal)"
msgstr ""

#: bCNC/ControlPage.py:1005 bCNC/ControlPage.py:1482
msgid "+"
msgstr ""

#: bCNC/ControlPage.py:1174 bCNC/ControlPage.py:1646
msgid "-"
msgstr ""

#: bCNC/plugins/slicemesh.py:58
msgid ".STL/.PLY file to slice"
msgstr ""

#: bCNC/ProbePage.py:1742
#, fuzzy
msgid "1. Spindle"
msgstr "RPM"

#: bCNC/ProbePage.py:1748
msgid "2. Camera"
msgstr ""

#: bCNC/plugins/slicemesh.py:67
msgid "3D slice (devel)"
msgstr ""

#: bCNC/CNCCanvas.py:2001
#, fuzzy
#| msgid ">>> ERROR: {}\n"
msgid ">>> ERROR: {}\n"
msgstr ">>> ERROR: {}\n"

#: bCNC/ControlPage.py:1407
msgid "A"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:67
msgid "A G-code word was repeated in the block."
msgstr "Una palabra g-code aparece repetida dentro del bloque."

#: bCNC/ControlPage.py:651
#, fuzzy
#| msgid "X work position (click to set)"
msgid "A work position (click to set)"
msgstr "Posición de trabajo X (click para definirla)"

#: bCNC/ControlPage.py:727
msgid "A=0"
msgstr ""

#: bCNC/ControlPage.py:793
msgid "ABC=0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:117
msgid "ATC: current tool is not set. Set current tool with M61. (grblHAL)"
msgstr ""

#: bCNC/FilePage.py:216
msgid "About"
msgstr "Acerca de"

#: bCNC/FilePage.py:224
msgid "About the program"
msgstr "Información sobre el programa"

#: bCNC/bmain.py:847
#, fuzzy
#| msgid "About {}"
msgid "About {} v{}"
msgstr "Acerca de {}"

#: bCNC/ToolsPage.py:641
msgid "Acceleration x"
msgstr "Aceleración X"

#: bCNC/ToolsPage.py:642
msgid "Acceleration y"
msgstr "Aceleración Y"

#: bCNC/ToolsPage.py:643
msgid "Acceleration z"
msgstr "Aceleración Z"

#: bCNC/EditorPage.py:335
msgid "Active"
msgstr "Habilitar"

#: bCNC/plugins/trochoidPath.py:48 bCNC/plugins/trochoidal_3D.py:72
msgid "Adaptative"
msgstr ""

#: bCNC/EditorPage.py:258 bCNC/ProbePage.py:702 bCNC/ToolsPage.py:1472
msgid "Add"
msgstr "Insertar"

#: bCNC/ToolsPage.py:1478
msgid "Add a new operation/object"
msgstr "Añadir una nueva operación/objeto"

#: bCNC/CNCCanvas.py:468
msgid "Add an orientation marker"
msgstr "Añadir un marcador de orientación"

#: bCNC/plugins/zigzag.py:108
#, fuzzy
msgid "Additional length at start/end"
msgstr "Separación adicional"

#: bCNC/plugins/endmilloffset.py:478
#, fuzzy
#| msgid "Additional offset distance"
msgid "Additional offset (mm)"
msgstr "Separación adicional"

#: bCNC/plugins/trochoidPath.py:41 bCNC/plugins/trochoidal_3D.py:65
#: bCNC/ToolsPage.py:1013
msgid "Additional offset distance"
msgstr "Separación adicional"

#: bCNC/ProbePage.py:2111
msgid "After a tool change distance to scan starting from ProbeZ"
msgstr ""
"Distancia a sondear tras un cambio de herramienta (empezando desde ProbeZ)"

#: bCNC/ToolsPage.py:617
msgid "Align Camera"
msgstr ""

#: bCNC/ToolsPage.py:620
msgid "Align Camera Angle"
msgstr ""

#: bCNC/ToolsPage.py:619
msgid "Align Camera Height"
msgstr ""

#: bCNC/ToolsPage.py:618
msgid "Align Camera Width"
msgstr ""

#: bCNC/ProbePage.py:831
msgid "Align GCode with the machine markers"
msgstr "Alinear el G-Code a los marcadores de la máquina"

#: bCNC/EditorPage.py:106 bCNC/bmain.py:140
msgid "All"
msgstr "Todo"

#: bCNC/bmain.py:1235
msgid "All GCode"
msgstr "Todo el g-code"

#: bCNC/bmain.py:119
msgid "All accepted"
msgstr "Se aceptan"

#: bCNC/bmain.py:2609
msgid "Already running"
msgstr "Ya se está ejecutando el programa"

#: bCNC/ToolsPage.py:717
msgid "Angle"
msgstr "Ángulo"

#: bCNC/ProbePage.py:808
msgid "Angle:"
msgstr "Ángulo:"

#: bCNC/plugins/spiral.py:481
msgid "Approach Height (Safe Z)"
msgstr ""

#: bCNC/plugins/arcfit.py:11
msgid "ArcFit"
msgstr ""

#: bCNC/ProbePage.py:956
msgid "At least one probe direction should be specified"
msgstr "Debe especificar al menos una dirección de sondeo"

#: bCNC/EditorPage.py:198 bCNC/ProbePage.py:128 bCNC/ProbePage.py:254
#: bCNC/ProbePage.py:1224
msgid "Autolevel"
msgstr "Auto-nivelado"

#: bCNC/ProbePage.py:135
msgid "Autolevel Z surface"
msgstr ""
"Sondear una superficie para compensar automáticamente variaciones en Z "
"(altura)"

#: bCNC/ProbePage.py:573
msgid "Automatic GOTO after probing"
msgstr ""

#: bCNC/Utils.py:602
msgid "Automatic error reporting"
msgstr "Enviar informes automáticamente"

#: bCNC/plugins/midi2cnc.py:70
msgid "Axis to be used"
msgstr "Ejes a mover"

#: bCNC/ControlPage.py:1491
msgid "B"
msgstr ""

#: bCNC/ControlPage.py:668
#, fuzzy
#| msgid "X work position (click to set)"
msgid "B work position (click to set)"
msgstr "Posición de trabajo X (click para definirla)"

#: bCNC/ControlPage.py:743
msgid "B=0"
msgstr ""

#: bCNC/ControlPage.py:777
msgid "BC=0"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "BEFORE & AFTER probing"
msgstr "ANTES y DESPUÉS del sondeo"

#: bCNC/FilePage.py:316
msgid "Baud:"
msgstr "Tasa (baudios):"

#: bCNC/EditorPage.py:250
#, fuzzy
msgid "Block"
msgstr "Desbloquear"

#: bCNC/controllers/_GenericGRBL.py:126
msgid "Bluetooth initalisation failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/jigsaw.py:362
#, fuzzy
msgid "Board height"
msgstr "Altura de la herramienta medida"

#: bCNC/plugins/jigsaw.py:361
msgid "Board width"
msgstr ""

#: bCNC/EditorPage.py:464
msgid "Bottom"
msgstr "Inferior"

#: bCNC/EditorPage.py:461
#, fuzzy
msgid "Bottom-Left"
msgstr "Inferior"

#: bCNC/EditorPage.py:467
#, fuzzy
msgid "Bottom-Right"
msgstr "Inferior"

#: bCNC/plugins/bowl.py:14
msgid "Bowl"
msgstr "Cuenco"

#: bCNC/plugins/box.py:19
msgid "Box"
msgstr "Caja"

#: bCNC/TerminalPage.py:215
#, fuzzy
msgid "Buffered commands"
msgstr "Enviar los comandos M6"

#: bCNC/TerminalPage.py:126
msgid "Build"
msgstr "Compilación"

#: bCNC/plugins/drillmark.py:49
msgid "Burn power for drillmark"
msgstr ""

#: bCNC/plugins/drillmark.py:48
msgid "Burn time for drillmark"
msgstr ""

#: bCNC/ControlPage.py:1410
#, fuzzy
#| msgid "CW"
msgid "C"
msgstr "+90⁰"

#: bCNC/ControlPage.py:685
#, fuzzy
#| msgid "X work position (click to set)"
msgid "C work position (click to set)"
msgstr "Posición de trabajo X (click para definirla)"

#: bCNC/ControlPage.py:759
msgid "C=0"
msgstr ""

#: bCNC/ToolsPage.py:1522 bCNC/ToolsPage.py:2090
msgid "CAM"
msgstr "Oper. CAM básicas"

#: bCNC/EditorPage.py:596
msgid "CCW"
msgstr "-90⁰"

#: bCNC/ProbePage.py:507
msgid "CIRCLE"
msgstr ""

#: bCNC/ControlPage.py:2363
msgid "CNC communication and control"
msgstr "Controles de la CNC"

#: bCNC/bmain.py:636
msgid "CNC is currently running, please stop it before."
msgstr "La CNC está trabajando, debe detenerla antes"

#: bCNC/ToolsPage.py:926
msgid "CUT selected paths"
msgstr "CORTE de los trazos seleccionados"

#: bCNC/EditorPage.py:566
msgid "CW"
msgstr "+90⁰"

#: bCNC/ProbePage.py:1926 bCNC/ProbePage.py:2132
msgid "Calibrate"
msgstr "Calibrar"

#: bCNC/ProbePage.py:2121
msgid "Calibration:"
msgstr "Calibración:"

#: bCNC/ProbePage.py:142 bCNC/ProbePage.py:1630 bCNC/ToolsPage.py:1870
msgid "Camera"
msgstr ""

#: bCNC/ToolsPage.py:1878
#, fuzzy
msgid "Camera Configuration"
msgstr "Configuración de color"

#: bCNC/ProbePage.py:1701
msgid "Camera cross hair diameter [units]"
msgstr ""

#: bCNC/ProbePage.py:1643
msgid "Camera location inside canvas"
msgstr ""

#: bCNC/ProbePage.py:1718 bCNC/ProbePage.py:1727
msgid "Camera offset from gantry"
msgstr ""

#: bCNC/ProbePage.py:1862
msgid "Camera offset is updated"
msgstr ""

#: bCNC/ProbePage.py:1655
#, fuzzy
msgid "Camera rotation [degrees]"
msgstr "Resolución (grados)"

#: bCNC/ProbePage.py:1688
msgid "Camera scale [pixels / unit]"
msgstr ""

#: bCNC/lib/bFileDialog.py:361 bCNC/Utils.py:876
msgid "Cancel"
msgstr "Cancelar"

#: bCNC/lib/bFileDialog.py:582
#, fuzzy
#| msgid "Cannot access path \"{}\""
msgid "Cannot access path \"{}\""
msgstr "No se puede acceder a la ruta \"{}\""

#: bCNC/plugins/sketch.py:50
msgid "Casual first point"
msgstr ""

#: bCNC/plugins/halftone.py:47
msgid "Cell size"
msgstr ""

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/center.py:12 bCNC/EditorPage.py:463 bCNC/ProbePage.py:646
#: bCNC/ProbePage.py:662
msgid "Center"
msgstr "Centrar"

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/simpleArc.py:65
#, fuzzy
#| msgid "Center"
msgid "Center X"
msgstr "Centrar"

#: bCNC/plugins/function_plot.py:28
msgid "Center X coordinate"
msgstr ""

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/simpleArc.py:66
#, fuzzy
#| msgid "Center"
msgid "Center Y"
msgstr "Centrar"

#: bCNC/plugins/function_plot.py:29
msgid "Center Y coordinate"
msgstr ""

#: bCNC/ProbePage.py:671
msgid "Center probing using a ring"
msgstr "Sondeo de centrado, usando un anillo de calibración"

#: bCNC/ProbePage.py:1944
msgid "Change"
msgstr "Cambiar"

#: bCNC/EditorPage.py:732
msgid "Change cut direction to CCW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:715
msgid "Change cut direction to CW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:682
msgid "Change cut direction to climb for selected gcode blocks"
msgstr ""
"Cambiar la dirección de corte del bloque seleccionado al modo "
"\"climb\" (\"en concordancia\", \"hacia abajo\")"

#: bCNC/EditorPage.py:665
msgid "Change cut direction to conventional for selected gcode blocks"
msgstr ""
"Cambiar la dirección de corte del bloque seleccionado al modo "
"\"convencional\" (\"en oposición\", \"hacia arriba\")"

#: bCNC/ToolsPage.py:1810
msgid "Change program language restart is required"
msgstr "Si cambia el idioma, deberá reiniciar el programa"

#: bCNC/CNCCanvas.py:2250
msgid "Change viewing angle"
msgstr "Tipo de vista (2D/3D)"

#: bCNC/ProbePage.py:2011
msgid "Change:"
msgstr "Pos. cambio:"

#: bCNC/plugins/halftone.py:43 bCNC/plugins/sketch.py:57
msgid "Channel to analyze"
msgstr ""

#: bCNC/Updates.py:86
msgid "Check Interval"
msgstr "Comprobación periódica"

#: bCNC/Updates.py:135
msgid "Check Now"
msgstr "Comprobar ahora"

#: bCNC/FilePage.py:211
msgid "Check Updates"
msgstr "Comprobar actualizaciones"

#: bCNC/TerminalPage.py:157
msgid "Check gcode"
msgstr "Comprobar g-code"

#: bCNC/Updates.py:142
msgid "Check the web site for new versions of bCNC"
msgstr "Comprobar si hay nuevas versiones de bCNC en la página web"

#: bCNC/lib/bFileDialog.py:998
msgid "Choose Directory"
msgstr "Elegir directorio"

#: bCNC/ProbePage.py:530
msgid "Circle radius"
msgstr ""

#: bCNC/plugins/trochoidal.py:46
msgid "Circular"
msgstr ""

#: bCNC/plugins/gear.py:163
msgid "Circular Pitch"
msgstr "Separación entre dientes (circular pitch)"

#: bCNC/ProbePage.py:224 bCNC/ProbePage.py:794 bCNC/TerminalPage.py:45
msgid "Clear"
msgstr "Borrar datos"

#: bCNC/ControlPage.py:479
msgid "Clear Message"
msgstr "Limpiar el mensaje"

#: bCNC/ProbePage.py:230
msgid "Clear probe data"
msgstr "Eliminar los datos de muestreo actuales"

#: bCNC/TerminalPage.py:50
msgid "Clear terminal"
msgstr "Limpiar terminal"

#: bCNC/CNCCanvas.py:440
msgid "Click to set the origin (zero)"
msgstr "Haga click para definir el origen (cero)"

#: bCNC/EditorPage.py:674
msgid "Climb"
msgstr "Concordancia"

#: bCNC/plugins/endmilloffset.py:469
msgid "Climb milling"
msgstr ""

#: bCNC/EditorPage.py:40
msgid "Clipboard"
msgstr "Portapapeles"

#: bCNC/plugins/trochoidal.py:45 bCNC/plugins/trochoidal_3D.py:92
msgid "Clockwise"
msgstr ""

#: bCNC/EditorPage.py:276 bCNC/ToolsPage.py:1489
msgid "Clone"
msgstr "Clonar"

#: bCNC/EditorPage.py:282
msgid "Clone selected lines or blocks [Ctrl-D]"
msgstr "Clonar las líneas o bloques seleccionados [Ctrl-D]"

#: bCNC/ToolsPage.py:1495
msgid "Clone selected operation/object"
msgstr "Duplicar la operación/objeto seleccionado"

#: bCNC/FilePage.py:267 bCNC/Updates.py:126 bCNC/Utils.py:613
#: bCNC/bmain.py:1085 bCNC/bmain.py:1320 bCNC/bmain.py:2554
msgid "Close"
msgstr "Cerrar"

#: bCNC/plugins/text.py:42
msgid "Close Contours"
msgstr ""

#: bCNC/FilePage.py:280
msgid "Close program [Ctrl-Q]"
msgstr "Salir del programa [Ctrl-Q] ó simplemente cierra la ventana"

#: bCNC/plugins/closepath.py:17
msgid "Close the path"
msgstr ""

#: bCNC/plugins/closepath.py:11
msgid "ClosePath"
msgstr ""

#: bCNC/ToolsPage.py:712
msgid "Coating"
msgstr "Recubrimiento"

#: bCNC/CNCList.py:749 bCNC/EditorPage.py:203
#, fuzzy
msgid "Color"
msgstr "Colores"

#: bCNC/ToolsPage.py:1934
msgid "Colors"
msgstr "Colores"

#: bCNC/Utils.py:861 bCNC/bmain.py:215
msgid "Command:"
msgstr "Comando:"

#: bCNC/TerminalPage.py:61
msgid "Commands"
msgstr "Comandos"

#: bCNC/EditorPage.py:370 bCNC/ToolsPage.py:681 bCNC/ToolsPage.py:708
#: bCNC/ToolsPage.py:738
msgid "Comment"
msgstr "Comentar"

#: bCNC/ProbePage.py:295
#, fuzzy
msgid "Common"
msgstr "Comentar"

#: bCNC/ToolsPage.py:822
msgid "Compensate islands for cutter radius"
msgstr ""

#: bCNC/ToolsPage.py:1795 bCNC/ToolsPage.py:1821
msgid "Config"
msgstr "Configuración"

#: bCNC/FilePage.py:348
msgid "Connect on startup"
msgstr "Conectar automáticamente"

#: bCNC/FilePage.py:352
msgid "Connect to serial on startup of the program"
msgstr "Conectar automáticamente cuando se inicie bCNC"

#: bCNC/ControlPage.py:82 bCNC/ControlPage.py:122
msgid "Connection"
msgstr "Conexión"

#: bCNC/controllers/_GenericGRBL.py:31
msgid "Connection is established with Grbl"
msgstr "Hay conexión con Grbl"

#: bCNC/ControlPage.py:927 bCNC/ControlPage.py:2364
msgid "Control"
msgstr "Control"

#: bCNC/ToolsPage.py:591 bCNC/ToolsPage.py:592 bCNC/ToolsPage.py:593
#: bCNC/ToolsPage.py:594 bCNC/ToolsPage.py:595 bCNC/ToolsPage.py:596
#: bCNC/ToolsPage.py:597 bCNC/ToolsPage.py:598 bCNC/ToolsPage.py:599
#: bCNC/ToolsPage.py:600 bCNC/ToolsPage.py:601 bCNC/ToolsPage.py:602
#, fuzzy
msgid "Control-"
msgstr "Control"

#: bCNC/ToolsPage.py:1854
#, fuzzy
msgid "Controller"
msgstr "Placa controladora:"

#: bCNC/ToolsPage.py:1862
#, fuzzy
msgid "Controller (GRBL) configuration"
msgstr "Configuración de color"

#: bCNC/bmain.py:202
#, fuzzy
msgid "Controller buffer fill"
msgstr "Placa controladora:"

#: bCNC/FilePage.py:330
msgid "Controller:"
msgstr "Placa controladora:"

#: bCNC/EditorPage.py:655
msgid "Conventional"
msgstr "Convencional"

#: bCNC/ControlPage.py:2149
msgid "Coolant:"
msgstr ""

#: bCNC/EditorPage.py:80
msgid "Copy"
msgstr "Copiar"

#: bCNC/EditorPage.py:86
msgid "Copy [Ctrl-C]"
msgstr "Copiar [Ctrl-C]"

#: bCNC/plugins/simpleRectangle.py:96
#, fuzzy
#| msgid "Internal Radius"
msgid "Corner Radius"
msgstr "Radio interno"

#: bCNC/plugins/zigzag.py:110
msgid "Corner resolution"
msgstr ""

#: bCNC/plugins/simpleArc.py:89
#, fuzzy
#| msgid "Create Spur GEAR"
msgid "Create Simple Arc"
msgstr "Crear ENGRANAJE"

#: bCNC/plugins/simpleDrill.py:104
msgid "Create Simple Drill"
msgstr ""

#: bCNC/plugins/simpleLine.py:73
msgid "Create Simple Line"
msgstr ""

#: bCNC/plugins/simpleRectangle.py:118
msgid "Create Simple Rectangle"
msgstr ""

#: bCNC/plugins/gear.py:178
msgid "Create Spur GEAR"
msgstr "Crear ENGRANAJE"

#: bCNC/plugins/hilbert.py:104
#, fuzzy
msgid "Create a Hilbert path"
msgstr "Crear una ruta de espirógrafo"

#: bCNC/plugins/zigzag.py:98
#, fuzzy
msgid "Create a Zig-Zag path"
msgstr "Crear una ruta de espirógrafo"

#: bCNC/plugins/spirograph.py:111
msgid "Create a spirograph path"
msgstr "Crear una ruta de espirógrafo"

#: bCNC/plugins/trochoidPath.py:29
msgid "Create a trochoid rute along selected blocks"
msgstr ""

#: bCNC/plugins/pyrograph.py:29
msgid "Create a variable feed path based upon image brightness"
msgstr ""
"Genera una ruta de velocidad variable según el brillo de una imagen (pensado "
"para el pirograbado)"

#: bCNC/plugins/box.py:393
msgid "Create finger BOX"
msgstr "Crear CAJA ensamblable"

#: bCNC/plugins/halftone.py:29
msgid "Create halftone pattern from a picture"
msgstr ""

#: bCNC/plugins/driller.py:36
msgid "Create holes along selected blocks"
msgstr "Crear agujeros a lo largo de la ruta seleccionada"

#: bCNC/plugins/sketch.py:28
#, fuzzy
msgid "Create sketch based on picture brightness"
msgstr ""
"Genera una ruta de velocidad variable según el brillo de una imagen (pensado "
"para el pirograbado)"

#: bCNC/ToolsPage.py:1162
msgid "Create tabs on blocks"
msgstr "Crear pestañas en los bloques de g-code"

#: bCNC/plugins/text.py:29
msgid "Create text using a ttf font"
msgstr "Crear texto usando una fuente .TTF"

#: bCNC/plugins/trochoidal_3D.py:38
#, fuzzy
#| msgid "Create holes along selected blocks"
msgid "Create trochoids along selected blocks"
msgstr "Crear agujeros a lo largo de la ruta seleccionada"

#: bCNC/ProbePage.py:1692
msgid "Crosshair:"
msgstr ""

#: bCNC/plugins/endmilloffset.py:452
msgid "Custom offset count"
msgstr ""

#: bCNC/plugins/box.py:356 bCNC/EditorPage.py:64 bCNC/ToolsPage.py:1530
msgid "Cut"
msgstr "Cortar"

#: bCNC/plugins/heightmap.py:75
msgid "Cut Border"
msgstr "Repasar los lados con cortes rectos"

#: bCNC/EditorPage.py:724
msgid "Cut CCW"
msgstr ""

#: bCNC/EditorPage.py:707
msgid "Cut CW"
msgstr ""

#: bCNC/plugins/trochoidPath.py:38
#, fuzzy
#| msgid "Diameter"
msgid "Cut Diameter"
msgstr "Diámetro"

#: bCNC/plugins/flatten.py:298
msgid "Cut Direction"
msgstr "Dirección de corte"

#: bCNC/plugins/spiral.py:482
msgid "Cut Pattern"
msgstr ""

#: bCNC/plugins/heightmap.py:74
msgid "Cut Top"
msgstr "Desbastar superficie (nivela la primera capa)"

#: bCNC/EditorPage.py:70
msgid "Cut [Ctrl-X]"
msgstr "Cortar [Ctrl-X]"

#: bCNC/ToolsPage.py:829
msgid "Cut contours of selected islands"
msgstr ""

#: bCNC/ToolsPage.py:1539
msgid "Cut for the full stock thickness selected code"
msgstr ""
"Corta la ruta seleccionada el grueso total especificado en \"sustrato\""

#: bCNC/plugins/spiral.py:483
#, fuzzy
#| msgid "Cut Direction"
msgid "Cut in Both Directions"
msgstr "Dirección de corte"

#: bCNC/ToolsPage.py:775
msgid "Cutting strategy"
msgstr ""

#: bCNC/ToolsPage.py:987
msgid "DRILL selected points"
msgstr "TALADRADO de los puntos seleccionados"

#: bCNC/ToolsPage.py:655
msgid "DRO Zero padding"
msgstr ""

#: bCNC/ToolsPage.py:1397
msgid "Database"
msgstr "Datos de herram. y material"

#: bCNC/lib/bFileDialog.py:307
msgid "Date"
msgstr "Fecha"

#: bCNC/Updates.py:101
#, fuzzy
#| msgid "Date of last checking"
msgid "Date last checked"
msgstr "Fecha de última comprobación"

#: bCNC/Updates.py:119
msgid "Days-interval to remind again for checking"
msgstr "Intervalo (en días) para mostrar el recordatorio"

#: bCNC/ToolsPage.py:650
msgid "Decimal digits"
msgstr "N⁰ decimales"

#: bCNC/ControlPage.py:1176 bCNC/ControlPage.py:1648
msgid "Decrease step by 1 unit"
msgstr "Disminuir el paso 1 unidad"

#: bCNC/EditorPage.py:292 bCNC/ProbePage.py:750 bCNC/ToolsPage.py:1506
msgid "Delete"
msgstr "Eliminar"

#: bCNC/ProbePage.py:803 bCNC/ProbePage.py:1061
msgid "Delete all markers"
msgstr "Eliminar todos los marcadores"

#: bCNC/ProbePage.py:1497
msgid "Delete autolevel information"
msgstr "Eliminar los datos de auto-nivelado"

#: bCNC/ProbePage.py:759
msgid "Delete current marker"
msgstr "Eliminar el marcador actual"

#: bCNC/EditorPage.py:298
msgid "Delete selected lines or blocks [Del]"
msgstr "Eliminar las líneas o bloques seleccionados [Del]"

#: bCNC/ToolsPage.py:1512
msgid "Delete selected operation/object"
msgstr "Eliminar la operación/objeto seleccionado"

#: bCNC/plugins/hilbert.py:114 bCNC/plugins/zigzag.py:111
#, fuzzy
msgid "Depth"
msgstr "Profundidad Dy"

#: bCNC/plugins/box.py:349
msgid "Depth Dy"
msgstr "Profundidad Dy"

#: bCNC/plugins/trochoidPath.py:55 bCNC/plugins/trochoidal_3D.py:89
#: bCNC/ToolsPage.py:684 bCNC/ToolsPage.py:768
msgid "Depth Increment"
msgstr "Profundidad de pasada"

#: bCNC/plugins/spiral.py:478
#, fuzzy
#| msgid "Depth to flatten"
msgid "Depth to Reduce"
msgstr "Profundidad Z de aplanado"

#: bCNC/plugins/flatten.py:293
msgid "Depth to flatten"
msgstr "Profundidad Z de aplanado"

#: bCNC/plugins/bowl.py:113 bCNC/ToolsPage.py:713
msgid "Diameter"
msgstr "Diámetro"

#: bCNC/plugins/Helical_Descent.py:83
#, fuzzy
msgid "Diameter Cut"
msgstr "Diámetro"

#: bCNC/ProbePage.py:649
msgid "Diameter:"
msgstr "Diámetro:"

#: bCNC/plugins/difference.py:18
msgid "Difference"
msgstr ""

#: bCNC/plugins/jigsaw.py:365
#, fuzzy
msgid "Difference between pieces"
msgstr "Distancia entre agujeros"

#: bCNC/plugins/difference.py:24
msgid "Difference of two shapes"
msgstr ""

#: bCNC/plugins/trochoidPath.py:40 bCNC/plugins/trochoidal_3D.py:60
#: bCNC/plugins/pyrograph.py:46 bCNC/ToolsPage.py:1010
msgid "Direction"
msgstr "Dirección"

#: bCNC/bmain.py:1584
msgid "Direction command error"
msgstr "Error en el comando de dirección"

#: bCNC/lib/bFileDialog.py:275
msgid "Directory:"
msgstr "Directorio:"

#: bCNC/EditorPage.py:327
msgid "Disable"
msgstr "No"

#: bCNC/ToolsPage.py:942
#, fuzzy
msgid "Distance (mm)"
msgstr "Desplazamiento:"

#: bCNC/ControlPage.py:1921
msgid "Distance Mode [G90,G91]"
msgstr "Modo de desplazamiento [G90,G91]"

#: bCNC/plugins/driller.py:45
msgid "Distance between holes"
msgstr "Distancia entre agujeros"

#: bCNC/ControlPage.py:1910 bCNC/ProbePage.py:2103
msgid "Distance:"
msgstr "Desplazamiento:"

#: bCNC/ControlPage.py:1170 bCNC/ControlPage.py:1642
msgid "Divide step by 10"
msgstr "Dividir el paso por 10"

#: bCNC/ProbePage.py:1498
msgid "Do you want to delete all autolevel in formation?"
msgstr "¿Desea eliminar todos los datos de auto-nivelado?"

#: bCNC/ProbePage.py:1062
msgid "Do you want to delete all orientation markers?"
msgstr "¿Desea eliminar todos los marcadores de orientación?"

#: bCNC/controllers/_GenericGRBL.py:162
msgid "Door closed. Ready to resume."
msgstr ""

#: bCNC/ToolsPage.py:639
msgid "Double Size Icon"
msgstr ""

#: bCNC/EditorPage.py:524
msgid "Down"
msgstr "Bajar"

#: bCNC/Updates.py:182
msgid "Download"
msgstr "Descargar"

#: bCNC/CNCCanvas.py:463
msgid "Drag a ruler to measure distances"
msgstr "Haga click y arrastre para tomar medidas"

#: bCNC/plugins/dragknife.py:25
msgid "Drag knife postprocessor"
msgstr ""

#: bCNC/plugins/dragknife.py:19
msgid "DragKnife"
msgstr ""

#: bCNC/plugins/halftone.py:51 bCNC/plugins/sketch.py:49
#: bCNC/plugins/pyrograph.py:47
msgid "Draw border"
msgstr "Dibujar marco"

#: bCNC/plugins/function_plot.py:35
msgid "Draw coordinate system?"
msgstr ""

#: bCNC/CNCCanvas.py:2432
msgid "Draw timeout in seconds"
msgstr ""

#: bCNC/CNCCanvas.py:2335
msgid "Draw:"
msgstr "Mostrar:"

#: bCNC/ToolsPage.py:1621
msgid "Drill"
msgstr "Taladrar"

#: bCNC/ToolsPage.py:938
msgid "Drill in center only"
msgstr ""

#: bCNC/plugins/drillmark.py:44
msgid "Drill mark size"
msgstr ""

#: bCNC/plugins/driller.py:20
msgid "Driller"
msgstr "Taladrador"

#: bCNC/plugins/driller.py:276
msgid "Driller abort: Distance must be > 0"
msgstr "Error: La distancia entre taladros debe ser positiva"

#: bCNC/plugins/driller.py:285
msgid "Driller abort: Dwell time >= 0, here time runs only forward!"
msgstr ""
"Error: El tiempo de espera debe ser positivo (o cero) ¡Aquí el tiempo sólo "
"avanza!"

#: bCNC/plugins/driller.py:293
msgid "Driller abort: Excellon-File not a file"
msgstr ""

#: bCNC/plugins/driller.py:280
msgid "Driller abort: Peck must be >= 0"
msgstr "Error: La Prof. por pasada debe ser positiva (o cero)"

#: bCNC/plugins/driller.py:303
msgid "Driller abort: Please select some path"
msgstr "Error: Debe seleccionar algún trazo sobre el que generar el taladrado"

#: bCNC/plugins/Helical_Descent.py:86
msgid "Drop by lap"
msgstr ""

#: bCNC/ToolsPage.py:941
msgid "Dwell (s)"
msgstr ""

#: bCNC/plugins/simpleDrill.py:71
#, fuzzy
#| msgid "Dwell time, 0 means None"
msgid "Dwell time (s)"
msgstr "Tiempo de espera"

#: bCNC/plugins/driller.py:48
msgid "Dwell time, 0 means None"
msgstr "Tiempo de espera"

#: bCNC/controllers/_GenericGRBL.py:41
msgid "EEPROM read failed. Reset and restored to default values."
msgstr ""

#: bCNC/CNCCanvas.py:525
msgid "ERROR: Cannot set X-Y marker  with the current view"
msgstr "ERROR: No se pudo establecer el marcador X-Y en la vista actual"

#: bCNC/controllers/_GenericGRBL.py:150
msgid "EStop asserted. Clear and reset (grblHAL)"
msgstr ""

#: bCNC/ProbePage.py:1560
#, fuzzy
msgid "Edge Detection"
msgstr "Dirección"

#: bCNC/EditorPage.py:194
msgid "Edit"
msgstr "Editar"

#: bCNC/ToolsPage.py:1461
msgid "Edit name of current operation/object"
msgstr "Cambiar el nombre de la operación/objeto seleccionado"

#: bCNC/ToolsPage.py:1445
msgid "Editable database of EndMills properties"
msgstr "Base de datos (editable) de herramientas y sus propiedades"

#: bCNC/ToolsPage.py:1429
msgid "Editable database of material properties"
msgstr "Base de datos (editable) de materiales y sus propiedades"

#: bCNC/EditorPage.py:804
msgid "Editor"
msgstr "Editor"

#: bCNC/controllers/_GenericGRBL.py:119
msgid "Emergency stop active. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2657
msgid "Empty gcode"
msgstr "El g-code está vacío"

#: bCNC/EditorPage.py:322
msgid "Enable"
msgstr "Sí"

#: bCNC/ToolsPage.py:640
msgid "Enable 6 Axis Displays"
msgstr ""

#: bCNC/EditorPage.py:342
msgid "Enable or disable blocks of gcode"
msgstr "Habilitar/Deshabilitar bloques de código"

#: bCNC/bmain.py:1154
msgid "Enabled GCode"
msgstr "G-code habilitado"

#: bCNC/plugins/simpleArc.py:69
msgid "End Angle in Degrees "
msgstr ""

#: bCNC/plugins/Helical_Descent.py:84 bCNC/plugins/endmilloffset.py:438
#: bCNC/plugins/trochoidPath.py:43 bCNC/plugins/trochoidal_3D.py:48
#: bCNC/ToolsPage.py:1003 bCNC/ToolsPage.py:1074 bCNC/ToolsPage.py:1437
msgid "End Mill"
msgstr "Herramienta"

#: bCNC/plugins/Helical_Descent.py:100
msgid "End in the Deep"
msgstr ""

#: bCNC/bmain.py:1953
#, fuzzy
#| msgid "EndMill: {} %g"
msgid "EndMill: {} {}"
msgstr "Herramienta: {} {:g}"

#: bCNC/plugins/Helical_Descent.py:96
msgid "Entry and Exit"
msgstr ""

#: bCNC/lib/bFileDialog.py:582 bCNC/lib/bFileDialog.py:655
#: bCNC/lib/bFileDialog.py:879 bCNC/lib/bFileDialog.py:907
#: bCNC/lib/bFileDialog.py:931
msgid "Error"
msgstr "Error"

#: bCNC/Utils.py:565
msgid "Error Reporting"
msgstr "Notificar de un error"

#: bCNC/lib/bFileDialog.py:880
#, fuzzy
#| msgid "Error creating folder \"{}\""
msgid "Error creating folder \"{}\""
msgstr "Error al crear la carpeta \"{}\""

#: bCNC/lib/bFileDialog.py:931
#, fuzzy
#| msgid "Error deleting file \"{}\""
msgid "Error deleting file \"{}\""
msgstr "Error al borrar el fichero \"{}\""

#: bCNC/lib/bFileDialog.py:655
#, fuzzy
#| msgid "Error listing folder \"{}\""
msgid "Error listing folder \"{}\""
msgstr "Error al listar el directorio \"{}\""

#: bCNC/bmain.py:2568
msgid "Error opening serial"
msgstr "Error al abrir el puerto serie"

#: bCNC/lib/bFileDialog.py:908
#, fuzzy
#| msgid "Error renaming \"{}\" to \"{}\""
msgid "Error renaming \"{}\" to \"{}\""
msgstr "Error al renombrar \"{}\" a \"{}\""

#: bCNC/Utils.py:694 bCNC/Utils.py:708
msgid "Error sending report"
msgstr "Error al enviar el informe"

#: bCNC/Updates.py:193
#, fuzzy
#| msgid "Error %d in connection"
msgid "Error {} in connection"
msgstr "Error {} en la conexión"

#: bCNC/ProbePage.py:853
msgid "Error:"
msgstr "Error:"

#: bCNC/plugins/bowl.py:136
msgid "Error: Check the Bowl and End Mill parameters"
msgstr ""
"Error: Comprobar los parámetros del Cuenco y la Herramienta seleccionada"

#: bCNC/plugins/jigsaw.py:406
msgid "Error: Check the parameters and your endmill config"
msgstr ""

#: bCNC/plugins/midi2cnc.py:155
msgid "Error: Sorry can't parse the Midi file."
msgstr "Error: No se puede leer el fichero Midi"

#: bCNC/plugins/midi2cnc.py:116
msgid "Error: This plugin requires midiparser.py"
msgstr "Error: Éste generador necesita midiparser.py"

#: bCNC/bmain.py:1487
msgid "Evaluation error"
msgstr "Error en la evaluación"

#: bCNC/plugins/trochoidal.py:47
msgid "Even spacing across segment"
msgstr ""

#: bCNC/ToolsPage.py:1927
msgid "Events"
msgstr ""

#: bCNC/plugins/driller.py:50
msgid "Excellon-File"
msgstr ""

#: bCNC/ToolsPage.py:1967
msgid "Execute"
msgstr "Ejecutar"

#: bCNC/bmain.py:2417
msgid "Existing Autolevel"
msgstr "Hay cargados datos de auto-nivelado"

#: bCNC/FilePage.py:272
msgid "Exit"
msgstr "Salir"

#: bCNC/ToolsPage.py:805
msgid "Exit strategy (usefull for threads)"
msgstr ""

#: bCNC/EditorPage.py:351
msgid "Expand"
msgstr "Expandir"

#: bCNC/plugins/spirograph.py:119
msgid "External Radius"
msgstr "Radio externo"

#: bCNC/ToolsPage.py:567 bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:591
msgid "F1"
msgstr ""

#: bCNC/ToolsPage.py:576 bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:600
msgid "F10"
msgstr ""

#: bCNC/ToolsPage.py:577 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:601
msgid "F11"
msgstr ""

#: bCNC/ToolsPage.py:578 bCNC/ToolsPage.py:590 bCNC/ToolsPage.py:602
msgid "F12"
msgstr ""

#: bCNC/ToolsPage.py:568 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:592
msgid "F2"
msgstr ""

#: bCNC/ToolsPage.py:569 bCNC/ToolsPage.py:581 bCNC/ToolsPage.py:593
msgid "F3"
msgstr ""

#: bCNC/ToolsPage.py:570 bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:594
msgid "F4"
msgstr ""

#: bCNC/ToolsPage.py:571 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:595
msgid "F5"
msgstr ""

#: bCNC/ToolsPage.py:572 bCNC/ToolsPage.py:584 bCNC/ToolsPage.py:596
msgid "F6"
msgstr ""

#: bCNC/ToolsPage.py:573 bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:597
msgid "F7"
msgstr ""

#: bCNC/ToolsPage.py:574 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:598
msgid "F8"
msgstr ""

#: bCNC/ToolsPage.py:575 bCNC/ToolsPage.py:587 bCNC/ToolsPage.py:599
msgid "F9"
msgstr ""

#: bCNC/ProbePage.py:485
msgid "FEED"
msgstr ""

#: bCNC/ProbePage.py:518
msgid "FINISH"
msgstr ""

#: bCNC/plugins/sketch.py:47
msgid "Fading force"
msgstr ""

#: bCNC/ProbePage.py:306
#, fuzzy
msgid "Fast Probe Feed:"
msgstr "Velocidad de sondeo:"

#: bCNC/plugins/scaling.py:43 bCNC/plugins/trochoidal_3D.py:95
#: bCNC/ToolsPage.py:682 bCNC/ToolsPage.py:769
msgid "Feed"
msgstr "Velocidad"

#: bCNC/ControlPage.py:2021
msgid "Feed Mode [G93, G94, G95]"
msgstr "Modo de avance (feed mode) [G93, G94, G95]"

#: bCNC/ControlPage.py:1999
msgid "Feed Rate [F#]"
msgstr "Velocidad de avance (feed rate) [F#]"

#: bCNC/plugins/trochoidal_3D.py:411
#, fuzzy
msgid "Feed has to be greater than 0"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/ControlPage.py:487
msgid "Feed hold"
msgstr "Pausar trabajo"

#: bCNC/ToolsPage.py:644
msgid "Feed max x"
msgstr "Vel. máx X"

#: bCNC/ToolsPage.py:645
msgid "Feed max y"
msgstr "Vel. máx Y"

#: bCNC/ToolsPage.py:646
msgid "Feed max z"
msgstr "Vel. máx Z"

#: bCNC/plugins/driller.py:56
#, fuzzy
#| msgid "Feed max x"
msgid "Feed rapid G0"
msgstr "Vel. máx X"

#: bCNC/controllers/_GenericGRBL.py:62
#, fuzzy
msgid "Feed rate has not yet been set or is undefined."
msgstr "Error: Dirección de corte no definida"

#: bCNC/ControlPage.py:1987
msgid "Feed:"
msgstr "Feed:"

#: bCNC/plugins/trochoidal.py:53
msgid "Feedrate"
msgstr ""

#: bCNC/FilePage.py:75 bCNC/FilePage.py:464
msgid "File"
msgstr "Inicio"

#: bCNC/FilePage.py:463
msgid "File I/O and configuration"
msgstr "Menú de conexión"

#: bCNC/lib/bFileDialog.py:951 bCNC/lib/bFileDialog.py:962
#, fuzzy
#| msgid "File \"{}\" does not exist"
msgid "File \"{}\" does not exist"
msgstr "No existe el fichero \"{}\""

#: bCNC/lib/bFileDialog.py:984
msgid "File already exists"
msgstr "Ya existe ese fichero"

#: bCNC/lib/bFileDialog.py:950 bCNC/lib/bFileDialog.py:961
msgid "File does not exist"
msgstr "No existe el fichero"

#: bCNC/bmain.py:2378
msgid "File modified"
msgstr "Documento modificado"

#: bCNC/lib/bFileDialog.py:332
msgid "File name:"
msgstr "Nombre:"

#: bCNC/lib/bFileDialog.py:340
msgid "Files of type:"
msgstr "Tipo de fichero:"

#: bCNC/EditorPage.py:167
msgid "Filter"
msgstr ""

#: bCNC/EditorPage.py:175
#, fuzzy
msgid "Filter blocks"
msgstr "Generado: REPLICACIÓN"

#: bCNC/plugins/Helical_Descent.py:87
#, fuzzy
msgid "Final Depth"
msgstr "Profundidad de trabajo"

#: bCNC/plugins/center.py:18
msgid "Find center of bounding box"
msgstr ""

#: bCNC/plugins/box.py:351
msgid "Fingers Nx"
msgstr "Pestañas Nx"

#: bCNC/plugins/box.py:352
msgid "Fingers Ny"
msgstr "Pestañas Ny"

#: bCNC/plugins/box.py:353
msgid "Fingers Nz"
msgstr "Pestañas Nz"

#: bCNC/ProbePage.py:1214
msgid "Finished recording"
msgstr ""

#: bCNC/ToolsPage.py:790
msgid "First cut at surface height"
msgstr "Desbastar superficie (nivela la primera capa)"

#: bCNC/CNCCanvas.py:2270
msgid "Fit to screen [F]"
msgstr "Ajustar a la pantalla [F]"

#: bCNC/plugins/flatpath.py:12
msgid "FlatPath"
msgstr ""

#: bCNC/plugins/flatten.py:13
msgid "Flatten"
msgstr "Aplanar"

#: bCNC/plugins/flatten.py:43
msgid "Flatten abort: Cut Direction is undefined"
msgstr "Error: Dirección de corte no definida"

#: bCNC/plugins/flatten.py:52
msgid "Flatten abort: Flatten Area dimensions must be > 0"
msgstr "Error: Las dimensiones del aplanado deben ser positivas"

#: bCNC/plugins/flatten.py:98
msgid "Flatten abort: Flatten area is too small for this End Mill."
msgstr ""
"Error: El área a aplanar es demasiado pequeña para la herramienta "
"seleccionada"

#: bCNC/plugins/flatten.py:58
msgid "Flatten abort: Hey this is only for subtractive machine! Check depth!"
msgstr "Error: Compruebe la profundidad de aplanado, debe ser negativa"

#: bCNC/plugins/flatten.py:47
msgid "Flatten abort: Pocket Type is undefined"
msgstr "Error: Tipo de excavación no definida"

#: bCNC/plugins/flatten.py:281
msgid "Flatten an area in different ways"
msgstr ""
"Nivela una superficie, aplanándola o alisándola de varios modos distintos"

#: bCNC/plugins/flatpath.py:18
msgid "Flatten the path"
msgstr ""

#: bCNC/plugins/flatten.py:328
msgid "Flatten: Generated flatten surface"
msgstr "Generado: APLANADO"

#: bCNC/EditorPage.py:581
msgid "Flip"
msgstr "180⁰"

#: bCNC/plugins/slicemesh.py:68
msgid "Flip upwards"
msgstr ""

#: bCNC/ControlPage.py:2168
msgid "Flood"
msgstr ""

#: bCNC/ToolsPage.py:715
msgid "Flutes"
msgstr "N⁰ de cortes"

#: bCNC/plugins/text.py:41
msgid "Font file"
msgstr "Fichero de fuente (.ttf)"

#: bCNC/plugins/text.py:40
msgid "Font size"
msgstr "Tamaño de fuente"

#: bCNC/ToolsPage.py:1941
msgid "Fonts"
msgstr "Fuente"

#: bCNC/ToolsPage.py:657
msgid "Footer gcode"
msgstr "Final de g-code"

#: bCNC/plugins/function_plot.py:24
msgid "Formula"
msgstr ""

#: bCNC/ProbePage.py:1575
msgid "Freeze"
msgstr ""

#: bCNC/plugins/function_plot.py:8
#, fuzzy
#| msgid "Connection"
msgid "Function"
msgstr "Conexión"

#: bCNC/bmain.py:133 bCNC/bmain.py:2487
msgid "G-Code"
msgstr "G-Code"

#: bCNC/plugins/arcfit.py:17
msgid "G-Code arc-fit"
msgstr ""

#: bCNC/bmain.py:134
#, fuzzy
msgid "G-Code clean"
msgstr "G-Code"

#: bCNC/plugins/linearize.py:17
msgid "G-Code linearizer"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:63
msgid "G-code command in block requires an integer value."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:108
msgid "G-code command not allowed when tool change is pending. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:45
msgid "G-code locked out during alarm or jog state"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:35
msgid "G-code words consist of a letter and a value. Letter was not found."
msgstr ""

#: bCNC/ProbePage.py:59
msgid "G38.2 stop on contact else error"
msgstr "G38.2 parar al tocar, ó error si no toca"

#: bCNC/ProbePage.py:60
msgid "G38.3 stop on contact"
msgstr "G38.3 parar al tocar"

#: bCNC/ProbePage.py:61
msgid "G38.4 stop on loss contact else error"
msgstr "G38.4 parar al dejar de tocar, ó error si no toca"

#: bCNC/ProbePage.py:62
msgid "G38.5 stop on loss contact"
msgstr "G38.5 parar al dejar de tocar"

#: bCNC/ControlPage.py:2051
msgid "G92:"
msgstr ""

#: bCNC/ProbePage.py:733
msgid "GCode X coordinate of orientation point"
msgstr "Coordenada X (G-Code) del punto de orientación"

#: bCNC/ProbePage.py:744
msgid "GCode Y coordinate of orientation point"
msgstr "Coordenada Y (G-Code) del punto de orientación"

#: bCNC/EditorPage.py:803
msgid "GCode editor"
msgstr "Editor de g-code"

#: bCNC/ToolsPage.py:2089
msgid "GCode manipulation tools and user plugins"
msgstr "Herramientas de creación y edición de g-code"

#: bCNC/bmain.py:2379
msgid "Gcode was modified do you want to save it first?"
msgstr "El g-code ha sido modificado ¿Desea guardarlo antes?"

#: bCNC/ProbePage.py:723
#, fuzzy
msgid "Gcode:"
msgstr "Modo:"

#: bCNC/plugins/gear.py:16
msgid "Gear"
msgstr "Engranaje"

#: bCNC/plugins/bowl.py:105
msgid "Generate a bowl cavity"
msgstr "Generar una cavidad esférica"

#: bCNC/plugins/box.py:339
msgid "Generate a finger box"
msgstr "Generar una caja ensamblable"

#: bCNC/plugins/endmilloffset.py:430
msgid "Generate a pocket or profile for selected shape (regarding islands)"
msgstr ""

#: bCNC/plugins/simpleArc.py:58
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Arc"
msgstr "Generar un engranaje ó rueda dentada"

#: bCNC/plugins/simpleDrill.py:60
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Drill"
msgstr "Generar un engranaje ó rueda dentada"

#: bCNC/plugins/simpleLine.py:44
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple line"
msgstr "Generar un engranaje ó rueda dentada"

#: bCNC/plugins/simpleRectangle.py:85
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple rectangle"
msgstr "Generar un engranaje ó rueda dentada"

#: bCNC/plugins/gear.py:153
msgid "Generate a spur gear"
msgstr "Generar un engranaje ó rueda dentada"

#: bCNC/plugins/halftone.py:53
#, fuzzy
msgid "Generate for conical end mill"
msgstr "Generar ruta de perfilado"

#: bCNC/plugins/endmilloffset.py:175 bCNC/plugins/endmilloffset.py:183
#: bCNC/plugins/endmilloffset.py:191 bCNC/plugins/endmilloffset.py:199
#: bCNC/plugins/endmilloffset.py:207 bCNC/plugins/endmilloffset.py:215
#: bCNC/plugins/endmilloffset.py:223 bCNC/plugins/endmilloffset.py:573
#: bCNC/ToolsPage.py:1088
msgid "Generate pocket path"
msgstr "Generar ruta de ahuecado (cavidad, pocket)"

#: bCNC/ToolsPage.py:1062
msgid "Generate profile path"
msgstr "Generar ruta de perfilado"

#: bCNC/plugins/Random.py:22 bCNC/plugins/tile.py:20
msgid "Generate replicas of selected code"
msgstr "Generar múltiples copias del código seleccionado"

#: bCNC/plugins/driller.py:459
#, fuzzy
#| msgid "Generated Driller: %d holes"
msgid "Generated Driller: {} holes"
msgstr "Generado: TALADRADOR ({} agujeros)"

#: bCNC/plugins/halftone.py:285
msgid "Generated Halftone size W={} x H={} x D={}, Total points: {}"
msgstr ""

#: bCNC/plugins/heightmap.py:408
#, fuzzy
#| msgid "Generated Heightmap %d x %d x %d "
msgid "Generated Heightmap {} x {} x {}"
msgstr "Generado: RELIEVE de {} x {} x {}  "

#: bCNC/plugins/midi2cnc.py:353
msgid "Generated Midi2CNC, ready to play?"
msgstr "Generado: MIDI2CNC. ¡Música, maestro!"

#: bCNC/plugins/pyrograph.py:213
#, fuzzy
#| msgid "Generated Pyrograph W=%g x H=%g x D=%g"
msgid "Generated Pyrograph W={:g} x H={:g} x D={:g}"
msgstr "Generado: PIROGRABADO W={:g} x H={:g} x D={:g}"

#: bCNC/plugins/function_plot.py:273
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated function graph"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/trochoidPath.py:110
#, fuzzy
msgid "Generated path for trochoidal cutting"
msgstr "Generar ruta de perfilado"

#: bCNC/plugins/arcfit.py:102
msgid "Generated: Arc fit"
msgstr ""

#: bCNC/plugins/bowl.py:134
msgid "Generated: BOWL"
msgstr "Generado: CUENCO"

#: bCNC/plugins/box.py:395
msgid "Generated: BOX with fingers"
msgstr "Generada: CAJA ensamblable"

#: bCNC/plugins/center.py:70
msgid "Generated: Center"
msgstr ""

#: bCNC/plugins/closepath.py:52
msgid "Generated: Closepath"
msgstr ""

#: bCNC/plugins/difference.py:102
msgid "Generated: Diff"
msgstr ""

#: bCNC/plugins/dragknife.py:229
msgid "Generated: Dragknife"
msgstr ""

#: bCNC/plugins/flatpath.py:60
msgid "Generated: Flat"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:545
#, fuzzy
msgid "Generated: Helical_Descent Result"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/hilbert.py:146
#, fuzzy
msgid "Generated: Hilbert"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/intersection.py:86
msgid "Generated: Intersect"
msgstr ""

#: bCNC/plugins/linearize.py:97
msgid "Generated: Linearize"
msgstr ""

#: bCNC/plugins/drillmark.py:319
#, fuzzy
msgid "Generated: Manual drillmark"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/simpleArc.py:91
#, fuzzy
msgid "Generated: Simple Arc"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/simpleDrill.py:106
#, fuzzy
msgid "Generated: Simple Drill"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/simpleLine.py:75
#, fuzzy
msgid "Generated: Simple Line"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/simpleRectangle.py:120
#, fuzzy
msgid "Generated: Simple Rectangle"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/spirograph.py:145
msgid "Generated: Spirograph"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/gear.py:180
msgid "Generated: Spur GEAR"
msgstr "Generado: ENGRANAJE"

#: bCNC/plugins/trochoidal.py:177
msgid "Generated: Trochoidal"
msgstr ""

#: bCNC/plugins/zigzag.py:158
#, fuzzy
msgid "Generated: Zig-Zag"
msgstr "Generado: ESPIRÓGRAFO"

#: bCNC/plugins/function_plot.py:14
msgid "Generates gcode from a formula"
msgstr ""

#: bCNC/plugins/jigsaw.py:387
msgid "Generating puzzle..."
msgstr ""

#: bCNC/ProbePage.py:1704
#, fuzzy
msgid "Get"
msgstr "definir"

#: bCNC/ProbePage.py:2050
msgid "Get current gantry position as machine tool change location"
msgstr ""
"Definir la posición actual de la CNC como ubicación para el cambio manual de "
"herramienta"

#: bCNC/ProbePage.py:2096
msgid "Get current gantry position as machine tool probe location"
msgstr "Definir la posición actual de la CNC como ubicación para el sondeo"

#: bCNC/ProbePage.py:1706
msgid "Get diameter from active endmill"
msgstr ""

#: bCNC/plugins/slicemesh.py:63
msgid "Get flat slice"
msgstr ""

#: bCNC/ProbePage.py:192
msgid "Get margins from gcode file"
msgstr "Obtener los márgenes de sondeo del código cargado actualmente"

#: bCNC/ProbePage.py:583
msgid "Goto"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:37
#, fuzzy
msgid "Grbl '$' system command was not recognized or supported."
msgstr ""
"No se reconoce el comando de sistema $ de Grbl, o sus parámetros no son "
"válidos."

#: bCNC/controllers/_GenericGRBL.py:21
msgid "Grbl executes jogging motion"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:13
msgid "Grbl is in idle state and waiting for user commands"
msgstr "Grbl se encuentra a la espera de nuevos comandos"

#: bCNC/controllers/_GenericGRBL.py:29
msgid "Grbl is not connected. Please specify the correct port and click Open."
msgstr ""
"No hay conexión con Grbl. Debe especificar un puerto y hacer click en Abrir."

#: bCNC/controllers/_GenericGRBL.py:14
msgid "Grbl is on hold state. Click on resume (pause) to continue"
msgstr ""
"Grbl se encuentra pausado. Haga click en el botón \"Pausar\" para continuar "
"la ejecución"

#: bCNC/plugins/sketch.py:41
msgid "Grundgy, search radius"
msgstr ""

#: bCNC/ProbePage.py:1659
msgid "Haircross Offset:"
msgstr ""

#: bCNC/ProbePage.py:1667
msgid "Haircross X offset [unit]"
msgstr ""

#: bCNC/ProbePage.py:1676
msgid "Haircross Y offset [unit]"
msgstr ""

#: bCNC/plugins/halftone.py:15
msgid "Halftone"
msgstr ""

#: bCNC/plugins/halftone.py:159
msgid "Halftone abort: Angle in V-Cutting end mill is missing"
msgstr ""

#: bCNC/plugins/halftone.py:173
#, fuzzy
msgid "Halftone abort: Can't read image file"
msgstr "Error: No se puede leer la imagen"

#: bCNC/plugins/halftone.py:148
msgid "Halftone abort: Cell size too small"
msgstr ""

#: bCNC/plugins/halftone.py:165
msgid "Halftone abort: Conical path need V-Cutting end mill"
msgstr ""

#: bCNC/plugins/halftone.py:144
msgid "Halftone abort: Maximum diameter too small"
msgstr ""

#: bCNC/plugins/halftone.py:138
msgid "Halftone abort: Minimum diameter must be minor then Maximum"
msgstr ""

#: bCNC/plugins/halftone.py:133
msgid "Halftone abort: Size too small to draw anything!"
msgstr ""

#: bCNC/plugins/halftone.py:109
#, fuzzy
msgid "Halftone abort: This plugin requires PIL/Pillow to read image data"
msgstr "Error: Éste generador necesita PIL/Pillow"

#: bCNC/ControlPage.py:84
msgid "Hard Reset"
msgstr "Re-establecer completamente"

#: bCNC/ToolsPage.py:656
msgid "Header gcode"
msgstr "Cabecera de g-code"

#: bCNC/ToolsPage.py:1105
msgid "Height"
msgstr "Altura"

#: bCNC/plugins/box.py:350
msgid "Height Dz"
msgstr "Altura Dz"

#: bCNC/plugins/flatten.py:292
msgid "Height to flatten"
msgstr "Dist. Y de aplanado"

#: bCNC/plugins/heightmap.py:40
msgid "Heightmap"
msgstr "Relieve"

#: bCNC/plugins/heightmap.py:98
msgid "Heightmap abort: Can't read image file"
msgstr "Error: No se puede leer la imagen"

#: bCNC/plugins/heightmap.py:89
msgid "Heightmap abort: This plugin requires PIL/Pillow"
msgstr "Error: Éste generador necesita PIL/Pillow"

#: bCNC/plugins/heightmap.py:171
msgid "Heightmap abort: angle not defined for selected End Mill"
msgstr "Error: No está definido el ángulo de la herramienta seleccionada"

#: bCNC/plugins/heightmap.py:102
msgid "Heightmap abort: depth must be < 0"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/Helical_Descent.py:271
#, fuzzy
msgid "Helical Abort: Drop must be greater than 0"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/Helical_Descent.py:286
msgid "Helical Abort: Entry Edge Clearence may be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:261
msgid "Helical Abort: Helix diameter must be greater than the end mill"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:267
msgid "Helical Abort: Helix diameter must be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:257
#, fuzzy
msgid "Helical Abort: Please select helical type"
msgstr "Error: Debe seleccionar algún trazo sobre el que generar el taladrado"

#: bCNC/plugins/Helical_Descent.py:281
#, fuzzy
msgid "Helical Abort: Please selecte Entry and Exit type"
msgstr "Error: Debe especificar un fichero de fuente (extensión .TTF)"

#: bCNC/plugins/Helical_Descent.py:275
msgid "Helical Abort: Z Feed Multiplier must be greater than 0"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:407
#, fuzzy
msgid "Helical Descent Diameter has to be greater than End mill"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/Helical_Descent.py:94
msgid "Helical Type"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:320
#, fuzzy
msgid "Helical abort: Please select some path"
msgstr "Error: Debe seleccionar algún trazo sobre el que generar el taladrado"

#: bCNC/TerminalPage.py:175
msgid "Help"
msgstr "Ayuda"

#: bCNC/Ribbon.py:641
msgid "Help [F1]"
msgstr "Ayuda [F1]"

#: bCNC/plugins/hilbert.py:15
msgid "Hilbert"
msgstr ""

#: bCNC/plugins/hilbert.py:136
#, fuzzy
msgid "Hilbert abort: depth must be minor or equal to zero"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/hilbert.py:131
msgid "Hilbert abort: verify the size"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:160
msgid "Hold complete. Ready to resume."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:161
msgid "Hold in-progress. Reset will throw an alarm."
msgstr ""

# Can't think of an abbreviated version in Spanish that relates better to the homing process
#: bCNC/ControlPage.py:93
msgid "Home"
msgstr "Home"

#: bCNC/controllers/_GenericGRBL.py:116
msgid "Home machine to continue. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:39
msgid "Homing cycle is not enabled via settings."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:142
msgid "Homing fail. Reset during active homing cycle."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:143
msgid "Homing fail. Safety door was opened during active homing cycle."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:152
msgid "Homing required. Execute homing command ($H) to continue. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:612
msgid "Horizontal"
msgstr "Ref. Horizontal"

#: bCNC/Utils.py:848
msgid "Icon to appear on button"
msgstr "Imagen que aparecerá de icono en el botón"

#: bCNC/Utils.py:837
msgid "Icon:"
msgstr "Icono:"

#: bCNC/plugins/Helical_Descent.py:97
msgid "If Eddge, Edge Clearance"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:253
msgid "If block selected false, please make a value of x"
msgstr ""

#: bCNC/ProbePage.py:67
msgid "Ignore M6 commands"
msgstr "Ignorar los comandos M6"

#: bCNC/plugins/text.py:44
msgid "Image chars width"
msgstr "Ancho de imagen (en caracteres)"

#: bCNC/plugins/halftone.py:50
#, fuzzy
msgid "Image rotation angle"
msgstr "Tipo de vista (2D/3D)"

#: bCNC/plugins/text.py:43
msgid "Image to Ascii"
msgstr "Imagen para ASCII-art"

#: bCNC/plugins/halftone.py:38 bCNC/plugins/sketch.py:52
#: bCNC/plugins/heightmap.py:78 bCNC/plugins/pyrograph.py:48
msgid "Image to process"
msgstr "Imagen a procesar"

#: bCNC/EditorPage.py:207 bCNC/FilePage.py:126
msgid "Import"
msgstr "Importar"

#: bCNC/bmain.py:2482
msgid "Import Gcode/DXF file"
msgstr "Importar documento G-code/DXF"

#: bCNC/FilePage.py:131
msgid "Import gcode/dxf file"
msgstr ""

#: bCNC/ControlPage.py:1008 bCNC/ControlPage.py:1484
msgid "Increase step by 1 unit"
msgstr "Aumentar el paso 1 unidad"

#: bCNC/EditorPage.py:742 bCNC/EditorPage.py:765
msgid "Info"
msgstr "Info"

#: bCNC/ToolsPage.py:1630
msgid "Insert a drill cycle on current objects/location"
msgstr "Operación de taladrado en las ubicaciones u objetos seleccionados"

#: bCNC/EditorPage.py:239 bCNC/EditorPage.py:266
msgid "Insert a new block or line of code [Ins or Ctrl-Enter]"
msgstr "Añade un nuevo bloque o línea de código [Ins or Ctrl-Enter]"

#: bCNC/ToolsPage.py:1646
msgid "Insert holding tabs"
msgstr ""
"Inserta pestañas de sustentación, para evitar que queden piezas sueltas "
"durante una operación de corte"

#: bCNC/Updates.py:60
msgid "Installed Version:"
msgstr "Versión instalada:"

#: bCNC/plugins/box.py:347
#, fuzzy
msgid "Internal Dimensions"
msgstr "Radio interno"

#: bCNC/plugins/spirograph.py:120
msgid "Internal Radius"
msgstr "Radio interno"

#: bCNC/plugins/intersection.py:18
msgid "Intersection"
msgstr ""

#: bCNC/plugins/intersection.py:24
msgid "Intersection of two shapes"
msgstr ""

#: bCNC/Updates.py:103
msgid "Interval (days):"
msgstr "Intervalo (días):"

#: bCNC/ProbePage.py:1412
msgid "Invalid X probing region"
msgstr "Región de sondeo en X no válida"

#: bCNC/ProbePage.py:1421
#, fuzzy
msgid "Invalid X range [xmin>=xmax]"
msgstr "Región de sondeo en X no válida"

#: bCNC/ProbePage.py:1436
msgid "Invalid Y probing region"
msgstr "Región de sondeo en Y no válida"

#: bCNC/ProbePage.py:1445
#, fuzzy
msgid "Invalid Y range [ymin>=ymax]"
msgstr "Región de sondeo en Y no válida"

#: bCNC/ProbePage.py:1457
msgid "Invalid Z probing region"
msgstr "Región de sondeo en Z no válida"

#: bCNC/ProbePage.py:1466
#, fuzzy
msgid "Invalid Z range [zmin>=zmax]"
msgstr "Región de sondeo en Z no válida"

#: bCNC/Sender.py:355
#, fuzzy
#| msgid "Invalid command {}"
msgid "Invalid command {}"
msgstr "Comando incorrecto {}"

#: bCNC/ProbePage.py:988
msgid "Invalid diameter entered"
msgstr "El diámetro no es válido"

#: bCNC/bmain.py:1585
#, fuzzy
#| msgid "Invalid direction {} specified"
msgid "Invalid direction {} specified"
msgstr "La dirección {} no es válida"

#: bCNC/ProbePage.py:923 bCNC/ProbePage.py:1475
msgid "Invalid probe feed rate"
msgstr "La velocidad no es válida"

#: bCNC/ProbePage.py:2199
msgid "Invalid tool change position"
msgstr "La ubicación para el cambio de herramienta no es válida"

#: bCNC/ProbePage.py:2231
msgid "Invalid tool height or not calibrated"
msgstr ""
"La altura de la herramienta no es válida, o no se ha realizado la calibración"

#: bCNC/ProbePage.py:2211
msgid "Invalid tool probe location"
msgstr "La ubicación para el sondeo no es válida"

#: bCNC/ProbePage.py:2221 bCNC/ProbePage.py:2241
msgid "Invalid tool scanning distance entered"
msgstr "La distancia de desplazamiento para el sondeo no es válida"

#: bCNC/bmain.py:1978
#, fuzzy
#| msgid "Invalid user command {}"
msgid "Invalid user command {}"
msgstr "Comando de usuario no válido: {}"

#: bCNC/plugins/heightmap.py:76 bCNC/EditorPage.py:138 bCNC/EditorPage.py:543
msgid "Invert"
msgstr "Invertir"

#: bCNC/plugins/halftone.py:45
#, fuzzy
msgid "Invert Colors"
msgstr "Colores"

#: bCNC/EditorPage.py:549
msgid "Invert cutting order of selected blocks"
msgstr "Invertir el orden de maquinado de los bloques seleccionados"

#: bCNC/EditorPage.py:144
msgid "Invert selection [Ctrl-I]"
msgstr "Invertir selección [Ctrl-I]"

#: bCNC/ToolsPage.py:1655
msgid "Island"
msgstr ""

#: bCNC/plugins/endmilloffset.py:494
msgid "Island behaviour"
msgstr ""

#: bCNC/plugins/jigsaw.py:403
msgid "Jigsaw puzzle generated in {}s"
msgstr ""

#: bCNC/plugins/jigsaw.py:353
msgid "Jigsaw puzzle generator"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:58
msgid "Jog command with no '=' or contains prohibited g-code."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:57
msgid "Jog target exceeds machine travel. Command ignored."
msgstr ""

#: bCNC/EditorPage.py:386
msgid "Join"
msgstr ""

#: bCNC/EditorPage.py:392
msgid "Join selected blocks"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:29
msgid "Keep original Yes/No"
msgstr ""

#: bCNC/plugins/simpleRotate.py:30
msgid "Keep original yes/no"
msgstr ""

#: bCNC/ToolsPage.py:1911
msgid "Language change"
msgstr "Cambio de idioma"

#: bCNC/ToolsPage.py:638
msgid "Laser Adaptive Power"
msgstr ""

#: bCNC/ToolsPage.py:637
#, fuzzy
msgid "Laser Cutter"
msgstr "Cortadora láser"

#: bCNC/controllers/_GenericGRBL.py:59
msgid "Laser mode requires PWM output."
msgstr ""

#: bCNC/plugins/driller.py:70
msgid "Laser power maximum"
msgstr ""

#: bCNC/plugins/driller.py:63
msgid "Laser power minimum"
msgstr ""

#: bCNC/Updates.py:89
msgid "Last Check:"
msgstr "Última comprobación:"

#: bCNC/ControlPage.py:603 bCNC/ControlPage.py:910
#, fuzzy
#| msgid "Last error: {}\n"
msgid "Last error: {}\n"
msgstr "Último error: {}\n"

#: bCNC/Updates.py:67
msgid "Latest Github Version:"
msgstr "Última versión en GitHub"

#: bCNC/Updates.py:73
#, fuzzy
#| msgid "Latest release version on on github"
msgid "Latest release version on github"
msgstr "Última versión liberada en GitHub"

#: bCNC/EditorPage.py:154
msgid "Layer"
msgstr ""

#: bCNC/ToolsPage.py:811
msgid "Leave islands uncut"
msgstr ""

#: bCNC/EditorPage.py:460
msgid "Left"
msgstr ""

#: bCNC/ToolsPage.py:716
msgid "Length"
msgstr "Longitud"

#: bCNC/plugins/spiral.py:477
msgid "Length of Stock to Reduce"
msgstr ""

#: bCNC/bmain.py:1204 bCNC/bmain.py:1290
msgid "Length:"
msgstr "Longitud:"

#: bCNC/plugins/spiral.py:484
msgid "Lift before rotate"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:154
msgid "Limit switch engaged. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:245
msgid "Line"
msgstr ""

#: bCNC/plugins/zigzag.py:107
#, fuzzy
msgid "Line length"
msgstr "Longitud"

#: bCNC/plugins/linearize.py:11
msgid "Linearize"
msgstr ""

#: bCNC/plugins/endmilloffset.py:503
msgid "Link segments"
msgstr ""

#: bCNC/plugins/slicemesh.py:136
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading mesh: {}"
msgstr "Cargando: {}..."

#: bCNC/bmain.py:2424
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading: {} ..."
msgstr "Cargando: {}..."

#: bCNC/ProbePage.py:1635
#, fuzzy
msgid "Location:"
msgstr "Conexión"

#: bCNC/plugins/driller.py:51
msgid "M3 for laser (settings below)"
msgstr ""

#: bCNC/ControlPage.py:314 bCNC/ControlPage.py:693
msgid "MPos:"
msgstr "MPos:"

#: bCNC/ProbePage.py:2002
msgid "MX"
msgstr ""

#: bCNC/ProbePage.py:2004
msgid "MY"
msgstr ""

#: bCNC/ProbePage.py:2006
msgid "MZ"
msgstr ""

#: bCNC/ProbePage.py:775
msgid "Machine X coordinate of orientation point"
msgstr "Coordenada X (máquina) del punto de orientación"

#: bCNC/ProbePage.py:787
msgid "Machine Y coordinate of orientation point"
msgstr "Coordenada Y (máquina) del punto de orientación"

#: bCNC/ToolsPage.py:1829
msgid "Machine configuration for bCNC"
msgstr "Configuración de la máquina CNC"

#: bCNC/controllers/_GenericGRBL.py:163
msgid "Machine stopped. Door still ajar. Can't resume until closed."
msgstr ""

#: bCNC/ProbePage.py:1962
msgid "Manual Tool Change"
msgstr "Cambio manual de herramienta"

#: bCNC/ProbePage.py:70
#, fuzzy
msgid "Manual Tool Change (NoProbe)"
msgstr "Cambio manual de herramienta (WCS)"

#: bCNC/ProbePage.py:69
msgid "Manual Tool Change (TLO)"
msgstr "Cambio manual de herramienta (TLO)"

#: bCNC/ProbePage.py:68
msgid "Manual Tool Change (WCS)"
msgstr "Cambio manual de herramienta (WCS)"

#: bCNC/plugins/drillmark.py:317
msgid "Manual drill mark"
msgstr ""

#: bCNC/ProbePage.py:2018
msgid "Manual tool change Machine X location"
msgstr "Posición X del cambio manual de herramienta"

#: bCNC/ProbePage.py:2029
msgid "Manual tool change Machine Y location"
msgstr "Posición Y del cambio manual de herramienta"

#: bCNC/ProbePage.py:2040
msgid "Manual tool change Machine Z location"
msgstr "Posición Z del cambio manual de herramienta"

#: bCNC/ProbePage.py:2064
msgid "Manual tool change Probing MX location"
msgstr "Posición X de sondeo"

#: bCNC/ProbePage.py:2075
msgid "Manual tool change Probing MY location"
msgstr "Posición Y de sondeo"

#: bCNC/ProbePage.py:2086
msgid "Manual tool change Probing MZ location"
msgstr "Posición Z de sondeo"

#: bCNC/ProbePage.py:186
msgid "Margins"
msgstr "Auto-márgenes"

#: bCNC/bmain.py:1159 bCNC/bmain.py:1240
msgid "Margins X:"
msgstr "Márgenes en X:"

#: bCNC/plugins/drillmark.py:46
msgid "Mark X center"
msgstr ""

#: bCNC/plugins/drillmark.py:47
msgid "Mark Y center"
msgstr ""

#: bCNC/ProbePage.py:1752
msgid "Mark camera position for calculating offset"
msgstr ""

#: bCNC/plugins/drillmark.py:50
msgid "Mark drawing power"
msgstr ""

#: bCNC/ProbePage.py:1745
msgid "Mark spindle position for calculating offset"
msgstr ""

#: bCNC/ProbePage.py:683
msgid "Markers:"
msgstr "Marcadores:"

#: bCNC/ToolsPage.py:711 bCNC/ToolsPage.py:739 bCNC/ToolsPage.py:1421
msgid "Material"
msgstr "Material"

#: bCNC/ProbePage.py:1232
msgid "Max"
msgstr "Máx"

#: bCNC/plugins/spiral.py:479
msgid "Max Depth per Pass"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:48
msgid "Max characters per line exceeded. Line was not processed and executed."
msgstr ""

#: bCNC/plugins/halftone.py:48
msgid "Max diameter, cap limit"
msgstr ""

#: bCNC/plugins/halftone.py:46
msgid "Max draw size (Width or Height)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:113
msgid "Max. feed rate exceeded. (grblHAL)"
msgstr ""

#: bCNC/plugins/midi2cnc.py:64
msgid "Maximum X travel"
msgstr "Distancia X máxima"

#: bCNC/plugins/midi2cnc.py:65
msgid "Maximum Y travel"
msgstr "Distancia Y máxima"

#: bCNC/plugins/midi2cnc.py:66
msgid "Maximum Z travel"
msgstr "Distancia Z máxima"

#: bCNC/plugins/pyrograph.py:42
msgid "Maximum feed"
msgstr "Velocidad máxima"

#: bCNC/plugins/sketch.py:48
msgid "Maximum light"
msgstr ""

#: bCNC/plugins/sketch.py:44 bCNC/plugins/heightmap.py:66
#: bCNC/plugins/pyrograph.py:40
msgid "Maximum size"
msgstr "Dimensión máxima"

#: bCNC/plugins/slicemesh.py:194
msgid "Mesh sliced"
msgstr ""

#: bCNC/plugins/midi2cnc.py:71
msgid "Midi to process"
msgstr "Fichero .MIDI a procesar"

#: bCNC/ProbePage.py:1230
msgid "Min"
msgstr "Min"

#: bCNC/plugins/halftone.py:49
msgid "Min diameter, cut off"
msgstr ""

#: bCNC/ToolsPage.py:1102
msgid "Min. Distance of tabs"
msgstr "Distancia mínima entre pestañas"

#: bCNC/plugins/trochoidal_3D.py:420
msgid "Minimum Adaptative Feed has to be greater than 0"
msgstr ""

#: bCNC/plugins/pyrograph.py:41
msgid "Minimum feed"
msgstr "Velocidad mínima"

#: bCNC/controllers/_GenericGRBL.py:40
msgid "Minimum step pulse time must be greater than 3usec"
msgstr ""

#: bCNC/EditorPage.py:619
msgid "Mirror horizontally X=-X selected gcode"
msgstr "Reflejar horizontalmente el g-code seleccionado (X=-X)"

#: bCNC/EditorPage.py:634
msgid "Mirror vertically Y=-Y selected gcode"
msgstr "Reflejar verticalmente el g-code seleccionado (Y=-Y)"

#: bCNC/ControlPage.py:2182
msgid "Mist"
msgstr ""

#: bCNC/ControlPage.py:2009
msgid "Mode:"
msgstr "Modo:"

#: bCNC/ProbePage.py:261
msgid "Modify selected G-Code to match autolevel"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:61
msgid "More than one g-code command from same modal group found in block."
msgstr ""

#: bCNC/ToolsPage.py:714
msgid "Mount Axis"
msgstr "Montado en el eje"

#: bCNC/EditorPage.py:416 bCNC/EditorPage.py:424
msgid "Move"
msgstr "Mover"

#: bCNC/ControlPage.py:1428
#, fuzzy
#| msgid "Move +X"
msgid "Move +A"
msgstr "Mover +X"

#: bCNC/ControlPage.py:1530
#, fuzzy
#| msgid "Move +X"
msgid "Move +B"
msgstr "Mover +X"

#: bCNC/ControlPage.py:1468
#, fuzzy
#| msgid "Move +X +Y"
msgid "Move +B +C"
msgstr "Mover +X +Y"

#: bCNC/ControlPage.py:1635
#, fuzzy
#| msgid "Move +X -Y"
msgid "Move +B -C"
msgstr "Mover +X -Y"

#: bCNC/ControlPage.py:1455
#, fuzzy
#| msgid "Move +X"
msgid "Move +C"
msgstr "Mover +X"

#: bCNC/ControlPage.py:1060
msgid "Move +X"
msgstr "Mover +X"

#: bCNC/ControlPage.py:992
msgid "Move +X +Y"
msgstr "Mover +X +Y"

#: bCNC/ControlPage.py:1163
msgid "Move +X -Y"
msgstr "Mover +X -Y"

#: bCNC/ControlPage.py:980
msgid "Move +Y"
msgstr "Mover +Y"

#: bCNC/ControlPage.py:954
msgid "Move +Z"
msgstr "Mover +Z"

#: bCNC/ControlPage.py:1596
#, fuzzy
#| msgid "Move -X"
msgid "Move -A"
msgstr "Mover -X"

#: bCNC/ControlPage.py:1504
#, fuzzy
#| msgid "Move -X"
msgid "Move -B"
msgstr "Mover -X"

#: bCNC/ControlPage.py:1442
#, fuzzy
#| msgid "Move -X +Y"
msgid "Move -B +C"
msgstr "Mover -X +Y"

#: bCNC/ControlPage.py:1609
#, fuzzy
#| msgid "Move -X -Y"
msgid "Move -B -C"
msgstr "Mover -X -Y"

#: bCNC/ControlPage.py:1622
#, fuzzy
#| msgid "Move -X"
msgid "Move -C"
msgstr "Mover -X"

#: bCNC/ControlPage.py:1028
msgid "Move -X"
msgstr "Mover -X"

#: bCNC/ControlPage.py:967
msgid "Move -X +Y"
msgstr "Mover -X +Y"

#: bCNC/ControlPage.py:1137
msgid "Move -X -Y"
msgstr "Mover -X -Y"

#: bCNC/ControlPage.py:1150
msgid "Move -Y"
msgstr "Mover -Y"

#: bCNC/ControlPage.py:1124
msgid "Move -Z"
msgstr "Mover -Z"

#: bCNC/CNCCanvas.py:451
msgid "Move CNC gantry to mouse location"
msgstr "Mover la CNC a la posición indicada con el ratón"

#: bCNC/ControlPage.py:451
#, fuzzy
msgid "Move Gantry"
msgstr "Mover la CNC [G]"

#: bCNC/EditorPage.py:451
msgid "Move all gcode such as origin is on mouse location [O]"
msgstr ""
"Desplazar todo el g-code para situar el origen en la posición definida con "
"el ratón [O]"

#: bCNC/CNCCanvas.py:760
#, fuzzy
#| msgid "Move by %g, %g, %g"
msgid "Move by {:g}, {:g}, {:g}"
msgstr "Mover {:g}, {:g}, {:g}"

#: bCNC/ControlPage.py:461
#, fuzzy
msgid "Move gantry to mouse location [g]"
msgstr "Mover la CNC a la posición indicada con el ratón"

#: bCNC/CNCCanvas.py:445
msgid "Move graphically objects"
msgstr "Mover los objetos con el ratón"

#: bCNC/EditorPage.py:433
msgid "Move objects [M]"
msgstr "Mover objetos [M]"

#: bCNC/EditorPage.py:532
msgid "Move selected g-code down [Ctrl-Down, Ctrl-PgDn]"
msgstr "Desplazar hacia abajo el código seleccionado [Ctrl-Down, Ctrl-PgDn]"

#: bCNC/EditorPage.py:513
msgid "Move selected g-code up [Ctrl-Up, Ctrl-PgUp]"
msgstr "Desplazar hacia arriba el código seleccionado [Ctrl-Up, Ctrl-PgUp]"

#: bCNC/plugins/simpleTranslate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Moved selected blocks"
msgstr "Generado: REPLICACIÓN"

#: bCNC/ControlPage.py:1001 bCNC/ControlPage.py:1477
msgid "Multiply step by 10"
msgstr "Multiplicar el paso por 10"

#: bCNC/ProbePage.py:1236
msgid "N"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:72
msgid "N line number value is not within the valid range of 1 - 9,999,999."
msgstr ""

#: bCNC/lib/bFileDialog.py:304 bCNC/plugins/Helical_Descent.py:77
#: bCNC/plugins/Random.py:29 bCNC/plugins/arcfit.py:32 bCNC/plugins/bowl.py:112
#: bCNC/plugins/box.py:346 bCNC/plugins/center.py:35
#: bCNC/plugins/difference.py:40 bCNC/plugins/dragknife.py:41
#: bCNC/plugins/driller.py:44 bCNC/plugins/drillmark.py:38
#: bCNC/plugins/endmilloffset.py:437 bCNC/plugins/flatten.py:288
#: bCNC/plugins/function_plot.py:23 bCNC/plugins/gear.py:160
#: bCNC/plugins/halftone.py:37 bCNC/plugins/hilbert.py:111
#: bCNC/plugins/intersection.py:41 bCNC/plugins/jigsaw.py:360
#: bCNC/plugins/linearize.py:33 bCNC/plugins/midi2cnc.py:60
#: bCNC/plugins/scaling.py:38 bCNC/plugins/sketch.py:36
#: bCNC/plugins/spiral.py:471 bCNC/plugins/spirograph.py:118
#: bCNC/plugins/text.py:37 bCNC/plugins/tile.py:27
#: bCNC/plugins/trochoidPath.py:37 bCNC/plugins/trochoidal.py:43
#: bCNC/plugins/trochoidal_3D.py:46 bCNC/plugins/zigzag.py:105
#: bCNC/plugins/heightmap.py:64 bCNC/plugins/pyrograph.py:37
#: bCNC/plugins/slicemesh.py:55 bCNC/ToolsPage.py:680 bCNC/ToolsPage.py:707
#: bCNC/ToolsPage.py:737 bCNC/ToolsPage.py:765 bCNC/ToolsPage.py:937
#: bCNC/ToolsPage.py:998 bCNC/ToolsPage.py:1073 bCNC/ToolsPage.py:1099
#: bCNC/ToolsPage.py:1981
msgid "Name"
msgstr "Nombre"

#: bCNC/Utils.py:833
msgid "Name to appear on button"
msgstr "Nombre que aparecerá en el botón"

#: bCNC/Utils.py:829
msgid "Name:"
msgstr "Nombre:"

#: bCNC/controllers/_GenericGRBL.py:38
msgid "Negative value received for an expected positive value."
msgstr ""

#: bCNC/FilePage.py:85
msgid "New"
msgstr "Nuevo"

#: bCNC/Ribbon.py:574
msgid "New file"
msgstr "Nuevo documento"

#: bCNC/FilePage.py:90
msgid "New gcode/dxf file"
msgstr "Crear nuevo documento en blanco"

#: bCNC/lib/bFileDialog.py:863
msgid "NewFolder"
msgstr "NuevaCarpeta"

#: bCNC/plugins/Random.py:45 bCNC/plugins/simpleRotate.py:49
#: bCNC/plugins/simpleTranslate.py:47 bCNC/plugins/tile.py:44
msgid "No g-code blocks selected"
msgstr "No se ha seleccionado ningún bloque de g-code"

#: bCNC/plugins/gear.py:161
msgid "No of teeth"
msgstr "N⁰ de dientes"

#: bCNC/EditorPage.py:122
msgid "None"
msgstr "Nada"

#: bCNC/bmain.py:2658
msgid "Not gcode file was loaded"
msgstr "No se ha cargado ningún fichero g-code"

#: bCNC/bmain.py:2035
msgid "Nothing to do"
msgstr ""

#: bCNC/ToolsPage.py:943
#, fuzzy
msgid "Number"
msgstr "Numero de pestañas"

#: bCNC/plugins/trochoidPath.py:60
#, fuzzy
#| msgid "Number of tabs"
msgid "Number of Tabs 0 = Not Tabs"
msgstr "Numero de pestañas"

#: bCNC/plugins/zigzag.py:106
#, fuzzy
msgid "Number of lines"
msgstr "Numero de pestañas"

#: bCNC/ToolsPage.py:1101
msgid "Number of tabs"
msgstr "Numero de pestañas"

#: bCNC/controllers/_GenericGRBL.py:36
msgid "Numeric value format is not valid or missing an expected value."
msgstr ""

#: bCNC/ControlPage.py:2154
msgid "OFF"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "ONLY before probing"
msgstr "SOLO antes del sondeo"

#: bCNC/plugins/endmilloffset.py:14
#, fuzzy
#| msgid "Offset:"
msgid "Offset"
msgstr "Desplazamiento:"

#: bCNC/plugins/spirograph.py:121
msgid "Offset radius"
msgstr "Radio de desplazamiento"

#: bCNC/plugins/endmilloffset.py:462
#, fuzzy
#| msgid "Offset radius"
msgid "Offset side"
msgstr "Radio de desplazamiento"

#: bCNC/ProbePage.py:836 bCNC/ProbePage.py:1710
msgid "Offset:"
msgstr "Desplazamiento:"

#: bCNC/Utils.py:877
msgid "Ok"
msgstr "Aceptar"

#: bCNC/controllers/_GenericGRBL.py:115
msgid "Only homing is allowed when a limit switch is engaged. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:816
msgid "Only leave selected islands uncut"
msgstr ""

#: bCNC/lib/bFileDialog.py:337 bCNC/lib/bFileDialog.py:938
#: bCNC/lib/bFileDialog.py:1063 bCNC/FilePage.py:110 bCNC/FilePage.py:376
#: bCNC/bmain.py:2546
msgid "Open"
msgstr "Abrir"

#: bCNC/FilePage.py:103
msgid "Open existing gcode/dxf file [Ctrl-O]"
msgstr "Abrir documento"

#: bCNC/bmain.py:2345
msgid "Open file"
msgstr "Abrir documento"

#: bCNC/Ribbon.py:584
msgid "Open file [Ctrl-O]"
msgstr "Abrir documento [Ctrl-O]"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
msgid "Open paths"
msgstr "Trazos no cerrados"

#: bCNC/FilePage.py:116
msgid "Open recent file"
msgstr "Documentos recientes"

#: bCNC/Updates.py:186
msgid "Open web browser to download bCNC"
msgstr "Abrir el navegador para descargar bCNC"

#: bCNC/ControlPage.py:129
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Open/Close connection"
msgstr "Conectar/Desconectar"

#: bCNC/FilePage.py:384
msgid "Open/Close serial port"
msgstr "Conectar/Desconectar"

#: bCNC/plugins/endmilloffset.py:443
#, fuzzy
#| msgid "Operation error"
msgid "Operation"
msgstr "Error en la operación"

#: bCNC/bmain.py:2083
msgid "Operation error"
msgstr "Error en la operación"

#: bCNC/bmain.py:2036
msgid "Operation {} requires some gcode to be selected"
msgstr ""

#: bCNC/EditorPage.py:490 bCNC/bmain.py:1780
msgid "Optimize"
msgstr "Optimizar g-code"

#: bCNC/FilePage.py:168
msgid "Options"
msgstr "Opciones"

#: bCNC/plugins/hilbert.py:113 bCNC/EditorPage.py:486
msgid "Order"
msgstr "Ordenar"

#: bCNC/ProbePage.py:677 bCNC/ProbePage.py:822 bCNC/bmain.py:138
msgid "Orient"
msgstr "Orientar"

#: bCNC/EditorPage.py:441
msgid "Origin"
msgstr "Origen"

#: bCNC/plugins/box.py:355 bCNC/plugins/trochoidPath.py:52
#: bCNC/plugins/trochoidal_3D.py:76 bCNC/ToolsPage.py:1018
msgid "Overcut"
msgstr "Ampliar esquinas interiores"

#: bCNC/plugins/endmilloffset.py:485
#, fuzzy
#| msgid "Overcut"
msgid "Overcut corners"
msgstr "Ampliar esquinas interiores"

#: bCNC/lib/bFileDialog.py:985
#, fuzzy
#| msgid "Overwrite existing file %r?"
msgid "Overwrite existing file {}?"
msgstr "¿Desea sobreescribir el fichero {}?"

#: bCNC/ProbePage.py:496
msgid "POINT"
msgstr ""

#: bCNC/CNCCanvas.py:435
msgid "Pan viewport"
msgstr "Mover vista"

#: bCNC/CNCCanvas.py:2300
msgid "Pan viewport [X]"
msgstr "Mover vista [X]"

#: bCNC/TerminalPage.py:96
msgid "Parameters"
msgstr "Parámetros"

#: bCNC/EditorPage.py:49
msgid "Paste"
msgstr "Pegar"

#: bCNC/EditorPage.py:55
msgid "Paste [Ctrl-V]"
msgstr "Pegar [Ctrl-V]"

#: bCNC/ControlPage.py:192
msgid "Pause"
msgstr "Pausar"

#: bCNC/ControlPage.py:214
msgid "Pause running program and soft reset controller to empty the buffer."
msgstr ""
"Pausar el programa en curso y reiniciar la máquina para vaciar el buffer."

#: bCNC/ControlPage.py:199
msgid "Pause running program. Sends either FEED_HOLD ! or CYCLE_START ~"
msgstr "Pausar el trabajo en curso. Envía FEED_HOLD ! ó CYCLE_START ~"

#: bCNC/ProbePage.py:1985
msgid "Pause:"
msgstr "Pausa:"

#: bCNC/ToolsPage.py:940
msgid "Peck depth"
msgstr "Profun. por pasada (valor positivo)"

#: bCNC/plugins/simpleDrill.py:70
#, fuzzy
#| msgid "Peck depth"
msgid "Peck depth (positive)"
msgstr "Profun. por pasada (valor positivo)"

#: bCNC/plugins/driller.py:47
#, fuzzy
#| msgid "Peck, 0 meas None"
msgid "Peck, 0 means None"
msgstr "Prof. por pasada (poner 0 para taladro simple)"

#: bCNC/FilePage.py:232 bCNC/bmain.py:2713 bCNC/bmain.py:2719
#: bCNC/bmain.py:2733
msgid "Pendant"
msgstr "Servidor web"

#: bCNC/bmain.py:2720
msgid "Pendant already started:\n"
msgstr "El servidor web ya se encuentra iniciado:\n"

#: bCNC/bmain.py:2714
msgid "Pendant started:\n"
msgstr "Iniciado el servidor web:\n"

#: bCNC/bmain.py:2734
msgid "Pendant stopped"
msgstr "Se ha detenido el servidor web"

#: bCNC/ProbePage.py:2135
msgid "Perform a calibration probing to determine the height"
msgstr "Realizar un sondeo para determinar la altura"

#: bCNC/ControlPage.py:100
#, fuzzy
#| msgid "Perform a homing cycle [$H]"
msgid "Perform a homing cycle [$H] now"
msgstr "Mover los ejes hasta los interruptores de fin de carrera [$H]"

#: bCNC/ToolsPage.py:1613
msgid "Perform a pocket operation on selected code"
msgstr ""
"Excava el interior del trazo seleccionado. Para que la ruta generada tenga "
"profundidad, debe aplicarse una operación de corte posteriormente"

#: bCNC/ToolsPage.py:1595
msgid "Perform a profile operation on selected code"
msgstr ""
"Perfilado de la ruta seleccionada. Como la operación de corte, pero teniendo "
"en cuenta el diámetro de la herramienta seleccionada"

# Review needed
#: bCNC/ProbePage.py:1935
msgid "Perform a single a tool change cycle to set the calibration field"
msgstr ""
"Realizar un cambio de herramienta para obtener el campo \"calibración\""

#: bCNC/ProbePage.py:636
msgid "Perform a single probe cycle"
msgstr "Realizar un ciclo de sondeo"

#: bCNC/ProbePage.py:1951
msgid "Perform a tool change cycle"
msgstr "Realizar un cambio de herramienta"

#: bCNC/plugins/jigsaw.py:363
msgid "Piece count"
msgstr ""

#: bCNC/ControlPage.py:1978
msgid "Plane [G17,G18,G19]"
msgstr "Plano de trabajo [G17,G18,G19]"

#: bCNC/controllers/_GenericGRBL.py:112
msgid "Plane must be ZX for threading. (grblHAL)"
msgstr ""

#: bCNC/ControlPage.py:1967
msgid "Plane:"
msgstr "Plano:"

#: bCNC/ToolsPage.py:1912
msgid "Please restart the program."
msgstr "Por favor, reinicie el programa"

#: bCNC/bmain.py:1781
msgid "Please select the blocks of gcode you want to optimize."
msgstr "Seleccione los bloques de g-code que desea optimizar."

#: bCNC/bmain.py:2609
msgid "Please stop before"
msgstr "Antes debe pulsar \"Detener\""

#: bCNC/ToolsPage.py:651
msgid "Plotting Arc accuracy"
msgstr "Resol. al dibuj. arcos"

#: bCNC/plugins/scaling.py:44 bCNC/plugins/trochoidal_3D.py:96
#: bCNC/ToolsPage.py:683 bCNC/ToolsPage.py:770
msgid "Plunge Feed"
msgstr "Velocidad de bajada"

#: bCNC/plugins/trochoidal_3D.py:415
msgid "Plunge Feed has to be greater than 0"
msgstr ""

#: bCNC/ToolsPage.py:1025 bCNC/ToolsPage.py:1604
msgid "Pocket"
msgstr "Ahuecar"

#: bCNC/plugins/flatten.py:299
msgid "Pocket type"
msgstr "Tipo de excavación"

#: bCNC/ProbePage.py:1164
msgid "Pointrec"
msgstr ""

#: bCNC/ProbePage.py:1967
msgid "Policy:"
msgstr "Política:"

#: bCNC/FilePage.py:294
msgid "Port:"
msgstr "Puerto:"

#: bCNC/ProbePage.py:596
msgid "Pos:"
msgstr "Posición:"

#: bCNC/EditorPage.py:209
msgid "Postprocess Inkscape g-code"
msgstr ""

#: bCNC/plugins/gear.py:162
msgid "Pressure angle"
msgstr "Ángulo de presión"

#: bCNC/ProbePage.py:106 bCNC/ProbePage.py:114 bCNC/ProbePage.py:539
#: bCNC/ProbePage.py:627 bCNC/bmain.py:137
msgid "Probe"
msgstr "Sondeo"

#: bCNC/ProbePage.py:987
msgid "Probe Center Error"
msgstr "Error al centrar"

#: bCNC/ProbePage.py:369
msgid "Probe Command"
msgstr "Comando de sondeo:"

#: bCNC/ProbePage.py:922 bCNC/ProbePage.py:955 bCNC/ProbePage.py:1411
#: bCNC/ProbePage.py:1420 bCNC/ProbePage.py:1435 bCNC/ProbePage.py:1444
#: bCNC/ProbePage.py:1456 bCNC/ProbePage.py:1465 bCNC/ProbePage.py:1474
msgid "Probe Error"
msgstr "Error de sondeo"

#: bCNC/ProbePage.py:328
msgid "Probe Feed:"
msgstr "Velocidad de sondeo:"

#: bCNC/bmain.py:2390
msgid "Probe File modified"
msgstr "Datos de sondeo modificados"

#: bCNC/ProbePage.py:2198 bCNC/ProbePage.py:2210 bCNC/ProbePage.py:2220
#: bCNC/ProbePage.py:2230 bCNC/ProbePage.py:2240
msgid "Probe Tool Change Error"
msgstr "Error en el sondeo de cambio de herramienta"

#: bCNC/ProbePage.py:603
msgid "Probe along X direction"
msgstr "Sondear en la dirección X"

#: bCNC/ProbePage.py:611
msgid "Probe along Y direction"
msgstr "Sondear en la dirección Y"

#: bCNC/ProbePage.py:619
msgid "Probe along Z direction"
msgstr "Sondear en la dirección Z"

#: bCNC/ProbePage.py:2366
msgid "Probe configuration and probing"
msgstr "Configuración de la sonda, cambio de herramienta y auto-nivelado"

#: bCNC/ProbePage.py:902
msgid "Probe connected?"
msgstr "¿Está conectada la sonda?"

#: bCNC/controllers/_GenericGRBL.py:155
msgid "Probe protection triggered. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2391
msgid "Probe was modified do you want to save it first?"
msgstr "Los datos de muestreo han cambiado ¿Desea guardarlos antes?"

#: bCNC/ProbePage.py:543 bCNC/ProbePage.py:2057
msgid "Probe:"
msgstr "Sonda:"

#: bCNC/ProbePage.py:655
msgid "Probing ring internal diameter"
msgstr "Diámetro interno del anillo de calibración"

#: bCNC/plugins/box.py:354 bCNC/ToolsPage.py:1586
msgid "Profile"
msgstr "Perfilar"

#: bCNC/bmain.py:2129 bCNC/bmain.py:2231
#, fuzzy
#| msgid "Profile block distance=%g"
msgid "Profile block distance={:g}"
msgstr "Perfilado del bloque a distancia={:g}"

#: bCNC/plugins/bowl.py:115
msgid "Progressive"
msgstr "Progresivo"

#: bCNC/Updates.py:74
msgid "Published at:"
msgstr "Publicado el:"

#: bCNC/Updates.py:80
#, fuzzy
msgid "Published date of the latest github release"
msgstr "Fecha de publicación de la última versión en GitHub"

#: bCNC/plugins/midi2cnc.py:61
msgid "Pulse per unit for X"
msgstr "Pasos por unidad (X)"

#: bCNC/plugins/midi2cnc.py:62
msgid "Pulse per unit for Y"
msgstr "Pasos por unidad (Y)"

#: bCNC/plugins/midi2cnc.py:63
msgid "Pulse per unit for Z"
msgstr "Pasos por unidad (Z)"

#: bCNC/plugins/pyrograph.py:13
msgid "Pyrograph"
msgstr "Pirograbado"

#: bCNC/plugins/pyrograph.py:97
msgid "Pyrograph abort: Can't read image file"
msgstr "Error: No se puede leer la imagen"

#: bCNC/plugins/pyrograph.py:85
msgid "Pyrograph abort: Please check feed rate parameters"
msgstr "Error: Compruebe los parámetros de velocidad"

#: bCNC/plugins/pyrograph.py:58
msgid "Pyrograph abort: This plugin requires PIL/Pillow"
msgstr "Error: Éste generador necesita PIL/Pillow"

#: bCNC/plugins/pyrograph.py:80
msgid "Pyrograph abort: Tool Size must be > 0"
msgstr "Error: El grosor de la punta de pirograbado debe ser un valor positivo"

#: bCNC/plugins/pyrograph.py:76
msgid "Pyrograph abort: please define a scan Direction"
msgstr "Error: Debe definir una dirección de escaneo"

#: bCNC/plugins/pyrograph.py:38
msgid "Pyrograph tip size"
msgstr "Grosor de la punta de pirograbado"

#: bCNC/ProbePage.py:474
msgid "RAPID"
msgstr ""

#: bCNC/plugins/scaling.py:45 bCNC/plugins/trochoidal_3D.py:94
msgid "RPM"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:114
msgid "RPM out of range. (grblHAL)"
msgstr ""

#: bCNC/plugins/trochoidal.py:51
msgid "Radial depth of cut (<= cutter D * 0.4)"
msgstr ""

#: bCNC/plugins/simpleArc.py:67
msgid "Radius"
msgstr ""

#: bCNC/ToolsPage.py:781
msgid "Ramp length"
msgstr ""

#: bCNC/plugins/Random.py:15
msgid "Random"
msgstr ""

#: bCNC/plugins/jigsaw.py:364
msgid "Random seed"
msgstr ""

#: bCNC/plugins/function_plot.py:26
msgid "Range of X"
msgstr ""

#: bCNC/plugins/function_plot.py:27
msgid "Range of Y"
msgstr ""

#: bCNC/ProbePage.py:592
#, fuzzy
msgid "Rapid goto to last probe location"
msgstr "La ubicación para el sondeo no es válida"

#: bCNC/bmain.py:1213
msgid "Rapid:"
msgstr ""

#: bCNC/plugins/flatten.py:294
msgid "Raster border"
msgstr "Repasar los lados con cortes rectos"

#: bCNC/ProbePage.py:452
msgid "Record"
msgstr ""

#: bCNC/ProbePage.py:468
msgid "Record Z coordinate?"
msgstr ""

#: bCNC/Ribbon.py:624
msgid "Redo [Ctrl-Y]"
msgstr "Repetir [Ctrl-Y]"

#: bCNC/CNCCanvas.py:2425
msgid "Redraw display [Ctrl-R]"
msgstr "Re-dibujar la pantalla [Ctrl-R]"

#: bCNC/plugins/spiral.py:464
msgid "Reduce Diameter of 4th Axis Stock"
msgstr ""

#: bCNC/FilePage.py:361
msgid "Refresh"
msgstr ""

#: bCNC/FilePage.py:367
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Refresh list of serial ports"
msgstr "Conectar/Desconectar"

#: bCNC/ProbePage.py:1740
msgid "Register:"
msgstr ""

#: bCNC/ToolsPage.py:1455
msgid "Rename"
msgstr "Renombrar"

#: bCNC/plugins/sketch.py:51
msgid "Repetition of a point"
msgstr ""

#: bCNC/FilePage.py:189 bCNC/Utils.py:568
msgid "Report"
msgstr "Bugs"

#: bCNC/Utils.py:701
msgid "Report successfully send"
msgstr "Informe enviado correctamente"

#: bCNC/Utils.py:702
msgid "Report was successfully uploaded to web site"
msgstr "Se ha enviado el informe. Muchas gracias por su colaboración"

#: bCNC/ControlPage.py:136 bCNC/ControlPage.py:2080
msgid "Reset"
msgstr "Reiniciar"

#: bCNC/ControlPage.py:2082
#, fuzzy
msgid "Reset override to 100%"
msgstr "Restablecer velocidad al 100%"

#: bCNC/plugins/function_plot.py:25
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "Resolution"
msgstr "Resolución (grados)"

#: bCNC/plugins/bowl.py:114
msgid "Resolution (degrees)"
msgstr "Resolución (grados)"

#: bCNC/TerminalPage.py:67
msgid "Restore All"
msgstr "Restablecer todo"

#: bCNC/TerminalPage.py:64
msgid "Restore Settings"
msgstr "Restablecer ajustes"

#: bCNC/TerminalPage.py:66
msgid "Restore Workspace"
msgstr "Restablecer eje relativo de trabajo"

#: bCNC/ControlPage.py:493
msgid "Resume"
msgstr "Continuar"

#: bCNC/ControlPage.py:1517
msgid "Return ABC to 0."
msgstr ""

#: bCNC/EditorPage.py:691
msgid "Reverse"
msgstr "Marcha atrás"

#: bCNC/EditorPage.py:699
msgid "Reverse cut direction for selected gcode blocks"
msgstr "Invierte la dirección de corte para los bloques seleccionados"

#: bCNC/EditorPage.py:466
#, fuzzy
msgid "Right"
msgstr "Altura"

#: bCNC/plugins/drillmark.py:58
msgid "Ring mark (d/2)"
msgstr ""

#: bCNC/plugins/spiral.py:94
#, fuzzy
#| msgid "All accepted"
msgid "Risk Accepted"
msgstr "Se aceptan"

#: bCNC/plugins/spiral.py:475
msgid "Rotary Alignment Axis"
msgstr ""

#: bCNC/plugins/spiral.py:476
#, fuzzy
#| msgid "Mount Axis"
msgid "Rotary Axis"
msgstr "Montado en el eje"

#: bCNC/EditorPage.py:588
msgid "Rotate selected gcode by 180deg"
msgstr "Voltear el g-code 180⁰"

#: bCNC/EditorPage.py:573
msgid "Rotate selected gcode clock-wise (-90deg)"
msgstr "Rotar el g-code en el sentido de las agujas del reloj"

#: bCNC/EditorPage.py:604
msgid "Rotate selected gcode counter-clock-wise (90deg)"
msgstr "Rotar el g-code en el sentido contrario a las agujas del reloj"

#: bCNC/plugins/simpleRotate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Rotated selected blocks"
msgstr "Generado: REPLICACIÓN"

#: bCNC/plugins/simpleRotate.py:19
msgid "Rotates a block to a new position"
msgstr ""

#: bCNC/ProbePage.py:1647
msgid "Rotation:"
msgstr ""

#: bCNC/EditorPage.py:213
msgid "Round"
msgstr "Redondear"

#: bCNC/EditorPage.py:647
msgid "Route"
msgstr ""

#: bCNC/CNCCanvas.py:2329
msgid "Ruler [R]"
msgstr "Regla [R]"

#: bCNC/Sender.py:685
msgid "Run ended"
msgstr "Ejecución completada"

#: bCNC/ControlPage.py:184
msgid "Run g-code commands from editor to controller"
msgstr "Ejecutar el trabajo CNC actualmente cargado"

#: bCNC/bmain.py:635
msgid "Running"
msgstr "Trabajando"

#: bCNC/Updates.py:65
msgid "Running version of bCNC"
msgstr "Versión de bCNC en ejecución"

#: bCNC/bmain.py:2695
msgid "Running..."
msgstr "Ejecutando..."

#: bCNC/controllers/_GenericGRBL.py:123
msgid "SD Card directory listing failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:124
msgid "SD Card directory not found. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:125
msgid "SD Card file empty. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:122
msgid "SD Card file open/read failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:121
msgid "SD Card mount failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/slicemesh.py:40
msgid "STL/PLY Slicer"
msgstr ""

#: bCNC/ToolsPage.py:740
msgid "Safe Z"
msgstr "Z segura para despl."

#: bCNC/plugins/simpleDrill.py:73
msgid "Safe z secu for G0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:53
msgid "Safety door detected as opened and door state initiated."
msgstr ""

#: bCNC/lib/bFileDialog.py:977 bCNC/FilePage.py:153
msgid "Save"
msgstr "Guardar"

#: bCNC/lib/bFileDialog.py:972
msgid "Save As"
msgstr "Guardar como"

#: bCNC/Ribbon.py:594
msgid "Save all [Ctrl-S]"
msgstr "Guardar todo [Ctrl-S]"

#: bCNC/bmain.py:2366
msgid "Save file"
msgstr "Guardar documento"

#: bCNC/FilePage.py:159
msgid "Save gcode/dxf AS"
msgstr "Guardar como"

#: bCNC/FilePage.py:145
msgid "Save gcode/dxf file [Ctrl-S]"
msgstr "Guardar documento [Ctrl-S]"

#: bCNC/ProbePage.py:1680
msgid "Scale:"
msgstr ""

#: bCNC/plugins/scaling.py:22
msgid "Scaling"
msgstr ""

#: bCNC/plugins/scaling.py:291
msgid "Scaling Generated"
msgstr ""

#: bCNC/plugins/scaling.py:175
msgid "Scaling abort: Please select some path"
msgstr ""

#: bCNC/plugins/scaling.py:30
msgid "Scaling the selected block"
msgstr ""

#: bCNC/plugins/heightmap.py:67 bCNC/ProbePage.py:241 bCNC/ProbePage.py:271
msgid "Scan"
msgstr "Escanear"

#: bCNC/ProbePage.py:247
msgid "Scan Autolevel Margins"
msgstr ""

#: bCNC/ProbePage.py:280
msgid "Scan probed area for level information on Z plane"
msgstr ""
"Realizar el sondeo de la superficie para obtener los datos de auto-nivelado"

#: bCNC/plugins/heightmap.py:72
msgid "ScanDir"
msgstr "Dirección de escaneo"

#: bCNC/EditorPage.py:96
msgid "Select"
msgstr "Seleccionar"

#: bCNC/FilePage.py:307
msgid "Select (or manual enter) port to connect"
msgstr "Seleccione el puerto serie al que se encuentra conectada su CNC"

#: bCNC/EditorPage.py:112
msgid "Select all blocks [Ctrl-A]"
msgstr "Seleccionar todos los bloques [Ctrl-A]"

#: bCNC/EditorPage.py:160
#, fuzzy
msgid "Select all blocks from current layer"
msgstr "Seleccionar todos los bloques [Ctrl-A]"

#: bCNC/FilePage.py:323
msgid "Select connection baud rate"
msgstr ""
"Seleccione la tasa (baud rate) a la que se encuentra configurada la placa "
"controladora de su CNC"

#: bCNC/FilePage.py:340
msgid "Select controller board"
msgstr "Seleccionar placa controladora"

#: bCNC/CNCCanvas.py:430
msgid "Select objects with mouse"
msgstr "Seleccionar objetos con el ratón"

#: bCNC/ProbePage.py:696
msgid "Select orientation marker"
msgstr "Seleccionar marcador de orientación"

#: bCNC/ControlPage.py:2078
msgid "Select override type."
msgstr ""

#: bCNC/CNCCanvas.py:2288
msgid "Select tool [S]"
msgstr "Seleccionar [S]"

#: bCNC/plugins/Helical_Descent.py:79
#, fuzzy
msgid "Selected Block"
msgstr "Generado: REPLICACIÓN"

#: bCNC/FilePage.py:197
msgid "Send Error Report"
msgstr "Notificar de problemas al autor"

#: bCNC/ProbePage.py:66
msgid "Send M6 commands"
msgstr "Enviar los comandos M6"

#: bCNC/Utils.py:617
msgid "Send report"
msgstr "Enviar informe"

#: bCNC/FilePage.py:289
msgid "Serial"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial Error"
msgstr "Error de puerto serie"

#: bCNC/TerminalPage.py:241
msgid "Serial Terminal"
msgstr "Monitor del puerto serie"

#: bCNC/bmain.py:2601
msgid "Serial is not connected"
msgstr "El puerto serie no está conectado"

#: bCNC/ControlPage.py:735
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set A coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:801
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set ABC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:751
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set B coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:785
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set BC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:767
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set C coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:2101
msgid "Set Feed/Rapid/Spindle Override. Right or Double click to reset."
msgstr ""

#: bCNC/ControlPage.py:436
msgid "Set WPOS"
msgstr "Definir WPOS"

#: bCNC/ControlPage.py:445
msgid "Set WPOS to mouse location"
msgstr "Definir WPOS con la posición indicada con el ratón"

#: bCNC/ControlPage.py:357
msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada X (o al valor definido en WPos)"

#: bCNC/ControlPage.py:407
msgid "Set XY coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:424
msgid "Set XYZ coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:373
msgid "Set Y coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada Y (o al valor definido en WPos)"

#: bCNC/ControlPage.py:389
msgid "Set Z coordinate to zero (or to typed coordinate in WPos)"
msgstr "Poner a cero la coordenada Z (o al valor definido en WPos)"

#: bCNC/ProbePage.py:320
msgid "Set initial probe feed rate for tool change and calibration"
msgstr ""

#: bCNC/CNCCanvas.py:458
msgid "Set mouse location as current machine position (X/Y only)"
msgstr "Definir la posición actual de la CNC con el ratón (sólo X e Y)"

#: bCNC/ControlPage.py:2056
msgid "Set position [G92 X# Y# Z#]"
msgstr ""

#: bCNC/ProbePage.py:340
msgid "Set probe feed rate"
msgstr "Definir velocidad de sondeo (feed rate)"

#: bCNC/ControlPage.py:2135
msgid "Set spindle RPM"
msgstr "Definir la velocidad del motor spindle (Revoluciones Por Minuto)"

#: bCNC/ProbePage.py:354
msgid "Set tool offset for probing"
msgstr "Definir compensación de longitud de la herramienta, para el sondeo"

#: bCNC/controllers/_GenericController.py:163
#, fuzzy
#| msgid "Set workspace {} to {}"
msgid "Set workspace {} to {}"
msgstr "Definir el espacio de trabajo {} a {}"

#: bCNC/ToolsPage.py:1019
msgid "Sets if we want to overcut or not."
msgstr ""

#: bCNC/TerminalPage.py:80
msgid "Settings"
msgstr "Ajustes"

#: bCNC/ProbePage.py:165
msgid "Setup probing for manual tool change"
msgstr "Definir los parámetros del protocolo de cambio de herramienta"

#: bCNC/ToolsPage.py:710
msgid "Shape"
msgstr "Forma"

#: bCNC/plugins/jigsaw.py:369
msgid "Shape of the tap"
msgstr ""

#: bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:581
#: bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:584
#: bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:587
#: bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:590
msgid "Shift-"
msgstr ""

#: bCNC/ToolsPage.py:1837
msgid "Shortcuts"
msgstr "Atajos"

#: bCNC/ToolsPage.py:1845
#, fuzzy
msgid "Shortcuts configuration"
msgstr "Configuración de estilo de fuente"

#: bCNC/ToolsPage.py:1011
msgid "Should we machine on inside or outside of the shape?"
msgstr ""

#: bCNC/ControlPage.py:473
msgid "Show Info"
msgstr "Mostrar detalles"

#: bCNC/EditorPage.py:773
msgid "Show cutting information on selected blocks [Ctrl-n]"
msgstr ""
"Muestra información sobre el corte en los bloques seleccionados [Ctrl-n]"

#: bCNC/EditorPage.py:757
msgid "Show statistics for enabled gcode"
msgstr "Muestra datos estadísticos del g-code seleccionado"

#: bCNC/ProbePage.py:121
msgid "Simple probing along a direction"
msgstr "Sondeo simple en una dirección"

#: bCNC/plugins/heightmap.py:77
msgid "Single pass"
msgstr ""

#: bCNC/lib/bFileDialog.py:306 bCNC/plugins/hilbert.py:112
msgid "Size"
msgstr "Tamaño"

#: bCNC/ToolsPage.py:1004
msgid "Size of this endmill will be used as offset distance"
msgstr ""

#: bCNC/plugins/sketch.py:20
msgid "Sketch"
msgstr ""

#: bCNC/plugins/sketch.py:255
#, fuzzy
msgid "Sketch abort: Can't read image file"
msgstr "Error: No se puede leer la imagen"

#: bCNC/plugins/sketch.py:244
#, fuzzy
msgid "Sketch abort: Please let me draw at least 1 squiggle"
msgstr "Error: Debe especificar un fichero de fuente (extensión .TTF)"

#: bCNC/plugins/sketch.py:248
#, fuzzy
msgid "Sketch abort: Squiggle Length must be > 0"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/sketch.py:200
#, fuzzy
msgid "Sketch abort: This plugin requires PIL/Pillow to read image data"
msgstr "Error: Éste generador necesita PIL/Pillow"

#: bCNC/plugins/sketch.py:234
msgid "Sketch abort: Too small to draw anything!"
msgstr ""

#: bCNC/plugins/slicemesh.py:179
msgid "Slicing {} {:f} in {:f} -> {:f} of {}"
msgstr ""

#: bCNC/plugins/function_plot.py:34
#, fuzzy
msgid "Small line length"
msgstr "Longitud"

#: bCNC/controllers/_GenericGRBL.py:46
msgid "Soft limits cannot be enabled without homing also enabled."
msgstr ""

#: bCNC/ControlPage.py:143
msgid "Software reset of controller [ctrl-x]"
msgstr "Reinicio del software de la máquina [ctrl-x]"

#: bCNC/plugins/midi2cnc.py:32
msgid "Sound your machine from a midi file"
msgstr ""
"Haga sonar música con los motores de su máquina CNC, cargando un fichero de "
"audio .MIDI"

#: bCNC/ControlPage.py:2112
msgid "Spindle"
msgstr "RPM"

#: bCNC/ProbePage.py:1737
msgid "Spindle Z position when camera was registered"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:156
msgid "Spindle at speed timeout. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:654
msgid "Spindle max (RPM)"
msgstr "Vel. máx. spindle (RPM)"

#: bCNC/ToolsPage.py:653
msgid "Spindle min (RPM)"
msgstr "Vel. min. spindle (RPM)"

#: bCNC/ProbePage.py:1854
msgid "Spindle position is not registered"
msgstr ""

#: bCNC/ProbePage.py:1846
msgid "Spindle position is registered"
msgstr ""

#: bCNC/ProbePage.py:1855
msgid "Spindle position must be registered before camera"
msgstr ""

#: bCNC/plugins/spiral.py:18
#, fuzzy
#| msgid "Spirograph"
msgid "Spiral"
msgstr "Espirógrafo"

#: bCNC/plugins/spiral.py:69
#, fuzzy
msgid "Spiral abort: Approach height must be greater than Z Start"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/spiral.py:74
msgid "Spiral abort: Depth Reduction must be negative"
msgstr ""

#: bCNC/plugins/spiral.py:156 bCNC/plugins/spiral.py:179
#: bCNC/plugins/spiral.py:192 bCNC/plugins/spiral.py:220
#: bCNC/plugins/spiral.py:284 bCNC/plugins/spiral.py:318
#: bCNC/plugins/spiral.py:357 bCNC/plugins/spiral.py:379
#: bCNC/plugins/spiral.py:430
msgid "Spiral abort: Rotary Axis Not Assigned."
msgstr ""

#: bCNC/plugins/spiral.py:60
#, fuzzy
#| msgid "Flatten abort: Pocket Type is undefined"
msgid "Spiral abort: Rotary Axis is undefined"
msgstr "Error: Tipo de excavación no definida"

#: bCNC/plugins/spiral.py:64
#, fuzzy
#| msgid "Flatten abort: Pocket Type is undefined"
msgid "Spiral abort: Spiral Type is undefined"
msgstr "Error: Tipo de excavación no definida"

#: bCNC/plugins/spiral.py:80
msgid "Spiral abort: Step Over exceeds tool limits"
msgstr ""

#: bCNC/plugins/spiral.py:124
#, fuzzy
#| msgid "Flatten abort: Flatten area is too small for this End Mill."
msgid "Spiral abort: Stock Length is too small for this End Mill."
msgstr ""
"Error: El área a aplanar es demasiado pequeña para la herramienta "
"seleccionada"

#: bCNC/plugins/spiral.py:100
msgid "Spiral abort: Stock Length to cut must be positive"
msgstr ""

#: bCNC/plugins/spiral.py:518
msgid "Spiral: Reduced 4th Axis Stock"
msgstr ""

#: bCNC/plugins/spirograph.py:17
msgid "Spirograph"
msgstr "Espirógrafo"

#: bCNC/EditorPage.py:401
msgid "Split"
msgstr ""

#: bCNC/EditorPage.py:407
msgid "Split selected blocks"
msgstr ""

#: bCNC/ToolsPage.py:795
msgid "Spring pass"
msgstr ""

#: bCNC/plugins/sketch.py:46
msgid "Squiggle Length"
msgstr ""

#: bCNC/plugins/sketch.py:45
msgid "Squiggle total count"
msgstr ""

#: bCNC/ControlPage.py:178 bCNC/FilePage.py:238
msgid "Start"
msgstr "Comenzar"

#: bCNC/plugins/simpleArc.py:68
msgid "Start Angle in Degrees"
msgstr ""

#: bCNC/ControlPage.py:2175
msgid "Start flood (M8)"
msgstr ""

#: bCNC/ControlPage.py:2189
msgid "Start mist (M7)"
msgstr ""

#: bCNC/FilePage.py:246
msgid "Start pendant"
msgstr "Iniciar servidor web (para controlar la CNC desde la red)"

#: bCNC/ToolsPage.py:652
msgid "Start up"
msgstr "Comando de inicio"

#: bCNC/ControlPage.py:2121
msgid "Start/Stop spindle (M3/M5)"
msgstr "Encender/Apagar el motor spindle (M3/M5)"

#: bCNC/TerminalPage.py:140
msgid "Startup"
msgstr "Inicio"

#: bCNC/ControlPage.py:1877 bCNC/TerminalPage.py:110
msgid "State"
msgstr "Estado"

#: bCNC/ControlPage.py:611 bCNC/ControlPage.py:918
#, fuzzy
#| msgid "State: {}"
msgid "State: {}"
msgstr "Estado: {}"

#: bCNC/EditorPage.py:750 bCNC/bmain.py:1133
msgid "Statistics"
msgstr "Estadísticas"

#: bCNC/ControlPage.py:236
msgid "Status:"
msgstr "Estado:"

#: bCNC/ProbePage.py:1234
msgid "Step"
msgstr "Paso"

#: bCNC/plugins/zigzag.py:109
#, fuzzy
msgid "Step distance"
msgstr "Desplazamiento:"

#: bCNC/ControlPage.py:1562
#, fuzzy
#| msgid "Step for Z move operation"
msgid "Step for A move operation"
msgstr "Distancia (paso) para los desplazamientos en Z"

#: bCNC/ControlPage.py:1090
msgid "Step for Z move operation"
msgstr "Distancia (paso) para los desplazamientos en Z"

#: bCNC/ControlPage.py:1075 bCNC/ControlPage.py:1546
msgid "Step for every move operation"
msgstr "Distancia (paso) para los desplazamientos"

#: bCNC/ControlPage.py:1265 bCNC/ControlPage.py:1739
#, fuzzy
#| msgid "Step: %g"
msgid "Step: {:g}"
msgstr "Paso: {:g}"

#: bCNC/ControlPage.py:1744
#, fuzzy
#| msgid "Step: %g    Zstep:%g "
msgid "Step: {:g}   Astep:{:g} "
msgstr "Paso: {:g}    Paso Z:{:g} "

#: bCNC/ControlPage.py:1270
#, fuzzy
#| msgid "Step: %g    Zstep:%g "
msgid "Step: {:g}  Zstep: {:g} "
msgstr "Paso: {:g}    Paso Z:{:g} "

#: bCNC/ToolsPage.py:718
msgid "Stepover %"
msgstr "% de avance"

#: bCNC/plugins/spiral.py:480
msgid "Stepover (spiral=mm, lines=deg)"
msgstr ""

#: bCNC/ToolsPage.py:1405
msgid "Stock"
msgstr "Sustrato"

#: bCNC/ToolsPage.py:1413
msgid "Stock material currently on machine"
msgstr "Trozo de material a desbastar, actualmente colocado en la máquina"

#: bCNC/ControlPage.py:208 bCNC/FilePage.py:251
msgid "Stop"
msgstr "Detener"

#: bCNC/ControlPage.py:2161
msgid "Stop cooling (M9)"
msgstr ""

#: bCNC/FilePage.py:259
msgid "Stop pendant"
msgstr "Detener servidor web"

#: bCNC/ToolsPage.py:741 bCNC/ToolsPage.py:766
msgid "Surface Z"
msgstr "Z de la superficie"

#: bCNC/ProbePage.py:1543
msgid "Switch To"
msgstr ""

#: bCNC/ProbePage.py:1553
msgid "Switch between camera and spindle"
msgstr ""

#: bCNC/ControlPage.py:1904
#, fuzzy
#| msgid "Switch to workspace {}"
msgid "Switch to workspace {}"
msgstr "Cambiar al eje relativo {}"

#: bCNC/ProbePage.py:347
msgid "TLO"
msgstr "TLO"

#: bCNC/ControlPage.py:2029
msgid "TLO:"
msgstr ""

#: bCNC/ToolsPage.py:1638
msgid "Tabs"
msgstr "Pestañas"

#: bCNC/plugins/trochoidPath.py:63
#, fuzzy
#| msgid "Diameter"
msgid "Tabs Diameter"
msgstr "Diámetro"

#: bCNC/plugins/trochoidPath.py:64
#, fuzzy
#| msgid "Height"
msgid "Tabs Height"
msgstr "Altura"

#: bCNC/ToolsPage.py:1153
msgid "Tabs error"
msgstr "Error al generar pestañas"

#: bCNC/plugins/driller.py:46 bCNC/plugins/spirograph.py:122
#: bCNC/plugins/trochoidPath.py:53 bCNC/plugins/trochoidal_3D.py:82
#: bCNC/ToolsPage.py:767 bCNC/ToolsPage.py:939
msgid "Target Depth"
msgstr "Profundidad deseada (en blanco para grosor total)"

#: bCNC/plugins/simpleDrill.py:69
msgid "Target z (negative under surface)"
msgstr ""

#: bCNC/TerminalPage.py:38 bCNC/TerminalPage.py:189
msgid "Terminal"
msgstr "Terminal"

#: bCNC/TerminalPage.py:205
#, fuzzy
msgid "Terminal communication with controller"
msgstr "Controles de la CNC"

#: bCNC/plugins/text.py:94
#, fuzzy
#| msgid "Text abort: That embarrassing, I can't read this font file!"
msgid "Text abort: That's embarrassing, I can't read this font file!"
msgstr "Error: No se puede leer el fichero fuente especificado"

#: bCNC/plugins/text.py:62
msgid "Text abort: please input a Font size > 0"
msgstr "Error: El tamaño de fuente debe ser positivo"

#: bCNC/plugins/text.py:65
msgid "Text abort: please select a font file"
msgstr "Error: Debe especificar un fichero de fuente (extensión .TTF)"

#: bCNC/plugins/text.py:38
msgid "Text to generate"
msgstr "Texto a generar"

#: bCNC/Utils.py:573
#, fuzzy
#| msgid "The following report is about to be send to the author of {}"
msgid "The following report is about to be send to the author of {}"
msgstr "Se va a enviar este informe de error al autor de {}"

#: bCNC/plugins/sketch.py:239
msgid "The maximum illumination shouldn't be more than 250!"
msgstr ""

#: bCNC/Utils.py:695
msgid "There was a problem connecting to the web site"
msgstr ""
"Hubo un problema en la conexión. Rogamos envíe sus comentarios a través de "
"la web del programa"

#: bCNC/ToolsPage.py:742
msgid "Thickness"
msgstr "Grosor"

#: bCNC/plugins/Helical_Descent.py:57
msgid "This is my Helical Descent"
msgstr ""

#: bCNC/plugins/tile.py:13
msgid "Tile"
msgstr "Replicar"

#: bCNC/plugins/Random.py:44 bCNC/plugins/simpleRotate.py:48
#: bCNC/plugins/simpleTranslate.py:46 bCNC/plugins/tile.py:44
msgid "Tile error"
msgstr "Error al replicar"

#: bCNC/plugins/Random.py:80 bCNC/plugins/tile.py:85
msgid "Tiled selected blocks"
msgstr "Generado: REPLICACIÓN"

#: bCNC/bmain.py:1222 bCNC/bmain.py:1301
msgid "Time:"
msgstr "Duración:"

#: bCNC/CNCCanvas.py:2437
#, fuzzy
msgid "Timeout:"
msgstr "Duración:"

#: bCNC/CNCCanvas.py:2347
msgid "Toggle display of axes"
msgstr "Dibujar los ejes de referencia"

#: bCNC/CNCCanvas.py:2418
#, fuzzy
msgid "Toggle display of camera"
msgstr "Dibujar los ejes de referencia"

#: bCNC/CNCCanvas.py:2357
msgid "Toggle display of grid lines"
msgstr "Dibujar la rejilla"

#: bCNC/CNCCanvas.py:2367
msgid "Toggle display of margins"
msgstr "Dibujar los límites del g-code cargado"

#: bCNC/CNCCanvas.py:2388
msgid "Toggle display of paths (G1,G2,G3)"
msgstr "Dibujar los trazos de trabajo (G1,G2,G3)"

#: bCNC/CNCCanvas.py:2378
msgid "Toggle display of probe"
msgstr "Dibujar los datos de auto-nivelado"

#: bCNC/CNCCanvas.py:2398
msgid "Toggle display of rapid motion (G0)"
msgstr "Dibujar los desplazamientos rápidos (G0)"

#: bCNC/CNCCanvas.py:2408
msgid "Toggle display of workarea"
msgstr "Dibujar los límites del área de trabajo"

#: bCNC/EditorPage.py:316
msgid "Toggle enable/disable block of g-code [Ctrl-L]"
msgstr "Habilitar/Deshabilitar bloque de código seleccionado [Ctrl-L]"

#: bCNC/EditorPage.py:359
msgid "Toggle expand/collapse blocks of gcode [Ctrl-E]"
msgstr "Expandir/Colapsar bloques de código [Ctrl-E]"

#: bCNC/ToolsPage.py:1662
msgid "Toggle island"
msgstr ""

#: bCNC/ProbePage.py:158
msgid "Tool"
msgstr "Herramienta"

#: bCNC/Utils.py:852
msgid "Tool Tip:"
msgstr "Descripción:"

#: bCNC/ProbePage.py:1979
msgid "Tool change policy"
msgstr "Política de cambio de herramienta"

#: bCNC/ControlPage.py:2041
msgid "Tool length offset [G43.1#]"
msgstr ""

#: bCNC/ControlPage.py:1957
msgid "Tool number [T#]"
msgstr "Número de herramienta [T#]"

#: bCNC/ProbePage.py:2127
msgid "Tool probe height"
msgstr "Altura de la herramienta medida"

#: bCNC/CNCCanvas.py:2273 bCNC/ControlPage.py:1950
msgid "Tool:"
msgstr "Herramienta:"

#: bCNC/Utils.py:857
msgid "Tooltip for button"
msgstr "Breve descripción que aparece al pasar con el ratón sobre el botón"

#: bCNC/EditorPage.py:462
msgid "Top"
msgstr "Superior"

#: bCNC/EditorPage.py:459
msgid "Top-Left"
msgstr ""

#: bCNC/EditorPage.py:465
msgid "Top-Right"
msgstr ""

#: bCNC/EditorPage.py:558
msgid "Transform"
msgstr "Transformar"

#: bCNC/plugins/simpleTranslate.py:19
msgid "Translates a block to a new position"
msgstr ""

#: bCNC/ToolsPage.py:647
msgid "Travel x"
msgstr "Longitud eje X"

#: bCNC/ToolsPage.py:648
msgid "Travel y"
msgstr "Longitud eje Y"

#: bCNC/ToolsPage.py:649
msgid "Travel z"
msgstr "Longitud eje Z"

#: bCNC/plugins/trochoidal_3D.py:47
msgid "Trochoid Cut Diameter"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:402
msgid "Trochoid Cut Diameter has to be greater than End mill"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:1672
msgid "Trochoid Generated"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:397
#, fuzzy
#| msgid "Driller abort: Please select some path"
msgid "Trochoid abort: Please select some path"
msgstr "Error: Debe seleccionar algún trazo sobre el que generar el taladrado"

#: bCNC/plugins/trochoidal.py:52
msgid "Trochoid diameter (<= cutter D)"
msgstr ""

#: bCNC/plugins/trochoidal.py:49
msgid "Trochoid entry (prepare for helicut)"
msgstr ""

#: bCNC/plugins/trochoidal.py:18
msgid "Trochoidal"
msgstr ""

#: bCNC/plugins/trochoidPath.py:20
msgid "Trochoidal Path"
msgstr ""

#: bCNC/plugins/trochoidal.py:24
msgid "Trochoidal g-code postprocessor"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:30
msgid "Trochoidcut"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:50
msgid "Trochoids Advance"
msgstr ""

#: bCNC/ProbePage.py:1568
msgid "Turn on/off edge detection"
msgstr ""

#: bCNC/ProbePage.py:1583
msgid "Turn on/off freeze image"
msgstr ""

#: bCNC/lib/bFileDialog.py:305 bCNC/ToolsPage.py:709
msgid "Type"
msgstr "Tipo"

#: bCNC/plugins/trochoidal_3D.py:56
msgid "Type of Splice"
msgstr ""

#: bCNC/plugins/drillmark.py:56
msgid "Type of the mark"
msgstr ""

#: bCNC/Ribbon.py:604
msgid "Undo [Ctrl-Z]"
msgstr "Deshacer [Ctrl-Z]"

#: bCNC/ToolsPage.py:636
msgid "Units (inches)"
msgstr "Unidades (pulgadas)"

#: bCNC/ControlPage.py:1942
msgid "Units [G20, G21]"
msgstr "Unidades [G20, G21]"

#: bCNC/ControlPage.py:1931
msgid "Units:"
msgstr "Unidades:"

#: bCNC/ControlPage.py:108
msgid "Unlock"
msgstr "Desbloquear"

#: bCNC/ControlPage.py:115
msgid "Unlock controller [$X]"
msgstr "Desbloquear la máquina [$X]"

#: bCNC/EditorPage.py:128
msgid "Unselect all blocks [Ctrl-Shift-A]"
msgstr "De-seleccionar todo [Ctrl-Shift-A]"

#: bCNC/controllers/_GenericGRBL.py:60
msgid "Unsupported or invalid g-code command found in block."
msgstr ""

#: bCNC/EditorPage.py:505
msgid "Up"
msgstr "Subir"

#: bCNC/FilePage.py:203
msgid "Updates"
msgstr "Actualizaciones"

#: bCNC/plugins/heightmap.py:56
#, fuzzy
#| msgid "Use a brightess map to create a variable Z path"
msgid "Use a brightness map to create a variable Z path"
msgstr ""
"Lee una imagen e interpreta los niveles de brillo como alturas, generando "
"una ruta de maquinado 3D"

#: bCNC/plugins/driller.py:49
msgid "Use anchor"
msgstr ""

#: bCNC/ToolsPage.py:1921
msgid "User File"
msgstr "Fichero de configuración del usuario"

#: bCNC/Utils.py:823
msgid "User configurable button"
msgstr "Botón configurable por el usuario"

#: bCNC/ToolsPage.py:1981
msgid "Value"
msgstr "Valor"

#: bCNC/controllers/_GenericGRBL.py:107
msgid "Value out of range. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:118
msgid "Value word conflict. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:627
msgid "Vertical"
msgstr "Ref. Vertical"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
#, fuzzy
#| msgid "WARNING: {}"
msgid "WARNING: {}"
msgstr "AVISO: {}"

#: bCNC/ControlPage.py:261 bCNC/ProbePage.py:765
msgid "WPos:"
msgstr "WPos:"

#: bCNC/bmain.py:2524
msgid "Warning"
msgstr "Aviso"

#: bCNC/plugins/function_plot.py:110
#, fuzzy
#| msgid "Warning"
msgid "Warning: "
msgstr "Aviso"

#: bCNC/ToolsPage.py:621
msgid "Web Camera"
msgstr ""

#: bCNC/ToolsPage.py:624
msgid "Web Camera Angle"
msgstr ""

#: bCNC/ToolsPage.py:623
msgid "Web Camera Height"
msgstr ""

#: bCNC/ToolsPage.py:622
msgid "Web Camera Width"
msgstr ""

#: bCNC/plugins/box.py:348
msgid "Width Dx"
msgstr "Ancho Dx"

#: bCNC/plugins/flatten.py:291
msgid "Width to flatten"
msgstr "Dist. X de aplanado"

#: bCNC/ProbePage.py:149
msgid "Work surface camera view and alignment"
msgstr ""

#: bCNC/plugins/halftone.py:52 bCNC/plugins/sketch.py:43
#: bCNC/plugins/text.py:39 bCNC/plugins/heightmap.py:65
#: bCNC/plugins/pyrograph.py:39
msgid "Working Depth"
msgstr "Profundidad de trabajo"

#: bCNC/plugins/simpleDrill.py:67 bCNC/ControlPage.py:1015
msgid "X"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:80
msgid "X Initial"
msgstr ""

#: bCNC/plugins/scaling.py:39
msgid "X Scale"
msgstr ""

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/scaling.py:42
#, fuzzy
#| msgid "Center"
msgid "X Y Center"
msgstr "Centrar"

#: bCNC/ProbePage.py:1275
msgid "X bins"
msgstr "N⁰ de puntos en X"

#: bCNC/plugins/function_plot.py:30
#, fuzzy
msgid "X dimension"
msgstr "Radio interno"

#: bCNC/ProbePage.py:1255
msgid "X maximum"
msgstr "X máximo"

#: bCNC/ProbePage.py:1247
msgid "X minimum"
msgstr "X mínimo"

#: bCNC/plugins/function_plot.py:32
msgid "X number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:289 bCNC/plugins/spiral.py:472
msgid "X start"
msgstr "X inicial"

#: bCNC/ProbePage.py:1263
msgid "X step"
msgstr "Paso en X"

#: bCNC/ControlPage.py:274
msgid "X work position (click to set)"
msgstr "Posición de trabajo X (click para definirla)"

#: bCNC/ProbePage.py:1241
msgid "X:"
msgstr ""

#: bCNC/ControlPage.py:349
msgid "X=0"
msgstr ""

#: bCNC/ControlPage.py:399
msgid "XY=0"
msgstr ""

#: bCNC/ControlPage.py:415
msgid "XYZ=0"
msgstr ""

#: bCNC/plugins/simpleDrill.py:68 bCNC/ControlPage.py:936
msgid "Y"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:81
msgid "Y Initial"
msgstr ""

#: bCNC/plugins/scaling.py:40
msgid "Y Scale"
msgstr ""

#: bCNC/ProbePage.py:1315
msgid "Y bins"
msgstr "N⁰ de puntos en Y"

#: bCNC/plugins/function_plot.py:31
#, fuzzy
msgid "Y dimension"
msgstr "Radio interno"

#: bCNC/ProbePage.py:1295
msgid "Y maximum"
msgstr "Y máximo"

#: bCNC/ProbePage.py:1287
msgid "Y minimum"
msgstr "Y mínimo"

#: bCNC/plugins/function_plot.py:33
msgid "Y number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:290 bCNC/plugins/spiral.py:473
msgid "Y start"
msgstr "Y inicial"

#: bCNC/ProbePage.py:1303
msgid "Y step"
msgstr "Paso en Y"

#: bCNC/ControlPage.py:290
msgid "Y work position (click to set)"
msgstr "Posición de trabajo Y (click para definirla)"

#: bCNC/ProbePage.py:1281
msgid "Y:"
msgstr ""

#: bCNC/ControlPage.py:365
msgid "Y=0"
msgstr ""

#: bCNC/ToolsPage.py:1154
msgid "You cannot have both the number of tabs or distance equal to zero"
msgstr ""
"No puedes poner a cero simultáneamente el número de pestañas y la distancia "
"entre ellas"

#: bCNC/ToolsPage.py:807
msgid "You should probably always use 'on path', unless you are threadmilling!"
msgstr ""

#: bCNC/Utils.py:591
msgid "Your email"
msgstr "Tu email"

#: bCNC/ControlPage.py:933 bCNC/ProbePage.py:462
msgid "Z"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:88
msgid "Z Feed Multiplier"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:82
msgid "Z Initial"
msgstr ""

#: bCNC/ProbePage.py:1328
msgid "Z Minimum depth to scan"
msgstr ""
"Profundidad a escanear (valor negativo). Dará error si la sonda no hace "
"contacto en éste rango."

#: bCNC/plugins/scaling.py:41
msgid "Z Scale"
msgstr ""

#: bCNC/plugins/simpleDrill.py:72
msgid "Z feed for drilling"
msgstr ""

#: bCNC/ProbePage.py:1336
msgid "Z safe to move"
msgstr ""
"Altura de desplazamiento (valor positivo). Debe estar libre de obstáculos"

#: bCNC/plugins/spiral.py:474
#, fuzzy
#| msgid "X start"
msgid "Z start"
msgstr "X inicial"

#: bCNC/ControlPage.py:306
msgid "Z work position (click to set)"
msgstr "Posición de trabajo Z (click para definirla)"

#: bCNC/ProbePage.py:1322
msgid "Z:"
msgstr ""

#: bCNC/ControlPage.py:381
msgid "Z=0"
msgstr ""

#: bCNC/ProbePage.py:202
msgid "Zero"
msgstr "Referencia Z=0"

#: bCNC/plugins/zigzag.py:15
msgid "Zig-Zag"
msgstr ""

#: bCNC/plugins/zigzag.py:148
#, fuzzy
msgid "Zig-Zag abort: depth must be minor or equal to zero"
msgstr "Error: La profundidad debe ser un valor negativo"

#: bCNC/plugins/zigzag.py:143
msgid "Zig-Zag abort: verify CornerRes >= 0"
msgstr ""

#: bCNC/plugins/zigzag.py:135
msgid "Zig-Zag abort: verify LineLen > 0"
msgstr ""

#: bCNC/plugins/zigzag.py:131
msgid "Zig-Zag abort: verify Nlines > 0"
msgstr ""

#: bCNC/plugins/zigzag.py:139
msgid "Zig-Zag abort: verify Step > 0"
msgstr ""

#: bCNC/CNCCanvas.py:2256
msgid "Zoom In [Ctrl-=]"
msgstr "Acercar [Ctrl-=]"

#: bCNC/CNCCanvas.py:2263
msgid "Zoom Out [Ctrl--]"
msgstr "Alejar [Ctrl--]"

#: bCNC/ControlPage.py:1400
#, fuzzy
#| msgid "Control"
msgid "abcControl"
msgstr "Control"

#: bCNC/ControlPage.py:625
msgid "abcDRO"
msgstr ""

#: bCNC/ControlPage.py:637
#, fuzzy
#| msgid "WPos:"
msgid "abcWPos:"
msgstr "WPos:"

#: bCNC/plugins/endmilloffset.py:479
msgid "acts like a tool corrector inside the profile"
msgstr ""

#: bCNC/plugins/simpleRotate.py:28
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "angle step (degrees)"
msgstr "Resolución (grados)"

#: bCNC/plugins/dragknife.py:54
msgid "angle threshold"
msgstr ""

#: bCNC/plugins/arcfit.py:38
msgid "arc precision (mm)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:12
msgid "bCNC is currently sending a gcode program to Grbl"
msgstr "bCNC está enviando un g-code a la tarjeta controladora con Grbl"

#: bCNC/plugins/simpleRectangle.py:97
msgid "clockwise"
msgstr ""

#: bCNC/plugins/dragknife.py:48
msgid "distance from dragknife rotation center to the tip of the blade"
msgstr ""

#: bCNC/plugins/dragknife.py:55
msgid "do not perform pivot action for angles smaller than this"
msgstr ""

#: bCNC/plugins/dragknife.py:47
msgid "dragknife offset"
msgstr ""

#: bCNC/CNCCanvas.py:678
#, fuzzy
#| msgid "dx=%g  dy=%g  dz=%g  length=%g  angle=%g"
msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgstr "dx={:g}  dy={:g}  dz={:g}  longitud={:g}  ángulo={:g}"

#: bCNC/plugins/dragknife.py:75
msgid "feedrate"
msgstr ""

#: bCNC/ProbePage.py:2047 bCNC/ProbePage.py:2092
msgid "get"
msgstr "actual"

#: bCNC/plugins/arcfit.py:39
msgid "how precisely must arc fit. set to 0 to disable arc fitting"
msgstr ""

#: bCNC/plugins/endmilloffset.py:463
msgid "indicates the direction (inside / outside) for making profiles"
msgstr ""

#: bCNC/plugins/dragknife.py:70
msgid "initial direction"
msgstr ""

#: bCNC/plugins/slicemesh.py:83
msgid "layer height (0 = only single zmin)"
msgstr ""

#: bCNC/plugins/arcfit.py:45
msgid "line precision (mm)"
msgstr ""

#: bCNC/plugins/slicemesh.py:88
msgid "maximum Z height"
msgstr ""

#: bCNC/plugins/arcfit.py:50
msgid "minimal number of segments to create arc"
msgstr ""

#: bCNC/plugins/slicemesh.py:86
msgid "minimum Z height"
msgstr ""

#: bCNC/plugins/simpleRotate.py:29 bCNC/plugins/simpleTranslate.py:28
msgid "nb repeat including original"
msgstr ""

#: bCNC/CNCCanvas.py:627
#, fuzzy
#| msgid "origin %g %g %g"
msgid "origin {:g} {:g} {:g}"
msgstr "origen {:g} {:g} {:g}"

#: bCNC/bmain.py:3097
msgid "python serial missing"
msgstr "Falta el módulo pyserial"

#: bCNC/plugins/slicemesh.py:74
msgid "scale factor"
msgstr ""

#: bCNC/plugins/linearize.py:39
msgid "segment size"
msgstr ""

#: bCNC/ControlPage.py:1961 bCNC/ControlPage.py:2003 bCNC/ControlPage.py:2045
#: bCNC/ProbePage.py:360
msgid "set"
msgstr "definir"

#: bCNC/plugins/dragknife.py:80
msgid "simulate"
msgstr ""

#: bCNC/plugins/dragknife.py:89
msgid "simulation precision"
msgstr ""

#: bCNC/plugins/slicemesh.py:23
msgid "slicemesh"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:101
msgid "splice steps every 360 degrees"
msgstr ""

#: bCNC/plugins/linearize.py:48
msgid "subdiv lines"
msgstr ""

#: bCNC/plugins/dragknife.py:61
msgid "swivel height"
msgstr ""

# I'd have to see this functionality in action to be able to describe it properly
#: bCNC/Sender.py:355
msgid "unknown command"
msgstr "Comando desconocido"

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/simpleRotate.py:26
#, fuzzy
#| msgid "Center"
msgid "x center"
msgstr "Centrar"

#: bCNC/plugins/simpleTranslate.py:26
#, fuzzy
#| msgid "Depth Increment"
msgid "x increment"
msgstr "Profundidad de pasada"

#: bCNC/plugins/simpleLine.py:52 bCNC/plugins/simpleRectangle.py:93
msgid "xEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:51 bCNC/plugins/simpleRectangle.py:92
#, fuzzy
#| msgid "Start"
msgid "xStart"
msgstr "Comenzar"

# This needs to have text, since the word Center is used for the probing menu as well
#: bCNC/plugins/simpleRotate.py:27
#, fuzzy
#| msgid "Center"
msgid "y center"
msgstr "Centrar"

#: bCNC/plugins/simpleTranslate.py:27
#, fuzzy
#| msgid "Depth Increment"
msgid "y increment"
msgstr "Profundidad de pasada"

#: bCNC/plugins/simpleLine.py:54 bCNC/plugins/simpleRectangle.py:95
msgid "yEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:53 bCNC/plugins/simpleRectangle.py:94
#, fuzzy
#| msgid "Start"
msgid "yStart"
msgstr "Comenzar"

#: bCNC/plugins/slicemesh.py:77
msgid "z offset"
msgstr ""

#~ msgid ""
#~ "ERROR: Please install the python pyserial module\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tor yum install python-serial\n"
#~ "\tor dnf install python-pyserial"
#~ msgstr ""
#~ "ERROR: Por favor, instale el módulo de python \"pyserial\"\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tor yum install python-serial\n"
#~ "\tor dnf install python-pyserial"

#~ msgid "Color configuration"
#~ msgstr "Configuración de color"

#~ msgid "Font configuration"
#~ msgstr "Configuración de estilo de fuente"

#~ msgid "Tools"
#~ msgstr "Herramientas"

#~ msgid "Probe X/Y axis by using a set square probe"
#~ msgstr "Cuadrar los ejes X e Y con una esquina de 90⁰"

#~ msgid "Perform a center probe cycle"
#~ msgstr "Realizar un ciclo de centrado"

#~ msgid ""
#~ "ERROR: Please install the python pyserial module\n"
#~ "Windows: C:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Linux: sudo apt-get or yum install python-serial"
#~ msgstr ""
#~ "ERROR: Es necesario instalar el módulo de Python \"pyserial\"\n"
#~ "En Windows: C:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "En Linux: sudo apt-get (ó yum install) python-serial"

#~ msgid "Draw a square tab"
#~ msgstr "Dibujar una pestaña cuadrada"

#~ msgid "Place origin with the mouse on canvas [O]"
#~ msgstr "Definir el origen haciendo click con el ratón [O]"

#~ msgid "Reverse direction of selected gcode blocks"
#~ msgstr "Invertir el contenido de cada bloque seleccionado"

#~ msgid "Macros"
#~ msgstr "Generadores"

#~ msgid "Probe is not zeroed"
#~ msgstr "La sonda no ha sido iniciada a cero"

#~ msgid "Please ZERO any location of the probe before starting a run"
#~ msgstr ""
#~ "Debe PONER A CERO la ubicación de la sonda antes de comenzar la ejecución"
