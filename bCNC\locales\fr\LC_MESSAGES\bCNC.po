# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-19 19:55+0200\n"
"PO-Revision-Date: 2022-07-19 20:20+0200\n"
"Last-Translator: @SteveMoto [GitHub]\n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.0.1\n"

#: bCNC/controllers/_GenericGRBL.py:51
msgid ""
"(Compile Option) Grbl '$' setting value exceeds the maximum step rate "
"supported."
msgstr ""
"(Option compilation) Le réglage de la valeur Grbl '$' dépasse la vitesse "
"d'impulsion maximum supportée."

#: bCNC/controllers/_GenericGRBL.py:55
msgid ""
"(Grbl-Mega Only) Build info or startup line exceeded EEPROM line length "
"limit."
msgstr ""
"(Grbl-Mega seulement) Info de build ou ligne de début dépassant la longueur "
"limite de ligne de l'EEPROM."

#: bCNC/controllers/_GenericGRBL.py:69
msgid ""
"A G-code command implicitly or explicitly requires XYZ axis words in the "
"block, but none were detected."
msgstr ""
"Une commande G-code demande implicitement ou explicitement des mots sur les "
"axes XYZ dans le bloc, mais aucun n'a été détectée."

#: bCNC/controllers/_GenericGRBL.py:75
msgid ""
"A G-code command was sent, but is missing some required P or L value words "
"in the line."
msgstr ""
"Une commande G-Code a été envoyée, mais il manque une valeur P ou L dans la "
"ligne."

#: bCNC/controllers/_GenericGRBL.py:87
msgid ""
"A G2 or G3 arc was commanded but there are no XYZ axis words in the selected "
"plane to trace the arc."
msgstr ""
"Un arc G2 ou G3 a été commandé mais il n'y a pas de mots d'axes dans le plan "
"sélectionné pour tracer l'arc."

#: bCNC/controllers/_GenericGRBL.py:96
msgid ""
"A G2 or G3 arc, traced with the offset definition, is missing the IJK offset "
"word in the selected plane to trace the arc."
msgstr ""
"Un arc G2 ou G3, tracé avec la définition de décalage, n'a pas le mot de "
"décalage IJK dans le plan sélectionné pour tracer l'arc."

#: bCNC/controllers/_GenericGRBL.py:93
msgid ""
"A G2 or G3 arc, traced with the radius definition, had a mathematical error "
"when computing the arc geometry. Try either breaking up the arc into semi-"
"circles or quadrants, or redefine them with the arc offset definition."
msgstr ""
"Un arc G2 ou G3, tracé avec le rayon défini, retourne une erreur "
"mathématique lors du calcul de la géométrie de l'arc. Essayez soit de casser "
"l'arc en demi-cercles ou quart de cercles, ou redéfinissez-les en "
"définissant le décalage d'arc."

#: bCNC/ToolsPage.py:824
msgid ""
"Add additional margin/offset around islands to compensate for endmill "
"radius. This is automaticaly done for all islands if they are marked as tabs."
msgstr ""

#: bCNC/ProbePage.py:714
msgid ""
"Add an orientation marker. Jog first the machine to the marker position and "
"then click on canvas to add the marker."
msgstr ""
"Ajouter un marqueur d'orientation. Déplacer d'abord la machine vers la "
"position du marqueur, puis cliquer sur l'écran pour ajouter le marqueur."

#: bCNC/controllers/_GenericGRBL.py:16
msgid ""
"Alarm is an emergency state. Something has gone terribly wrong when these "
"occur. Typically, they are caused by limit error when the machine has moved "
"or wants to move outside the machine space and crash into something. They "
"also report problems if Grbl is lost and can't guarantee positioning or a "
"probe command has failed. Once in alarm-mode, Grbl will lock out and shut "
"down everything until the user issues a reset. Even after a reset, Grbl will "
"remain in alarm-mode, block all G-code from being executed, but allows the "
"user to override the alarm manually. This is to ensure the user knows and "
"acknowledges the problem and has taken steps to fix or account for it."
msgstr ""
"Alarme est un état d'urgence : quelque chose ne s'est pas du tout bien passé."
"C'est typique d'une erreur causée par un dépassement de limite lors du "
"déplacement de la machine hors de son espace et qu'elle a buté contre "
"quelque chose. Ça  peur aussi indiquer que Grbl est perdu et qu'il ne sait "
"pas où il se situe ou  qu'une commande avec la sonde a échouée. Une fois "
"dans ce mode d'urgence, Grbl verrouille tout et s'éteint jusqu'à ce que vous "
"fassiez un reset. Même après ça, Grbl reste dans ce mode, empêche "
"l'exécution de tout gcode, mais il permet de désactiver manuellement cette "
"alarme. Ceci afin que l'utilisateur sache exactement ce qu'il en ait et "
"qu'il agisse en connaissance de cause."

#: bCNC/controllers/_GenericGRBL.py:33
msgid ""
"All is good! Everything in the last line was understood by Grbl and was "
"successfully processed and executed."
msgstr ""
"Impeccable ! Tout a été compris par Grbl et a été correctement exécuté."

#: bCNC/plugins/linearize.py:50
msgid ""
"Also subdivide the lines. Otherwise only arcs and splines will be subdivided"
msgstr ""

#: bCNC/bmain.py:2418
msgid ""
"Autolevel/probe information already exists.\n"
"Delete it?"
msgstr ""
"Information d'autonivelage/sondage déjà existante.\n"
"L'effacer ?"

#: bCNC/plugins/slicemesh.py:276
msgid ""
"Calculating distance {} of {} (SciPy not installed => using SLOW fallback "
"method)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:505
msgid ""
"Currently there is some weird behaviour sometimes when trying to link "
"segments of pocket internally, so it can be disabled using this option. This "
"workaround should be fixed and removed in future."
msgstr ""

#: bCNC/ToolsPage.py:797
msgid ""
"Do the last cut once more in opposite direction. Helix bottom is disabled in "
"such case."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:168
msgid ""
"Door closed and resuming. Restoring from park, if applicable. Reset will "
"throw an alarm."
msgstr ""
"Porte fermée et reprise. Récupération à partir du park, si applicable. Une "
"ré-initialisation déclenchera une alarme."

#: bCNC/controllers/_GenericGRBL.py:165
msgid ""
"Door opened. Hold (or parking retract) in-progress. Reset will throw an "
"alarm."
msgstr ""
"Porte ouverte. Pause (ou retour au parc) en cours. Une ré-initialisation "
"déclenchera une alarme."

#: bCNC/controllers/_GenericGRBL.py:131
msgid ""
"G-code motion target exceeds machine travel. Machine position safely "
"retained. Alarm may be unlocked."
msgstr ""
"La cible fixée par le mouvement G-code est en dehors des zones de "
"déplacement de la machine. Position de la machine conservée par sécurité. "
"L'alarme peut être déverrouillée."

#: bCNC/bmain.py:2526
#, fuzzy
#| msgid ""
#| "Gcode file {} was changed since editing started\n"
#| "Reload new version?"
msgid ""
"Gcode file {} was changed since editing started\n"
"Reload new version?"
msgstr ""
"Le fichier G-Code {} a été modifié depuis le début de l'édition\n"
"Recharger la nouvelle version ?"

#: bCNC/ToolsPage.py:1027
msgid ""
"Generate pocket after profiling? Useful for making pockets with overcuts."
msgstr ""

#: bCNC/plugins/sketch.py:368
#, fuzzy
#| msgid "Generated Sketch size W={} x H={} x distance={}, Total length:{}"
msgid ""
"Generated Sketch size W={} x H={} x distance={}, Total line:{}, Total length:"
"{}"
msgstr ""
"Taille générée de l'Esquisse W={} x H={} x distance={}, Longueur Totale :{}"

#: bCNC/controllers/_GenericGRBL.py:43
msgid ""
"Grbl '$' command cannot be used unless Grbl is IDLE. Ensures smooth "
"operation during a job."
msgstr ""
"La commande Grbl '$' ne peut être utilisée sauf si Grbl est dans l'état IDLE "
"(en attente). Ceci permet d'éviter les problèmes durant un travail. "

#: bCNC/controllers/_GenericGRBL.py:19
msgid ""
"Grbl is in g-code check mode. If you send g-code to it, it will only check "
"it without actualy doing any motion. You can exit this by $C command (Or "
"equivalent button in terminal tab)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:26
msgid ""
"Grbl is in queue state. This also means you have relatively old GRBL "
"version, there are even 0.9 versions newer than this."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:23
msgid ""
"Grbl is in sleep mode. Motors are disabled, so you can move them manualy. "
"That also means that your machine might have lost the position (or "
"microsteps) and you may need to re-zero. Perform reset+unlock (or stop) to "
"wake Grbl again."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:78
msgid ""
"Grbl supports six work coordinate systems G54-G59. G59.1, G59.2, and G59.3 "
"are not supported."
msgstr ""
"Grbl ne supporte que 6 systèmes de coordonnées de travail de G54 à G59. Ne "
"sont pas supportées G59.1, G59.2 et G59.3."

#: bCNC/controllers/_GenericGRBL.py:128
msgid ""
"Hard limit triggered. Machine position is likely lost due to sudden and "
"immediate halt. Re-homing is highly recommended."
msgstr ""
"Fin de course déclenchée. La position de la machine a certainement dû être "
"perdue à cause d'un arrêt brusque et immédiat. Refaire un Homing est "
"fortement recommandé."

#: bCNC/controllers/_GenericGRBL.py:148
msgid ""
"Homing fail. Could not find limit switch within search distance. Defined as "
"1.5 * max_travel on search and 5 * pulloff on locate phases."
msgstr ""
"Échec du Homing. Fin de course non trouvée dans la zone de recherche. Réglée "
"à 1.5*déplacement_maxi  pour chercher et 5* pulloff dans les phases "
"d'approche."

#: bCNC/controllers/_GenericGRBL.py:158
#, fuzzy
#| msgid ""
#| "Homing fail. Could not find limit switch within search distance. Defined "
#| "as 1.5 * max_travel on search and 5 * pulloff on locate phases."
msgid ""
"Homing fail. Could not find second limit switch for auto squared axis within "
"search distances. Try increasing max travel, decreasing pull-off distance, "
"or check wiring. (grblHAL)"
msgstr ""
"Échec du Homing. Fin de course non trouvée dans la zone de recherche. Réglée "
"à 1.5*déplacement_maxi  pour chercher et 5* pulloff dans les phases "
"d'approche."

#: bCNC/controllers/_GenericGRBL.py:145
msgid ""
"Homing fail. Cycle failed to clear limit switch when pulling off. Try "
"increasing pull-off setting or check wiring."
msgstr ""
"Échec du Homing. Déclenchement de la fin de course échouée lors de "
"l'activation. Essayez d'augmenter le réglage de la fin de course ou vérifiez "
"le câblage."

#: bCNC/bmain.py:231
msgid ""
"MDI Command line: Accept g-code commands or macro commands (RESET/HOME...) "
"or editor commands (move,inkscape, round...) [Space or Ctrl-Space]"
msgstr ""
"Ligne de commande MDI : Accepte les commandes G-Code ou les macros (RESET/"
"HOME...) ou les commandes de l'éditeur (move, inkscape, round...) [Space or "
"Ctrl-Space]"

#: bCNC/plugins/linearize.py:41
msgid ""
"Maximal length of resulting lines, smaller number means more precise output "
"and longer g-code. Length will be automaticaly truncated to be even across "
"whole subdivided segment."
msgstr ""

#: bCNC/ControlPage.py:1045
msgid ""
"Move to Origin.\n"
"User configurable button.\n"
"Right click to configure."
msgstr ""
"Déplacer à l'origine.\n"
"Bouton Utilisateur Configurable.\n"
"Clic-Droit pour configurer."

#: bCNC/ControlPage.py:609 bCNC/ControlPage.py:916
msgid ""
"No info available.\n"
"Please contact the author."
msgstr ""
"Aucune information disponible.\n"
"Veuillez contacter l'auteur."

#: bCNC/plugins/endmilloffset.py:454
msgid ""
"Number of contours (Custom offset count) : indicates the number of contours "
"if custom selected. MAX:"
msgstr ""

#: bCNC/ProbePage.py:904
msgid ""
"Please verify that the probe is connected.\n"
"\n"
"Show this message again?"
msgstr ""
"Veuillez vérifiez que la sonde soit connectée. \n"
"\n"
"Montrer ce message à nouveau ?"

#: bCNC/controllers/_GenericGRBL.py:140
msgid ""
"Probe fail. Probe did not contact the workpiece within the programmed travel "
"for G38.2 and G38.4."
msgstr ""
"Échec de la sonde. La sonde n'a pas touché la pièce durant le déplacement "
"programmé par G38.2 et G38.4."

#: bCNC/controllers/_GenericGRBL.py:137
msgid ""
"Probe fail. The probe is not in the expected initial state before starting "
"probe cycle, where G38.2 and G38.3 is not triggered and G38.4 and G38.5 is "
"triggered."
msgstr ""
"Échec de la sonde. La sonde n'est pas dans l'état initial prévu avant "
"d'entamer l'opération, où G38.2 et G38.3 ne sont pas déclenchées et G38.4 et "
"G38.5 le sont."

#: bCNC/controllers/_GenericGRBL.py:134
msgid ""
"Reset while in motion. Grbl cannot guarantee position. Lost steps are "
"likely. Re-homing is highly recommended."
msgstr ""
"Ré-initialisation pendant le déplacement. Grbl ne peut garantir la position. "
"Des pas ont probablement été perdus. Refaire un Homing est fortement "
"recommandé."

#: bCNC/ProbePage.py:211
msgid ""
"Set current XY location as autoleveling Z-zero (recalculate probed data to "
"be relative to this XY origin point)"
msgstr ""

#: bCNC/ControlPage.py:251
msgid ""
"Show current state of the machine\n"
"Click to see details\n"
"Right-Click to clear alarm/errors"
msgstr ""
"Affiche l'état actuel de la machine\n"
"Cliquer pour voir les détails\n"
"Clic-Droit pour nettoyer les alarme/erreurs"

#: bCNC/plugins/dragknife.py:91
msgid ""
"Simulation is currently approximated by using lots of short lines. This is "
"the length of these lines."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:110
msgid ""
"Spindle not running when motion commanded in CSS or spindle sync mode. "
"(grblHAL)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:487 bCNC/plugins/endmilloffset.py:496
msgid ""
"Tabs are always ignored. You can select if all islands are active, none, or "
"only selected"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:102
msgid ""
"The G43.1 dynamic tool length offset command cannot apply an offset to an "
"axis other than its configured axis. The Grbl default axis is the Z-axis."
msgstr ""
"La  commande G43.1 de décalage de longueur d'outil dynamique (dynamic tool "
"length offset) ne peut appliquer un décalage sur un axe autre que celui pour "
"lequel il est configuré. L'axe par défaut dans Grbl est l'axe Z."

#: bCNC/controllers/_GenericGRBL.py:81
msgid ""
"The G53 G-code command requires either a G0 seek or G1 feed motion mode to "
"be active. A different motion was active."
msgstr ""
"La commande G-Code G53 nécessite soit un mouvement rapide G0 ou une "
"mouvement de travail G1 actif. Un mouvement différent était actif. "

#: bCNC/controllers/_GenericGRBL.py:90
msgid ""
"The motion command has an invalid target. G2, G3, and G38.2 generates this "
"error, if the arc is impossible to generate or if the probe target is the "
"current position."
msgstr ""
"La commande de mouvement a une cible non valable. G2, G3 et G38.2 génèrent "
"cette erreurs, si l'arc est impossible à générer ou si la cible de la sonde "
"est dans la position actuelle."

#: bCNC/controllers/_GenericGRBL.py:84
msgid ""
"There are unused axis words in the block and G80 motion mode cancel is "
"active."
msgstr ""
"Il y a des mots d’axes inutilisés dans le bloc et la révocation des codes de "
"déplacement modaux G80 est actif."

#: bCNC/controllers/_GenericGRBL.py:99
msgid ""
"There are unused, leftover G-code words that aren't used by any command in "
"the block."
msgstr ""
"Il y a des mots G-Code non reconnus ou abandonnés, inutilisés dans le bloc."

#: bCNC/Utils.py:709
#, fuzzy
#| msgid ""
#| "There was an error sending the report\n"
#| "Code={} {}"
msgid ""
"There was an error sending the report\n"
"Code={} {}"
msgstr ""
"Il y a eu une erreur lors de l'envoi du rapport\n"
"Code={} {}"

#: bCNC/plugins/endmilloffset.py:471
msgid ""
"This can be used to switch between Conventional and Climb milling. If unsure "
"use Convetional (default)."
msgstr ""

#: bCNC/plugins/drillmark.py:23
msgid ""
"This plugin is for creating drilling marks with a laser engraver\n"
"        for manual drilling"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:105
msgid ""
"Tool number greater than max supported value or undefined tool selected. "
"(grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:65
msgid ""
"Two G-code commands that both require the use of the XYZ axis words were "
"detected in the block."
msgstr ""
"Deux commandes G-Code qui requièrent toutes les 2 les mots d'axes XYZ ont "
"été détectées dans le bloc."

#: bCNC/plugins/dragknife.py:82
msgid ""
"Use this option to simulate cuting of dragknife path. Resulting shape will "
"reflect what shape will actuall be cut. This should reverse the dragknife "
"procedure and give you back the original shape from g-code that was "
"previously processed for dragknife."
msgstr ""

#: bCNC/bmain.py:2722
msgid ""
"\n"
"Would you like open it locally?"
msgstr ""
"\n"
"Voulez-vous l'ouvrir localement ?"

#: bCNC/bmain.py:888
msgid ""
"bCNC/\tAn advanced fully featured\n"
"\tg-code sender for GRBL."
msgstr ""
"bCNC/\tUn interpréteur complet avancé\n"
"\tde GCode pour GRBL."

#: bCNC/plugins/dragknife.py:72
msgid ""
"direction that knife blade is facing before and after cut. Eg.: if you set "
"this to X+, then the knifes rotation axis should be on the right side of the "
"tip. Meaning that the knife is ready to cut towards right immediately "
"without pivoting. If you cut multiple shapes in single operation, it's "
"important to have this set consistently across all of them."
msgstr ""

#: bCNC/plugins/arcfit.py:47
msgid ""
"how precisely must line fit. set to 0 to disable line fitting, but at least "
"some line fitting (0.001 to 0.01) might be needed to fix arcs, so they can "
"be fit"
msgstr ""

#: bCNC/plugins/endmilloffset.py:445
msgid ""
"indicates the number of profile passes (single,custom number,full pocket)"
msgstr ""

#: bCNC/ToolsPage.py:783
msgid ""
"positive value = relative to tool diameter (5 to 10 probably makes sense), "
"negative = absolute ramp distance (you probably don't need this). Also note "
"that ramp can't currently be shorter than affected g-code segment."
msgstr ""

#: bCNC/plugins/dragknife.py:63
msgid ""
"retract to this height for pivots (useful for thick materials, you should "
"enter number slightly lower than material thickness)"
msgstr ""

#: bCNC/CNCCanvas.py:2003
#, fuzzy
#| msgid "     line: {}\n"
msgid "     line: {}\n"
msgstr "     ligne : {}\n"

#: bCNC/bmain.py:1195 bCNC/bmain.py:1276
msgid "# Blocks:"
msgstr "# Blocs :"

#: bCNC/TerminalPage.py:182
msgid "$ Display build information of Grbl"
msgstr "$ Afficher les informations de version de Grbl"

#: bCNC/TerminalPage.py:103
msgid "$# Display parameters of Grbl"
msgstr "$$ Afficher les paramètres de Grbl"

#: bCNC/TerminalPage.py:88
msgid "$$ Display settings of Grbl"
msgstr "$$ Afficher les réglages de Grbl"

#: bCNC/ToolsPage.py:1173
msgid "$0 Step pulse time [us]"
msgstr "$0 Temps d'impulsion [µs]"

#: bCNC/ToolsPage.py:1174
msgid "$1 Step idle delay [ms]"
msgstr "$1 Temps avant mise en attente [ms]"

#: bCNC/ToolsPage.py:1180
msgid "$10 Status report [mask]"
msgstr "$10 Retour sur l'état [mask]"

#: bCNC/ToolsPage.py:1195
msgid "$100 X steps/mm"
msgstr "$100 X pas/mm"

#: bCNC/ToolsPage.py:1196
msgid "$101 Y steps/mm"
msgstr "$101 Y pas/mm"

#: bCNC/ToolsPage.py:1197
msgid "$102 Z steps/mm"
msgstr "$102 Z pas/mm"

#: bCNC/ToolsPage.py:1181
msgid "$11 Junction deviation [mm]"
msgstr "$11 Déviation Jonction [mm]"

#: bCNC/ToolsPage.py:1198
msgid "$110 X max rate [mm/min]"
msgstr "$110 X taux maxi [mm/min]"

#: bCNC/ToolsPage.py:1199
msgid "$111 Y max rate [mm/min]"
msgstr "$111 Y taux maxi [mm/min]"

#: bCNC/ToolsPage.py:1200
msgid "$112 Z max rate [mm/min]"
msgstr "$112 Z taux maxi [mm/min]"

#: bCNC/ToolsPage.py:1182
msgid "$12 Arc tolerance [mm]"
msgstr "$12 Tolérance Arc [mm]"

#: bCNC/ToolsPage.py:1201
msgid "$120 X acceleration [mm/sec^2]"
msgstr "$120 X accélération [mm/sec^2]"

#: bCNC/ToolsPage.py:1202
msgid "$121 Y acceleration [mm/sec^2]"
msgstr "$121 Y accélération [mm/sec^2]"

#: bCNC/ToolsPage.py:1203
msgid "$122 Z acceleration [mm/sec^2]"
msgstr "$122 Z accélération [mm/sec^2]"

#: bCNC/ToolsPage.py:1183
msgid "$13 Report inches"
msgstr "$13 Affiche pouces"

#: bCNC/ToolsPage.py:1204
msgid "$130 X max travel [mm]"
msgstr "$130 X déplacement maxi [mm]"

#: bCNC/ToolsPage.py:1205
msgid "$131 Y max travel [mm]"
msgstr "$131 Y déplacement maxi [mm]"

#: bCNC/ToolsPage.py:1206
msgid "$132 Z max travel [mm]"
msgstr "$132 Z déplacement maxi [mm]"

#: bCNC/ToolsPage.py:1207
msgid "$140 X homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1208
msgid "$141 Y homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1209
msgid "$142 Z homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1175
msgid "$2 Step port invert [mask]"
msgstr "$2 Inversion du port des pas [mask]"

#: bCNC/ToolsPage.py:1184
msgid "$20 Soft limits"
msgstr "$20 Limites Logicielles"

#: bCNC/ToolsPage.py:1185
msgid "$21 Hard limits"
msgstr "$21 Limites physiques"

#: bCNC/ToolsPage.py:1186
msgid "$22 Homing cycle"
msgstr "$22 Cycle Homing"

#: bCNC/ToolsPage.py:1187
msgid "$23 Homing direction invert [mask]"
msgstr "$23 Direction Homing inversée [mask]"

#: bCNC/ToolsPage.py:1188
msgid "$24 Homing feed [mm/min]"
msgstr "$24 Vitesse pour Homing [mm/min]"

#: bCNC/ToolsPage.py:1189
msgid "$25 Homing seek [mm/min]"
msgstr "$25 Vitesse de recherche précise du Homing [mm/min]"

#: bCNC/ToolsPage.py:1190
msgid "$26 Homing debounce [ms]"
msgstr "$26 Délai de réponse avant Homing, ms"

#: bCNC/ToolsPage.py:1191
msgid "$27 Homing pull-off [mm]"
msgstr "$27 Enclenchement des fins de courses lors du Homing [mm]"

#: bCNC/ToolsPage.py:1176
msgid "$3 Direction port invert [mask]"
msgstr "$3 Inversion du port pour la direction [mask]"

#: bCNC/ToolsPage.py:1192
msgid "$30 Max spindle speed [RPM]"
msgstr "$30 Vitesse maxi de rotation [RPM]"

#: bCNC/ToolsPage.py:1193
msgid "$31 Min spindle speed [RPM]"
msgstr "$31 Vitesse mini de rotation [RPM]"

#: bCNC/ToolsPage.py:1194
msgid "$32 Laser mode enable"
msgstr "$32 Mode Laser activé"

#: bCNC/ToolsPage.py:1177
msgid "$4 Step enable invert"
msgstr "$4 Inversion des pas activée"

#: bCNC/ToolsPage.py:1178
msgid "$5 Limit pins invert"
msgstr "$5 Inversion des broches de fin de course"

#: bCNC/ToolsPage.py:1179
msgid "$6 Probe pin invert"
msgstr "$6 Inversion de la broche de la sonde"

#: bCNC/TerminalPage.py:165
msgid "$C Enable/Disable checking of gcode"
msgstr "$C Activer/Désactiver la vérification du GCode"

#: bCNC/TerminalPage.py:117
msgid "$G Display state of Grbl"
msgstr "$G Afficher l'état de Grbl"

#: bCNC/TerminalPage.py:133
msgid "$I Display build information of Grbl"
msgstr "$I Afficher les informations de version de Grbl"

#: bCNC/TerminalPage.py:148
msgid "$N Display startup configuration of Grbl"
msgstr "$N Afficher la configuration de démarrage de Grbl"

#: bCNC/bmain.py:2450
#, fuzzy
#| msgid "'{}' loaded"
msgid "'{}' loaded"
msgstr "'{}' chargé"

#: bCNC/bmain.py:2446
#, fuzzy
#| msgid "'{}' reloaded at '{}'"
msgid "'{}' reloaded at '{}'"
msgstr "'{}' rechargé à '{}'"

#: bCNC/bmain.py:2459
#, fuzzy
#| msgid "'{}' saved"
msgid "'{}' saved"
msgstr "'{}' sauvegardé"

#: bCNC/EditorPage.py:376
msgid "(Un)Comment selected lines"
msgstr "(Dé)Commenter les lignes sélectionnées"

#: bCNC/controllers/_GenericGRBL.py:120
msgid "(grblHAL internal)"
msgstr ""

#: bCNC/ControlPage.py:1005 bCNC/ControlPage.py:1482
msgid "+"
msgstr ""

#: bCNC/ControlPage.py:1174 bCNC/ControlPage.py:1646
msgid "-"
msgstr ""

#: bCNC/plugins/slicemesh.py:58
msgid ".STL/.PLY file to slice"
msgstr ""

#: bCNC/ProbePage.py:1742
msgid "1. Spindle"
msgstr "1. Broche"

#: bCNC/ProbePage.py:1748
msgid "2. Camera"
msgstr "2. Caméra"

#: bCNC/plugins/slicemesh.py:67
msgid "3D slice (devel)"
msgstr ""

#: bCNC/CNCCanvas.py:2001
#, fuzzy
#| msgid ">>> ERROR: {}\n"
msgid ">>> ERROR: {}\n"
msgstr ">>> ERREUR : {}\n"

#: bCNC/ControlPage.py:1407
msgid "A"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:67
msgid "A G-code word was repeated in the block."
msgstr "Mot G-Code dupliqué dans le bloc."

#: bCNC/ControlPage.py:651
#, fuzzy
#| msgid "X work position (click to set)"
msgid "A work position (click to set)"
msgstr "Position de travail X (cliquer pour la régler)"

#: bCNC/ControlPage.py:727
msgid "A=0"
msgstr ""

#: bCNC/ControlPage.py:793
msgid "ABC=0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:117
msgid "ATC: current tool is not set. Set current tool with M61. (grblHAL)"
msgstr ""

#: bCNC/FilePage.py:216
msgid "About"
msgstr "À propos"

#: bCNC/FilePage.py:224
msgid "About the program"
msgstr "À propos du programme"

#: bCNC/bmain.py:847
#, fuzzy
#| msgid "About {}"
msgid "About {} v{}"
msgstr "Environ {}"

#: bCNC/ToolsPage.py:641
msgid "Acceleration x"
msgstr "Accélération x"

#: bCNC/ToolsPage.py:642
msgid "Acceleration y"
msgstr "Accélération y"

#: bCNC/ToolsPage.py:643
msgid "Acceleration z"
msgstr "Accéleration z"

#: bCNC/EditorPage.py:335
msgid "Active"
msgstr "Actif"

#: bCNC/plugins/trochoidPath.py:48 bCNC/plugins/trochoidal_3D.py:72
msgid "Adaptative"
msgstr ""

#: bCNC/EditorPage.py:258 bCNC/ProbePage.py:702 bCNC/ToolsPage.py:1472
msgid "Add"
msgstr "Ajouter"

#: bCNC/ToolsPage.py:1478
msgid "Add a new operation/object"
msgstr "Ajouter un nouvel objet/opération"

#: bCNC/CNCCanvas.py:468
msgid "Add an orientation marker"
msgstr "Ajouter un marqueur d'orientation"

#: bCNC/plugins/zigzag.py:108
msgid "Additional length at start/end"
msgstr "Distance supplémentaire au départ/fin"

#: bCNC/plugins/endmilloffset.py:478
#, fuzzy
#| msgid "Additional offset distance"
msgid "Additional offset (mm)"
msgstr "Distance supplémentaire de Décalage (Offset)"

#: bCNC/plugins/trochoidPath.py:41 bCNC/plugins/trochoidal_3D.py:65
#: bCNC/ToolsPage.py:1013
msgid "Additional offset distance"
msgstr "Distance supplémentaire de Décalage (Offset)"

#: bCNC/ProbePage.py:2111
msgid "After a tool change distance to scan starting from ProbeZ"
msgstr "Après un changement d'outil, distance "

#: bCNC/ToolsPage.py:617
msgid "Align Camera"
msgstr "Aligner Caméra"

#: bCNC/ToolsPage.py:620
msgid "Align Camera Angle"
msgstr "Aligner l'angle de la caméra"

#: bCNC/ToolsPage.py:619
msgid "Align Camera Height"
msgstr "Aligner la hauteur de la caméra"

#: bCNC/ToolsPage.py:618
msgid "Align Camera Width"
msgstr "Aligner la largeur de la caméra"

#: bCNC/ProbePage.py:831
msgid "Align GCode with the machine markers"
msgstr "Aligner le GCode avec les marqueurs de la machine"

#: bCNC/EditorPage.py:106 bCNC/bmain.py:140
msgid "All"
msgstr "Tout"

#: bCNC/bmain.py:1235
msgid "All GCode"
msgstr "Tout le GCode"

#: bCNC/bmain.py:119
msgid "All accepted"
msgstr "Tout accepté"

#: bCNC/bmain.py:2609
msgid "Already running"
msgstr "Déjà en marche"

#: bCNC/ToolsPage.py:717
msgid "Angle"
msgstr "Angle"

#: bCNC/ProbePage.py:808
msgid "Angle:"
msgstr "Angle :"

#: bCNC/plugins/spiral.py:481
msgid "Approach Height (Safe Z)"
msgstr ""

#: bCNC/plugins/arcfit.py:11
msgid "ArcFit"
msgstr ""

#: bCNC/ProbePage.py:956
msgid "At least one probe direction should be specified"
msgstr "Au moins une direction de sondage devrait être indiquée"

#: bCNC/EditorPage.py:198 bCNC/ProbePage.py:128 bCNC/ProbePage.py:254
#: bCNC/ProbePage.py:1224
msgid "Autolevel"
msgstr "Nivelage automatique"

#: bCNC/ProbePage.py:135
msgid "Autolevel Z surface"
msgstr "Nivelage automatique en Z"

#: bCNC/ProbePage.py:573
msgid "Automatic GOTO after probing"
msgstr ""

#: bCNC/Utils.py:602
msgid "Automatic error reporting"
msgstr "Rapport automatique d'erreur"

#: bCNC/plugins/midi2cnc.py:70
msgid "Axis to be used"
msgstr "Axe à utiliser"

#: bCNC/ControlPage.py:1491
msgid "B"
msgstr ""

#: bCNC/ControlPage.py:668
#, fuzzy
#| msgid "X work position (click to set)"
msgid "B work position (click to set)"
msgstr "Position de travail X (cliquer pour la régler)"

#: bCNC/ControlPage.py:743
msgid "B=0"
msgstr ""

#: bCNC/ControlPage.py:777
msgid "BC=0"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "BEFORE & AFTER probing"
msgstr "AVANT & APRÈS sondage"

#: bCNC/FilePage.py:316
msgid "Baud:"
msgstr "Baud :"

#: bCNC/EditorPage.py:250
msgid "Block"
msgstr "Bloc"

#: bCNC/controllers/_GenericGRBL.py:126
msgid "Bluetooth initalisation failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/jigsaw.py:362
msgid "Board height"
msgstr "Hauteur de la planche"

#: bCNC/plugins/jigsaw.py:361
msgid "Board width"
msgstr "Largeur de la planche"

#: bCNC/EditorPage.py:464
msgid "Bottom"
msgstr "Dessous"

#: bCNC/EditorPage.py:461
msgid "Bottom-Left"
msgstr "Bas-Gauche"

#: bCNC/EditorPage.py:467
msgid "Bottom-Right"
msgstr "Bas-Droit"

#: bCNC/plugins/bowl.py:14
msgid "Bowl"
msgstr "Cuvette"

#: bCNC/plugins/box.py:19
msgid "Box"
msgstr "Boîte"

#: bCNC/TerminalPage.py:215
msgid "Buffered commands"
msgstr "Commandes en mémoire tampon"

#: bCNC/TerminalPage.py:126
msgid "Build"
msgstr "Version"

#: bCNC/plugins/drillmark.py:49
msgid "Burn power for drillmark"
msgstr ""

#: bCNC/plugins/drillmark.py:48
msgid "Burn time for drillmark"
msgstr ""

#: bCNC/ControlPage.py:1410
#, fuzzy
#| msgid "CW"
msgid "C"
msgstr "Sens Horaire"

#: bCNC/ControlPage.py:685
#, fuzzy
#| msgid "X work position (click to set)"
msgid "C work position (click to set)"
msgstr "Position de travail X (cliquer pour la régler)"

#: bCNC/ControlPage.py:759
msgid "C=0"
msgstr ""

#: bCNC/ToolsPage.py:1522 bCNC/ToolsPage.py:2090
msgid "CAM"
msgstr "FAO"

#: bCNC/EditorPage.py:596
msgid "CCW"
msgstr "Sens Anti-Horaire"

#: bCNC/ProbePage.py:507
msgid "CIRCLE"
msgstr ""

#: bCNC/ControlPage.py:2363
msgid "CNC communication and control"
msgstr "Communication CNC et contrôle"

#: bCNC/bmain.py:636
msgid "CNC is currently running, please stop it before."
msgstr "CNC est en marche, veuillez l'arrêter avant."

#: bCNC/ToolsPage.py:926
msgid "CUT selected paths"
msgstr "Parcours de DÉCOUPE sélectionnés"

#: bCNC/EditorPage.py:566
msgid "CW"
msgstr "Sens Horaire"

#: bCNC/ProbePage.py:1926 bCNC/ProbePage.py:2132
msgid "Calibrate"
msgstr "Calibrer"

#: bCNC/ProbePage.py:2121
msgid "Calibration:"
msgstr "Calibration :"

#: bCNC/ProbePage.py:142 bCNC/ProbePage.py:1630 bCNC/ToolsPage.py:1870
msgid "Camera"
msgstr "Caméra"

#: bCNC/ToolsPage.py:1878
msgid "Camera Configuration"
msgstr "Configuration de la Caméra"

#: bCNC/ProbePage.py:1701
msgid "Camera cross hair diameter [units]"
msgstr "Diamètre du réticule de visée [unités]"

#: bCNC/ProbePage.py:1643
msgid "Camera location inside canvas"
msgstr "Position de la caméra dans le canevas"

#: bCNC/ProbePage.py:1718 bCNC/ProbePage.py:1727
msgid "Camera offset from gantry"
msgstr "Décalage de la caméra à partir du portique"

#: bCNC/ProbePage.py:1862
msgid "Camera offset is updated"
msgstr "Décalage de la caméra mis à jour"

#: bCNC/ProbePage.py:1655
msgid "Camera rotation [degrees]"
msgstr "Rotation de la caméra [degrés]"

#: bCNC/ProbePage.py:1688
msgid "Camera scale [pixels / unit]"
msgstr "Échelle de la caméra [pixels /unité]"

#: bCNC/lib/bFileDialog.py:361 bCNC/Utils.py:876
msgid "Cancel"
msgstr "Annuler"

#: bCNC/lib/bFileDialog.py:582
#, fuzzy
#| msgid "Cannot access path \"{}\""
msgid "Cannot access path \"{}\""
msgstr "Impossible d'accéder au chemin \"{}\""

#: bCNC/plugins/sketch.py:50
msgid "Casual first point"
msgstr ""

#: bCNC/plugins/halftone.py:47
msgid "Cell size"
msgstr "Taille de la Cellule"

#: bCNC/plugins/center.py:12 bCNC/EditorPage.py:463 bCNC/ProbePage.py:646
#: bCNC/ProbePage.py:662
msgid "Center"
msgstr "Centrer"

#: bCNC/plugins/simpleArc.py:65
#, fuzzy
#| msgid "Center"
msgid "Center X"
msgstr "Centrer"

#: bCNC/plugins/function_plot.py:28
msgid "Center X coordinate"
msgstr ""

#: bCNC/plugins/simpleArc.py:66
#, fuzzy
#| msgid "Center"
msgid "Center Y"
msgstr "Centrer"

#: bCNC/plugins/function_plot.py:29
msgid "Center Y coordinate"
msgstr ""

#: bCNC/ProbePage.py:671
msgid "Center probing using a ring"
msgstr "Sondage central avec un anneau"

#: bCNC/ProbePage.py:1944
msgid "Change"
msgstr "Changement"

#: bCNC/EditorPage.py:732
msgid "Change cut direction to CCW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:715
msgid "Change cut direction to CW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:682
msgid "Change cut direction to climb for selected gcode blocks"
msgstr ""
"Changer la direction de coupe pour monter pour les blocs de GCode "
"sélectionnés"

#: bCNC/EditorPage.py:665
msgid "Change cut direction to conventional for selected gcode blocks"
msgstr ""
"Changer la direction de coupe vers conventionnel pour les blocs de GCode "
"sélectionnées"

#: bCNC/ToolsPage.py:1810
msgid "Change program language restart is required"
msgstr "Rédémarrage requis pour changer la langue du programme"

#: bCNC/CNCCanvas.py:2250
msgid "Change viewing angle"
msgstr "Changer l'angle de vue"

#: bCNC/ProbePage.py:2011
msgid "Change:"
msgstr "Changement :"

#: bCNC/plugins/halftone.py:43 bCNC/plugins/sketch.py:57
msgid "Channel to analyze"
msgstr "Chaîne à analyser"

#: bCNC/Updates.py:86
msgid "Check Interval"
msgstr "Intervalle de Vérification"

#: bCNC/Updates.py:135
msgid "Check Now"
msgstr "Vérifier Maintenant"

#: bCNC/FilePage.py:211
msgid "Check Updates"
msgstr "Vérifier Mises à Jour"

#: bCNC/TerminalPage.py:157
msgid "Check gcode"
msgstr "Vérifier GCode"

#: bCNC/Updates.py:142
msgid "Check the web site for new versions of bCNC"
msgstr "Vérifier le site Internet pour de nouvelles versions de bCNC"

#: bCNC/lib/bFileDialog.py:998
msgid "Choose Directory"
msgstr "Choisir Répertoire"

#: bCNC/ProbePage.py:530
msgid "Circle radius"
msgstr ""

#: bCNC/plugins/trochoidal.py:46
msgid "Circular"
msgstr ""

#: bCNC/plugins/gear.py:163
msgid "Circular Pitch"
msgstr "Pas d'une dent circulaire"

#: bCNC/ProbePage.py:224 bCNC/ProbePage.py:794 bCNC/TerminalPage.py:45
msgid "Clear"
msgstr "Effacer"

#: bCNC/ControlPage.py:479
msgid "Clear Message"
msgstr "Effacer Message"

#: bCNC/ProbePage.py:230
msgid "Clear probe data"
msgstr "Effacer les données de la sonde"

#: bCNC/TerminalPage.py:50
msgid "Clear terminal"
msgstr "Effacer le terminal"

#: bCNC/CNCCanvas.py:440
msgid "Click to set the origin (zero)"
msgstr "Cliquer pour fixer l'origine (zéro)"

#: bCNC/EditorPage.py:674
msgid "Climb"
msgstr "Montée"

#: bCNC/plugins/endmilloffset.py:469
msgid "Climb milling"
msgstr ""

#: bCNC/EditorPage.py:40
msgid "Clipboard"
msgstr "Presse-papier"

#: bCNC/plugins/trochoidal.py:45 bCNC/plugins/trochoidal_3D.py:92
msgid "Clockwise"
msgstr ""

#: bCNC/EditorPage.py:276 bCNC/ToolsPage.py:1489
msgid "Clone"
msgstr "Cloner"

#: bCNC/EditorPage.py:282
msgid "Clone selected lines or blocks [Ctrl-D]"
msgstr "Cloner les blocs ou lignes sélectionnés [Ctrl-D]"

#: bCNC/ToolsPage.py:1495
msgid "Clone selected operation/object"
msgstr "Cloner l'objet/opération sélectionné"

#: bCNC/FilePage.py:267 bCNC/Updates.py:126 bCNC/Utils.py:613
#: bCNC/bmain.py:1085 bCNC/bmain.py:1320 bCNC/bmain.py:2554
msgid "Close"
msgstr "Fermer"

#: bCNC/plugins/text.py:42
msgid "Close Contours"
msgstr ""

#: bCNC/FilePage.py:280
msgid "Close program [Ctrl-Q]"
msgstr "Fermer le programme [Ctrl-Q]"

#: bCNC/plugins/closepath.py:17
msgid "Close the path"
msgstr ""

#: bCNC/plugins/closepath.py:11
msgid "ClosePath"
msgstr ""

#: bCNC/ToolsPage.py:712
msgid "Coating"
msgstr "Couche"

#: bCNC/CNCList.py:749 bCNC/EditorPage.py:203
msgid "Color"
msgstr "Couleur"

#: bCNC/ToolsPage.py:1934
msgid "Colors"
msgstr "Couleurs"

#: bCNC/Utils.py:861 bCNC/bmain.py:215
msgid "Command:"
msgstr "Commande :"

#: bCNC/TerminalPage.py:61
msgid "Commands"
msgstr "Commandes"

#: bCNC/EditorPage.py:370 bCNC/ToolsPage.py:681 bCNC/ToolsPage.py:708
#: bCNC/ToolsPage.py:738
msgid "Comment"
msgstr "Commentaire"

#: bCNC/ProbePage.py:295
msgid "Common"
msgstr "Commun"

#: bCNC/ToolsPage.py:822
msgid "Compensate islands for cutter radius"
msgstr ""

#: bCNC/ToolsPage.py:1795 bCNC/ToolsPage.py:1821
msgid "Config"
msgstr "Config"

#: bCNC/FilePage.py:348
msgid "Connect on startup"
msgstr "Connexion au démarrage"

#: bCNC/FilePage.py:352
msgid "Connect to serial on startup of the program"
msgstr "Connexion au port série au démarrage"

#: bCNC/ControlPage.py:82 bCNC/ControlPage.py:122
msgid "Connection"
msgstr "Connexion"

#: bCNC/controllers/_GenericGRBL.py:31
msgid "Connection is established with Grbl"
msgstr "Connection établie avec Grbl."

#: bCNC/ControlPage.py:927 bCNC/ControlPage.py:2364
msgid "Control"
msgstr "Contrôle"

#: bCNC/ToolsPage.py:591 bCNC/ToolsPage.py:592 bCNC/ToolsPage.py:593
#: bCNC/ToolsPage.py:594 bCNC/ToolsPage.py:595 bCNC/ToolsPage.py:596
#: bCNC/ToolsPage.py:597 bCNC/ToolsPage.py:598 bCNC/ToolsPage.py:599
#: bCNC/ToolsPage.py:600 bCNC/ToolsPage.py:601 bCNC/ToolsPage.py:602
msgid "Control-"
msgstr "Control-"

#: bCNC/ToolsPage.py:1854
msgid "Controller"
msgstr "Contrôleur"

#: bCNC/ToolsPage.py:1862
msgid "Controller (GRBL) configuration"
msgstr "Configuration du contrôleur (GRBL)"

#: bCNC/bmain.py:202
msgid "Controller buffer fill"
msgstr "Remplissage Tampon du Contrôleur :"

#: bCNC/FilePage.py:330
msgid "Controller:"
msgstr "Contrôleur :"

#: bCNC/EditorPage.py:655
msgid "Conventional"
msgstr "Conventionnel"

#: bCNC/ControlPage.py:2149
msgid "Coolant:"
msgstr ""

#: bCNC/EditorPage.py:80
msgid "Copy"
msgstr "Copier"

#: bCNC/EditorPage.py:86
msgid "Copy [Ctrl-C]"
msgstr "Copier [Ctrl-C]"

#: bCNC/plugins/simpleRectangle.py:96
#, fuzzy
#| msgid "Internal Radius"
msgid "Corner Radius"
msgstr "Rayon Interne"

#: bCNC/plugins/zigzag.py:110
msgid "Corner resolution"
msgstr "Résolution du Coin"

#: bCNC/plugins/simpleArc.py:89
#, fuzzy
#| msgid "Create Spur GEAR"
msgid "Create Simple Arc"
msgstr "Créer un engrenage"

#: bCNC/plugins/simpleDrill.py:104
msgid "Create Simple Drill"
msgstr ""

#: bCNC/plugins/simpleLine.py:73
msgid "Create Simple Line"
msgstr ""

#: bCNC/plugins/simpleRectangle.py:118
msgid "Create Simple Rectangle"
msgstr ""

#: bCNC/plugins/gear.py:178
msgid "Create Spur GEAR"
msgstr "Créer un engrenage"

#: bCNC/plugins/hilbert.py:104
msgid "Create a Hilbert path"
msgstr "Créer un parcours pour Hilbert"

#: bCNC/plugins/zigzag.py:98
msgid "Create a Zig-Zag path"
msgstr "Créer un parcours Zig-Zag"

#: bCNC/plugins/spirograph.py:111
msgid "Create a spirograph path"
msgstr "Créer un parcours pour le spirographe"

#: bCNC/plugins/trochoidPath.py:29
msgid "Create a trochoid rute along selected blocks"
msgstr ""

#: bCNC/plugins/pyrograph.py:29
msgid "Create a variable feed path based upon image brightness"
msgstr ""
"Créer parcours à vitesse variable en s'appuyant sur l'intensité de l'image"

#: bCNC/plugins/box.py:393
msgid "Create finger BOX"
msgstr "Créer une boîte assemblée par aboutage"

#: bCNC/plugins/halftone.py:29
msgid "Create halftone pattern from a picture"
msgstr "Créer le parcours de la pixellisation à partir d'une image"

#: bCNC/plugins/driller.py:36
msgid "Create holes along selected blocks"
msgstr "Créer des trous dans les blocs sélectionnés"

#: bCNC/plugins/sketch.py:28
msgid "Create sketch based on picture brightness"
msgstr "Créer esquisse en s'appuyant sur l'intensité de l'image"

#: bCNC/ToolsPage.py:1162
msgid "Create tabs on blocks"
msgstr "Créer des attaches dans les blocs"

#: bCNC/plugins/text.py:29
msgid "Create text using a ttf font"
msgstr "Créer un texte utilisant une police ttf"

#: bCNC/plugins/trochoidal_3D.py:38
#, fuzzy
#| msgid "Create holes along selected blocks"
msgid "Create trochoids along selected blocks"
msgstr "Créer des trous dans les blocs sélectionnés"

#: bCNC/ProbePage.py:1692
msgid "Crosshair:"
msgstr "Visée :"

#: bCNC/plugins/endmilloffset.py:452
msgid "Custom offset count"
msgstr ""

#: bCNC/plugins/box.py:356 bCNC/EditorPage.py:64 bCNC/ToolsPage.py:1530
msgid "Cut"
msgstr "Couper"

#: bCNC/plugins/heightmap.py:75
msgid "Cut Border"
msgstr "Coupe du Bord"

#: bCNC/EditorPage.py:724
msgid "Cut CCW"
msgstr ""

#: bCNC/EditorPage.py:707
msgid "Cut CW"
msgstr ""

#: bCNC/plugins/trochoidPath.py:38
#, fuzzy
#| msgid "Diameter"
msgid "Cut Diameter"
msgstr "Diamètre"

#: bCNC/plugins/flatten.py:298
msgid "Cut Direction"
msgstr "Direction de Coupe"

#: bCNC/plugins/spiral.py:482
msgid "Cut Pattern"
msgstr ""

#: bCNC/plugins/heightmap.py:74
msgid "Cut Top"
msgstr "Coupe du Haut"

#: bCNC/EditorPage.py:70
msgid "Cut [Ctrl-X]"
msgstr "ouper [Ctrl-X]"

#: bCNC/ToolsPage.py:829
msgid "Cut contours of selected islands"
msgstr ""

#: bCNC/ToolsPage.py:1539
msgid "Cut for the full stock thickness selected code"
msgstr ""
"Découpe pour toutes les épaisseurs enregistrées sur le code sélectionné"

#: bCNC/plugins/spiral.py:483
#, fuzzy
#| msgid "Cut Direction"
msgid "Cut in Both Directions"
msgstr "Direction de Coupe"

#: bCNC/ToolsPage.py:775
msgid "Cutting strategy"
msgstr "Stratégie de coupe"

#: bCNC/ToolsPage.py:987
msgid "DRILL selected points"
msgstr "Points de PERÇAGE sélectionnés"

#: bCNC/ToolsPage.py:655
msgid "DRO Zero padding"
msgstr "Nombre de décimales sur l'afficheur"

#: bCNC/ToolsPage.py:1397
msgid "Database"
msgstr "Base de données"

#: bCNC/lib/bFileDialog.py:307
msgid "Date"
msgstr "ate"

#: bCNC/Updates.py:101
#, fuzzy
#| msgid "Date of last checking"
msgid "Date last checked"
msgstr "Date de la dernière vérification"

#: bCNC/Updates.py:119
msgid "Days-interval to remind again for checking"
msgstr "Intervalle de jours pour rappeler une vérification de nouvelle version"

#: bCNC/ToolsPage.py:650
msgid "Decimal digits"
msgstr "Nombre de décimales"

#: bCNC/ControlPage.py:1176 bCNC/ControlPage.py:1648
msgid "Decrease step by 1 unit"
msgstr "Diminuer le pas par 1 unité"

#: bCNC/EditorPage.py:292 bCNC/ProbePage.py:750 bCNC/ToolsPage.py:1506
msgid "Delete"
msgstr "Effacer"

#: bCNC/ProbePage.py:803 bCNC/ProbePage.py:1061
msgid "Delete all markers"
msgstr "Effacer tous les marqueurs"

#: bCNC/ProbePage.py:1497
msgid "Delete autolevel information"
msgstr "Effacer les informations d'autolevel (nivelage automatique)"

#: bCNC/ProbePage.py:759
msgid "Delete current marker"
msgstr "Effacer le marqueur actuel"

#: bCNC/EditorPage.py:298
msgid "Delete selected lines or blocks [Del]"
msgstr "Effacer les blocs ou lignes sélectionnés [Ctrl-D]"

#: bCNC/ToolsPage.py:1512
msgid "Delete selected operation/object"
msgstr "Effacer l'objet/opération sélectionné"

#: bCNC/plugins/hilbert.py:114 bCNC/plugins/zigzag.py:111
msgid "Depth"
msgstr "Profondeur"

#: bCNC/plugins/box.py:349
msgid "Depth Dy"
msgstr "Profondeur Dy"

#: bCNC/plugins/trochoidPath.py:55 bCNC/plugins/trochoidal_3D.py:89
#: bCNC/ToolsPage.py:684 bCNC/ToolsPage.py:768
msgid "Depth Increment"
msgstr "Hauteur de passage"

#: bCNC/plugins/spiral.py:478
#, fuzzy
#| msgid "Depth to flatten"
msgid "Depth to Reduce"
msgstr "Profondeur à surfacer"

#: bCNC/plugins/flatten.py:293
msgid "Depth to flatten"
msgstr "Profondeur à surfacer"

#: bCNC/plugins/bowl.py:113 bCNC/ToolsPage.py:713
msgid "Diameter"
msgstr "Diamètre"

#: bCNC/plugins/Helical_Descent.py:83
#, fuzzy
msgid "Diameter Cut"
msgstr "Diamètre"

#: bCNC/ProbePage.py:649
msgid "Diameter:"
msgstr "Diamètre :"

#: bCNC/plugins/difference.py:18
msgid "Difference"
msgstr ""

#: bCNC/plugins/jigsaw.py:365
msgid "Difference between pieces"
msgstr "Différence entre les pièces"

#: bCNC/plugins/difference.py:24
msgid "Difference of two shapes"
msgstr ""

#: bCNC/plugins/trochoidPath.py:40 bCNC/plugins/trochoidal_3D.py:60
#: bCNC/plugins/pyrograph.py:46 bCNC/ToolsPage.py:1010
msgid "Direction"
msgstr "Direction"

#: bCNC/bmain.py:1584
msgid "Direction command error"
msgstr "Erreur sur la direction de la commande"

#: bCNC/lib/bFileDialog.py:275
msgid "Directory:"
msgstr "Répertoire :"

#: bCNC/EditorPage.py:327
msgid "Disable"
msgstr "Désactiver"

#: bCNC/ToolsPage.py:942
msgid "Distance (mm)"
msgstr "Distance (mm)"

#: bCNC/ControlPage.py:1921
msgid "Distance Mode [G90,G91]"
msgstr "Mode Distance [G90,G91]"

#: bCNC/plugins/driller.py:45
msgid "Distance between holes"
msgstr "Distance entre les trous"

#: bCNC/ControlPage.py:1910 bCNC/ProbePage.py:2103
msgid "Distance:"
msgstr "Distance :"

#: bCNC/ControlPage.py:1170 bCNC/ControlPage.py:1642
msgid "Divide step by 10"
msgstr "Diviser le pas par 10"

#: bCNC/ProbePage.py:1498
msgid "Do you want to delete all autolevel in formation?"
msgstr "Voulez-vous effacer toutes les informations d'autolevel ?"

#: bCNC/ProbePage.py:1062
msgid "Do you want to delete all orientation markers?"
msgstr "Voulez-vous effacer tous les marqueurs d'orientation ?"

#: bCNC/controllers/_GenericGRBL.py:162
msgid "Door closed. Ready to resume."
msgstr "Porte fermée. Prêt pour la reprise."

#: bCNC/ToolsPage.py:639
msgid "Double Size Icon"
msgstr "Taille des icônes double"

#: bCNC/EditorPage.py:524
msgid "Down"
msgstr "Bas"

#: bCNC/Updates.py:182
msgid "Download"
msgstr "Télécharger"

#: bCNC/CNCCanvas.py:463
msgid "Drag a ruler to measure distances"
msgstr "Utiliser une règle pour mesurer les distances"

#: bCNC/plugins/dragknife.py:25
msgid "Drag knife postprocessor"
msgstr ""

#: bCNC/plugins/dragknife.py:19
msgid "DragKnife"
msgstr ""

#: bCNC/plugins/halftone.py:51 bCNC/plugins/sketch.py:49
#: bCNC/plugins/pyrograph.py:47
msgid "Draw border"
msgstr "Dessiner les bords"

#: bCNC/plugins/function_plot.py:35
msgid "Draw coordinate system?"
msgstr ""

#: bCNC/CNCCanvas.py:2432
msgid "Draw timeout in seconds"
msgstr "Durée du dessin en seconde"

#: bCNC/CNCCanvas.py:2335
msgid "Draw:"
msgstr "Dessin :"

#: bCNC/ToolsPage.py:1621
msgid "Drill"
msgstr "Percer"

#: bCNC/ToolsPage.py:938
msgid "Drill in center only"
msgstr ""

#: bCNC/plugins/drillmark.py:44
msgid "Drill mark size"
msgstr ""

#: bCNC/plugins/driller.py:20
msgid "Driller"
msgstr "Perçage"

#: bCNC/plugins/driller.py:276
msgid "Driller abort: Distance must be > 0"
msgstr "Perçage annulé : La distance doit être > 0"

#: bCNC/plugins/driller.py:285
msgid "Driller abort: Dwell time >= 0, here time runs only forward!"
msgstr ""
"Perçage annulé : durée de temporisation >=0, here time runs only forward !"

#: bCNC/plugins/driller.py:293
msgid "Driller abort: Excellon-File not a file"
msgstr ""

#: bCNC/plugins/driller.py:280
msgid "Driller abort: Peck must be >= 0"
msgstr "Perçage annulé : Le palier doit être > 0"

#: bCNC/plugins/driller.py:303
msgid "Driller abort: Please select some path"
msgstr "Perçage annulé : Veuillez choisir un parcours"

#: bCNC/plugins/Helical_Descent.py:86
msgid "Drop by lap"
msgstr ""

#: bCNC/ToolsPage.py:941
msgid "Dwell (s)"
msgstr "Pause (s)"

#: bCNC/plugins/simpleDrill.py:71
#, fuzzy
#| msgid "Dwell (s)"
msgid "Dwell time (s)"
msgstr "Pause (s)"

#: bCNC/plugins/driller.py:48
msgid "Dwell time, 0 means None"
msgstr "Durée de temporisation, 0 signifie Aucun"

#: bCNC/controllers/_GenericGRBL.py:41
msgid "EEPROM read failed. Reset and restored to default values."
msgstr ""
"Échec de la lecture de l'EEPROM. Ré-initialisation et valeurs par défauts "
"restaurées."

#: bCNC/CNCCanvas.py:525
msgid "ERROR: Cannot set X-Y marker  with the current view"
msgstr "ERREUR : Impossible de régler le marqueur X-Y avec la vue actuelle"

#: bCNC/controllers/_GenericGRBL.py:150
msgid "EStop asserted. Clear and reset (grblHAL)"
msgstr ""

#: bCNC/ProbePage.py:1560
msgid "Edge Detection"
msgstr "Détection de Bord"

#: bCNC/EditorPage.py:194
msgid "Edit"
msgstr "Éditer"

#: bCNC/ToolsPage.py:1461
msgid "Edit name of current operation/object"
msgstr "Éditer le nom de l'objet/opération actuel"

#: bCNC/ToolsPage.py:1445
msgid "Editable database of EndMills properties"
msgstr "Base de données modifiable des fraises"

#: bCNC/ToolsPage.py:1429
msgid "Editable database of material properties"
msgstr "Base de données modifiable des propriétés des matériaux"

#: bCNC/EditorPage.py:804
msgid "Editor"
msgstr "Éditeur"

#: bCNC/controllers/_GenericGRBL.py:119
msgid "Emergency stop active. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2657
msgid "Empty gcode"
msgstr "GCode vide"

#: bCNC/EditorPage.py:322
msgid "Enable"
msgstr "Activer"

#: bCNC/ToolsPage.py:640
msgid "Enable 6 Axis Displays"
msgstr ""

#: bCNC/EditorPage.py:342
msgid "Enable or disable blocks of gcode"
msgstr "Activer ou désactiver des blocs de gCode"

#: bCNC/bmain.py:1154
msgid "Enabled GCode"
msgstr "GCode activé"

#: bCNC/plugins/simpleArc.py:69
msgid "End Angle in Degrees "
msgstr ""

#: bCNC/plugins/Helical_Descent.py:84 bCNC/plugins/endmilloffset.py:438
#: bCNC/plugins/trochoidPath.py:43 bCNC/plugins/trochoidal_3D.py:48
#: bCNC/ToolsPage.py:1003 bCNC/ToolsPage.py:1074 bCNC/ToolsPage.py:1437
msgid "End Mill"
msgstr "Fraise"

#: bCNC/plugins/Helical_Descent.py:100
msgid "End in the Deep"
msgstr ""

#: bCNC/bmain.py:1953
#, fuzzy
#| msgid "EndMill: {} {:g}"
msgid "EndMill: {} {}"
msgstr "Fraise : {} {:g}"

#: bCNC/plugins/Helical_Descent.py:96
msgid "Entry and Exit"
msgstr ""

#: bCNC/lib/bFileDialog.py:582 bCNC/lib/bFileDialog.py:655
#: bCNC/lib/bFileDialog.py:879 bCNC/lib/bFileDialog.py:907
#: bCNC/lib/bFileDialog.py:931
msgid "Error"
msgstr "Erreur"

#: bCNC/Utils.py:565
msgid "Error Reporting"
msgstr "Envoi du Rapport d'Erreur"

#: bCNC/lib/bFileDialog.py:880
#, fuzzy
#| msgid "Error creating folder \"{}\""
msgid "Error creating folder \"{}\""
msgstr "Erreur en créant le dossier \"{}\""

#: bCNC/lib/bFileDialog.py:931
#, fuzzy
#| msgid "Error deleting file \"{}\""
msgid "Error deleting file \"{}\""
msgstr "Erreur en effaçant le fichier \"{}\""

#: bCNC/lib/bFileDialog.py:655
#, fuzzy
#| msgid "Error listing folder \"{}\""
msgid "Error listing folder \"{}\""
msgstr "Erreur en listant le dossier \"{}\""

#: bCNC/bmain.py:2568
msgid "Error opening serial"
msgstr "Erreur à l'ouverture du port série"

#: bCNC/lib/bFileDialog.py:908
#, fuzzy
#| msgid "Error renaming \"{}\" to \"{}\""
msgid "Error renaming \"{}\" to \"{}\""
msgstr "Erreur en renommant \"{}\" en \"{}\""

#: bCNC/Utils.py:694 bCNC/Utils.py:708
msgid "Error sending report"
msgstr "Envoi du rapport d'erreur"

#: bCNC/Updates.py:193
#, fuzzy
#| msgid "Error {} in connection"
msgid "Error {} in connection"
msgstr "Erreur {} de connexion"

#: bCNC/ProbePage.py:853
msgid "Error:"
msgstr "Erreur :"

#: bCNC/plugins/bowl.py:136
msgid "Error: Check the Bowl and End Mill parameters"
msgstr "Erreur : Vérifiez les paramètres du Bol et de la Fraise"

#: bCNC/plugins/jigsaw.py:406
msgid "Error: Check the parameters and your endmill config"
msgstr ""
"Erreur : Vérifier les paramètres et la configuration de votre fraise droite"

#: bCNC/plugins/midi2cnc.py:155
msgid "Error: Sorry can't parse the Midi file."
msgstr "Erreur : Désolé, impossible de parser le fichier Midi."

#: bCNC/plugins/midi2cnc.py:116
msgid "Error: This plugin requires midiparser.py"
msgstr "Erreur : Ce plugin requiert midiparser.py"

#: bCNC/bmain.py:1487
msgid "Evaluation error"
msgstr "Erreur d'évaluation"

#: bCNC/plugins/trochoidal.py:47
msgid "Even spacing across segment"
msgstr ""

#: bCNC/ToolsPage.py:1927
msgid "Events"
msgstr "Évènements"

#: bCNC/plugins/driller.py:50
msgid "Excellon-File"
msgstr ""

#: bCNC/ToolsPage.py:1967
msgid "Execute"
msgstr "Exécuter"

#: bCNC/bmain.py:2417
msgid "Existing Autolevel"
msgstr "Nivelage existant"

#: bCNC/FilePage.py:272
msgid "Exit"
msgstr "Sortir"

#: bCNC/ToolsPage.py:805
msgid "Exit strategy (usefull for threads)"
msgstr ""

#: bCNC/EditorPage.py:351
msgid "Expand"
msgstr "Augmenter"

#: bCNC/plugins/spirograph.py:119
msgid "External Radius"
msgstr "Rayon externe"

#: bCNC/ToolsPage.py:567 bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:591
msgid "F1"
msgstr "F1"

#: bCNC/ToolsPage.py:576 bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:600
msgid "F10"
msgstr "F10"

#: bCNC/ToolsPage.py:577 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:601
msgid "F11"
msgstr "F11"

#: bCNC/ToolsPage.py:578 bCNC/ToolsPage.py:590 bCNC/ToolsPage.py:602
msgid "F12"
msgstr "F12"

#: bCNC/ToolsPage.py:568 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:592
msgid "F2"
msgstr "F2"

#: bCNC/ToolsPage.py:569 bCNC/ToolsPage.py:581 bCNC/ToolsPage.py:593
msgid "F3"
msgstr "F3"

#: bCNC/ToolsPage.py:570 bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:594
msgid "F4"
msgstr "F4"

#: bCNC/ToolsPage.py:571 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:595
msgid "F5"
msgstr "F5"

#: bCNC/ToolsPage.py:572 bCNC/ToolsPage.py:584 bCNC/ToolsPage.py:596
msgid "F6"
msgstr "F6"

#: bCNC/ToolsPage.py:573 bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:597
msgid "F7"
msgstr "F7"

#: bCNC/ToolsPage.py:574 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:598
msgid "F8"
msgstr "F8"

#: bCNC/ToolsPage.py:575 bCNC/ToolsPage.py:587 bCNC/ToolsPage.py:599
msgid "F9"
msgstr "F9"

#: bCNC/ProbePage.py:485
msgid "FEED"
msgstr ""

#: bCNC/ProbePage.py:518
msgid "FINISH"
msgstr ""

#: bCNC/plugins/sketch.py:47
msgid "Fading force"
msgstr ""

#: bCNC/ProbePage.py:306
msgid "Fast Probe Feed:"
msgstr "Vitesse rapide de la sonde :"

#: bCNC/plugins/scaling.py:43 bCNC/plugins/trochoidal_3D.py:95
#: bCNC/ToolsPage.py:682 bCNC/ToolsPage.py:769
msgid "Feed"
msgstr "Vitesse"

#: bCNC/ControlPage.py:2021
msgid "Feed Mode [G93, G94, G95]"
msgstr "Mode Vitesse [G93, G94, G95]"

#: bCNC/ControlPage.py:1999
msgid "Feed Rate [F#]"
msgstr "Vitesse de déplacement [F#]"

#: bCNC/plugins/trochoidal_3D.py:411
#, fuzzy
msgid "Feed has to be greater than 0"
msgstr "Carte 3D annulée : La profondeur doit être < 0"

#: bCNC/ControlPage.py:487
msgid "Feed hold"
msgstr "Déplacement en pause"

#: bCNC/ToolsPage.py:644
msgid "Feed max x"
msgstr "Vitesse max x"

#: bCNC/ToolsPage.py:645
msgid "Feed max y"
msgstr "Vitesse max y"

#: bCNC/ToolsPage.py:646
msgid "Feed max z"
msgstr "Vitesse max z"

#: bCNC/plugins/driller.py:56
#, fuzzy
#| msgid "Feed max x"
msgid "Feed rapid G0"
msgstr "Vitesse max x"

#: bCNC/controllers/_GenericGRBL.py:62
msgid "Feed rate has not yet been set or is undefined."
msgstr "La vitesse de travail n'a pas encore été réglée ou est indéfinie."

#: bCNC/ControlPage.py:1987
msgid "Feed:"
msgstr "Vitesse :"

#: bCNC/plugins/trochoidal.py:53
msgid "Feedrate"
msgstr ""

#: bCNC/FilePage.py:75 bCNC/FilePage.py:464
msgid "File"
msgstr "Fichier"

#: bCNC/FilePage.py:463
msgid "File I/O and configuration"
msgstr "Fichier I/O et configuration"

#: bCNC/lib/bFileDialog.py:951 bCNC/lib/bFileDialog.py:962
#, fuzzy
#| msgid "File \"{}\" does not exist"
msgid "File \"{}\" does not exist"
msgstr "Le fichier \"{}\" n'existe pas"

#: bCNC/lib/bFileDialog.py:984
msgid "File already exists"
msgstr "Fichier déjà existant"

#: bCNC/lib/bFileDialog.py:950 bCNC/lib/bFileDialog.py:961
msgid "File does not exist"
msgstr "Le fichier n'existe pas"

#: bCNC/bmain.py:2378
msgid "File modified"
msgstr "Fichier modifié"

#: bCNC/lib/bFileDialog.py:332
msgid "File name:"
msgstr "Nom du fichier :"

#: bCNC/lib/bFileDialog.py:340
msgid "Files of type:"
msgstr "Fichiers de type :"

#: bCNC/EditorPage.py:167
msgid "Filter"
msgstr ""

#: bCNC/EditorPage.py:175
msgid "Filter blocks"
msgstr "Filtrer les blocs"

#: bCNC/plugins/Helical_Descent.py:87
#, fuzzy
msgid "Final Depth"
msgstr "Profondeur de travail"

#: bCNC/plugins/center.py:18
msgid "Find center of bounding box"
msgstr ""

#: bCNC/plugins/box.py:351
msgid "Fingers Nx"
msgstr "Dents Nx"

#: bCNC/plugins/box.py:352
msgid "Fingers Ny"
msgstr "Dents Ny"

#: bCNC/plugins/box.py:353
msgid "Fingers Nz"
msgstr "Dents Nz"

#: bCNC/ProbePage.py:1214
msgid "Finished recording"
msgstr ""

#: bCNC/ToolsPage.py:790
msgid "First cut at surface height"
msgstr "Coupe d'abord à la hauteur de la surface"

#: bCNC/CNCCanvas.py:2270
msgid "Fit to screen [F]"
msgstr "Afficher en entier [F]"

#: bCNC/plugins/flatpath.py:12
msgid "FlatPath"
msgstr ""

#: bCNC/plugins/flatten.py:13
msgid "Flatten"
msgstr "Surfacer"

#: bCNC/plugins/flatten.py:43
msgid "Flatten abort: Cut Direction is undefined"
msgstr "Surfaçage annulé : Direction de Coupe non définie"

#: bCNC/plugins/flatten.py:52
msgid "Flatten abort: Flatten Area dimensions must be > 0"
msgstr "Surfaçage annulé : La dimension de la zone à aplanir doit être > 0"

#: bCNC/plugins/flatten.py:98
msgid "Flatten abort: Flatten area is too small for this End Mill."
msgstr ""
"Surfaçage annulé : La zone à aplanir est trop petite pour cette fraise."

#: bCNC/plugins/flatten.py:58
msgid "Flatten abort: Hey this is only for subtractive machine! Check depth!"
msgstr ""
"Surfaçage annulé : Hé, c'est seulement pour les machines qui enlève de la "
"matière ! Vérifiez la profondeur !"

#: bCNC/plugins/flatten.py:47
msgid "Flatten abort: Pocket Type is undefined"
msgstr "Surfaçage annulé : Type de poche non définie"

#: bCNC/plugins/flatten.py:281
msgid "Flatten an area in different ways"
msgstr "Aplanir une zone de différentes façons"

#: bCNC/plugins/flatpath.py:18
msgid "Flatten the path"
msgstr ""

#: bCNC/plugins/flatten.py:328
msgid "Flatten: Generated flatten surface"
msgstr "Surfaçage : surface aplanie générée"

#: bCNC/EditorPage.py:581
msgid "Flip"
msgstr "Symétrique"

#: bCNC/plugins/slicemesh.py:68
msgid "Flip upwards"
msgstr ""

#: bCNC/ControlPage.py:2168
msgid "Flood"
msgstr ""

#: bCNC/ToolsPage.py:715
msgid "Flutes"
msgstr "Tranchants"

#: bCNC/plugins/text.py:41
msgid "Font file"
msgstr "Fichier de la police"

#: bCNC/plugins/text.py:40
msgid "Font size"
msgstr "Taille de la police"

#: bCNC/ToolsPage.py:1941
msgid "Fonts"
msgstr "Polices"

#: bCNC/ToolsPage.py:657
msgid "Footer gcode"
msgstr "G-code à exécuter à la fin"

#: bCNC/plugins/function_plot.py:24
msgid "Formula"
msgstr ""

#: bCNC/ProbePage.py:1575
msgid "Freeze"
msgstr "Freeze"

#: bCNC/plugins/function_plot.py:8
#, fuzzy
#| msgid "Connection"
msgid "Function"
msgstr "Connexion"

#: bCNC/bmain.py:133 bCNC/bmain.py:2487
msgid "G-Code"
msgstr "G-Code"

#: bCNC/plugins/arcfit.py:17
msgid "G-Code arc-fit"
msgstr ""

#: bCNC/bmain.py:134
msgid "G-Code clean"
msgstr "Nettoyage du G-Code"

#: bCNC/plugins/linearize.py:17
msgid "G-Code linearizer"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:63
msgid "G-code command in block requires an integer value."
msgstr "La commande G-code dans le bloc doit recevoir une valeur entière."

#: bCNC/controllers/_GenericGRBL.py:108
msgid "G-code command not allowed when tool change is pending. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:45
msgid "G-code locked out during alarm or jog state"
msgstr "G-code écarté pendant l'alarm ou jog state"

#: bCNC/controllers/_GenericGRBL.py:35
msgid "G-code words consist of a letter and a value. Letter was not found."
msgstr ""
"Les mots G-code sont constitués d'une lettre et d'une valeur. Lettre non "
"trouvée."

#: bCNC/ProbePage.py:59
msgid "G38.2 stop on contact else error"
msgstr "G38.2 Arrêt au contact sinon erreur"

#: bCNC/ProbePage.py:60
msgid "G38.3 stop on contact"
msgstr "G38.3 stoppe au contact"

#: bCNC/ProbePage.py:61
msgid "G38.4 stop on loss contact else error"
msgstr "G38.4 stoppe à la perte de contact sinon erreur"

#: bCNC/ProbePage.py:62
msgid "G38.5 stop on loss contact"
msgstr "G38.5 stoppe à la perte de contact"

#: bCNC/ControlPage.py:2051
msgid "G92:"
msgstr ""

#: bCNC/ProbePage.py:733
msgid "GCode X coordinate of orientation point"
msgstr "Coordonnée GCode X du point d'orientation"

#: bCNC/ProbePage.py:744
msgid "GCode Y coordinate of orientation point"
msgstr "Coordonnée GCode Y du point d'orientation"

#: bCNC/EditorPage.py:803
msgid "GCode editor"
msgstr "Éditeur GCode"

#: bCNC/ToolsPage.py:2089
msgid "GCode manipulation tools and user plugins"
msgstr "Outils de manipulation GCode et plugins utilisateurs"

#: bCNC/bmain.py:2379
msgid "Gcode was modified do you want to save it first?"
msgstr "Le GCode a été modifié, voulez-vous d'abord l'enregistrer ?"

#: bCNC/ProbePage.py:723
msgid "Gcode:"
msgstr "Gcode :"

#: bCNC/plugins/gear.py:16
msgid "Gear"
msgstr "Engrenage"

#: bCNC/plugins/bowl.py:105
msgid "Generate a bowl cavity"
msgstr "Générer une cuvette"

#: bCNC/plugins/box.py:339
msgid "Generate a finger box"
msgstr "Générer une boîte à dents d'assemblage"

#: bCNC/plugins/endmilloffset.py:430
msgid "Generate a pocket or profile for selected shape (regarding islands)"
msgstr ""

#: bCNC/plugins/simpleArc.py:58
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Arc"
msgstr "Générer un engrenage"

#: bCNC/plugins/simpleDrill.py:60
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Drill"
msgstr "Générer un engrenage"

#: bCNC/plugins/simpleLine.py:44
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple line"
msgstr "Générer un engrenage"

#: bCNC/plugins/simpleRectangle.py:85
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple rectangle"
msgstr "Générer un engrenage"

#: bCNC/plugins/gear.py:153
msgid "Generate a spur gear"
msgstr "Générer un engrenage"

#: bCNC/plugins/halftone.py:53
msgid "Generate for conical end mill"
msgstr "Générer pour la fraise conique"

#: bCNC/plugins/endmilloffset.py:175 bCNC/plugins/endmilloffset.py:183
#: bCNC/plugins/endmilloffset.py:191 bCNC/plugins/endmilloffset.py:199
#: bCNC/plugins/endmilloffset.py:207 bCNC/plugins/endmilloffset.py:215
#: bCNC/plugins/endmilloffset.py:223 bCNC/plugins/endmilloffset.py:573
#: bCNC/ToolsPage.py:1088
msgid "Generate pocket path"
msgstr "Générer le parcours pour la poche"

#: bCNC/ToolsPage.py:1062
msgid "Generate profile path"
msgstr "Générer le parcours pour le contour"

#: bCNC/plugins/Random.py:22 bCNC/plugins/tile.py:20
msgid "Generate replicas of selected code"
msgstr "Générer des copies du code sélectionné"

#: bCNC/plugins/driller.py:459
#, fuzzy
#| msgid "Generated Driller: {} holes"
msgid "Generated Driller: {} holes"
msgstr "Perçage généré : {} trous"

#: bCNC/plugins/halftone.py:285
#, fuzzy
#| msgid "Generated Halftone size W={} x H={} x D={} ,Total points:%i"
msgid "Generated Halftone size W={} x H={} x D={}, Total points: {}"
msgstr ""
"Taille générée par la pixellisation W={} x H={} x D={}, Total des points :%i"

#: bCNC/plugins/heightmap.py:408
#, fuzzy
#| msgid "Generated Heightmap {} x {} x {} "
msgid "Generated Heightmap {} x {} x {}"
msgstr "Carte 3D {} x {} x {} générée"

#: bCNC/plugins/midi2cnc.py:353
msgid "Generated Midi2CNC, ready to play?"
msgstr "Midi2CNC généré, prêt à le lancer ?"

#: bCNC/plugins/pyrograph.py:213
#, fuzzy
#| msgid "Generated Pyrograph W={:g} x H={:g} x D={:g}"
msgid "Generated Pyrograph W={:g} x H={:g} x D={:g}"
msgstr "Générer Pyrograph W={:g} x H={:g} x D={:g}"

#: bCNC/plugins/function_plot.py:273
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated function graph"
msgstr "Spirograph généré"

#: bCNC/plugins/trochoidPath.py:110
#, fuzzy
#| msgid "Generate for conical end mill"
msgid "Generated path for trochoidal cutting"
msgstr "Générer pour la fraise conique"

#: bCNC/plugins/arcfit.py:102
msgid "Generated: Arc fit"
msgstr ""

#: bCNC/plugins/bowl.py:134
msgid "Generated: BOWL"
msgstr "BOL généré"

#: bCNC/plugins/box.py:395
msgid "Generated: BOX with fingers"
msgstr "Boîte à dents d'assemblage générée"

#: bCNC/plugins/center.py:70
msgid "Generated: Center"
msgstr ""

#: bCNC/plugins/closepath.py:52
msgid "Generated: Closepath"
msgstr ""

#: bCNC/plugins/difference.py:102
msgid "Generated: Diff"
msgstr ""

#: bCNC/plugins/dragknife.py:229
msgid "Generated: Dragknife"
msgstr ""

#: bCNC/plugins/flatpath.py:60
msgid "Generated: Flat"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:545
#, fuzzy
msgid "Generated: Helical_Descent Result"
msgstr "Hilbert généré"

#: bCNC/plugins/hilbert.py:146
msgid "Generated: Hilbert"
msgstr "Hilbert généré"

#: bCNC/plugins/intersection.py:86
msgid "Generated: Intersect"
msgstr ""

#: bCNC/plugins/linearize.py:97
msgid "Generated: Linearize"
msgstr ""

#: bCNC/plugins/drillmark.py:319
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Manual drillmark"
msgstr "Hilbert généré"

#: bCNC/plugins/simpleArc.py:91
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Arc"
msgstr "Hilbert généré"

#: bCNC/plugins/simpleDrill.py:106
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Drill"
msgstr "Hilbert généré"

#: bCNC/plugins/simpleLine.py:75
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Line"
msgstr "Hilbert généré"

#: bCNC/plugins/simpleRectangle.py:120
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Rectangle"
msgstr "Hilbert généré"

#: bCNC/plugins/spirograph.py:145
msgid "Generated: Spirograph"
msgstr "Spirograph généré"

#: bCNC/plugins/gear.py:180
msgid "Generated: Spur GEAR"
msgstr "Engrenage généré"

#: bCNC/plugins/trochoidal.py:177
msgid "Generated: Trochoidal"
msgstr ""

#: bCNC/plugins/zigzag.py:158
msgid "Generated: Zig-Zag"
msgstr "Zig-Zag généré"

#: bCNC/plugins/function_plot.py:14
msgid "Generates gcode from a formula"
msgstr ""

#: bCNC/plugins/jigsaw.py:387
msgid "Generating puzzle..."
msgstr "Génération du puzzle..."

#: bCNC/ProbePage.py:1704
msgid "Get"
msgstr "Régler"

#: bCNC/ProbePage.py:2050
msgid "Get current gantry position as machine tool change location"
msgstr ""
"Récupérer la position actuelle du portique comme position de changement "
"d'outil"

#: bCNC/ProbePage.py:2096
msgid "Get current gantry position as machine tool probe location"
msgstr "Récupérer la position actuelle du portique comme position de la sonde"

#: bCNC/ProbePage.py:1706
msgid "Get diameter from active endmill"
msgstr "Saisir le diamètre de la fraise active"

#: bCNC/plugins/slicemesh.py:63
msgid "Get flat slice"
msgstr ""

#: bCNC/ProbePage.py:192
msgid "Get margins from gcode file"
msgstr "Prendre les marges dans le fichier G-code"

#: bCNC/ProbePage.py:583
msgid "Goto"
msgstr "Aller"

#: bCNC/controllers/_GenericGRBL.py:37
msgid "Grbl '$' system command was not recognized or supported."
msgstr "La commande système $ envoyé à Grbl n'est pas reconnue ou supportée."

#: bCNC/controllers/_GenericGRBL.py:21
msgid "Grbl executes jogging motion"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:13
msgid "Grbl is in idle state and waiting for user commands"
msgstr "Grbl est en attente de commandes de l'utilisateur."

#: bCNC/controllers/_GenericGRBL.py:29
msgid "Grbl is not connected. Please specify the correct port and click Open."
msgstr ""
"Grbl n'est pas connecté. Veuillez indiquer le bon port et cliquez sur Ouvrir."

#: bCNC/controllers/_GenericGRBL.py:14
msgid "Grbl is on hold state. Click on resume (pause) to continue"
msgstr "Grbl est en pause. Cliquez sur reprendre (pause) pour poursuivre."

#: bCNC/plugins/sketch.py:41
msgid "Grundgy, search radius"
msgstr "Grundgy, chercher le rayon"

#: bCNC/ProbePage.py:1659
msgid "Haircross Offset:"
msgstr "Décalage du réticule :"

#: bCNC/ProbePage.py:1667
msgid "Haircross X offset [unit]"
msgstr "Décalage X du réticule de visée [unités]"

#: bCNC/ProbePage.py:1676
msgid "Haircross Y offset [unit]"
msgstr "Décalage Y du réticule de visée [unités]"

#: bCNC/plugins/halftone.py:15
msgid "Halftone"
msgstr "Pixellisation"

#: bCNC/plugins/halftone.py:159
msgid "Halftone abort: Angle in V-Cutting end mill is missing"
msgstr "Pixellisation annulée : Angle de la fraise en V non renseigné"

#: bCNC/plugins/halftone.py:173
msgid "Halftone abort: Can't read image file"
msgstr "Pixellisation annulée : impossible de lire le fichier image"

#: bCNC/plugins/halftone.py:148
msgid "Halftone abort: Cell size too small"
msgstr "Pixellisation annulée : Taille de la cellule trop petite"

#: bCNC/plugins/halftone.py:165
msgid "Halftone abort: Conical path need V-Cutting end mill"
msgstr "Pixellisation annulée : le parcours conique nécessite une fraise en V"

#: bCNC/plugins/halftone.py:144
msgid "Halftone abort: Maximum diameter too small"
msgstr "Pixellisation annulée : Diamètre Maxi trop petit"

#: bCNC/plugins/halftone.py:138
msgid "Halftone abort: Minimum diameter must be minor then Maximum"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/halftone.py:133
msgid "Halftone abort: Size too small to draw anything!"
msgstr ""
"Pixellisation annulée : Taille trop petite pour dessiner quoi que ce soit !"

#: bCNC/plugins/halftone.py:109
msgid "Halftone abort: This plugin requires PIL/Pillow to read image data"
msgstr ""
"Pixellisation annulée : Ce plugin nécessite PIL/Pillow pour lire les données "
"de l'image"

#: bCNC/ControlPage.py:84
msgid "Hard Reset"
msgstr "Hard Reset"

#: bCNC/ToolsPage.py:656
msgid "Header gcode"
msgstr "G-code à exécuter au début"

#: bCNC/ToolsPage.py:1105
msgid "Height"
msgstr "Hauteur"

#: bCNC/plugins/box.py:350
msgid "Height Dz"
msgstr "Hauteur Dz"

#: bCNC/plugins/flatten.py:292
msgid "Height to flatten"
msgstr "Hauteur à surfacer"

#: bCNC/plugins/heightmap.py:40
msgid "Heightmap"
msgstr "Carte 3D"

#: bCNC/plugins/heightmap.py:98
msgid "Heightmap abort: Can't read image file"
msgstr "Carte 3D annulée : impossible de lire le fichier"

#: bCNC/plugins/heightmap.py:89
msgid "Heightmap abort: This plugin requires PIL/Pillow"
msgstr "Carte 3D annulée : Ce plugin nécessite PIL/Pillow"

#: bCNC/plugins/heightmap.py:171
msgid "Heightmap abort: angle not defined for selected End Mill"
msgstr "Carte 3D annulée : angle non définie pour la Fraise choisie"

#: bCNC/plugins/heightmap.py:102
msgid "Heightmap abort: depth must be < 0"
msgstr "Carte 3D annulée : La profondeur doit être < 0"

#: bCNC/plugins/Helical_Descent.py:271
#, fuzzy
msgid "Helical Abort: Drop must be greater than 0"
msgstr "Carte 3D annulée : La profondeur doit être < 0"

#: bCNC/plugins/Helical_Descent.py:286
msgid "Helical Abort: Entry Edge Clearence may be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:261
#, fuzzy
msgid "Helical Abort: Helix diameter must be greater than the end mill"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/Helical_Descent.py:267
#, fuzzy
msgid "Helical Abort: Helix diameter must be positive"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/Helical_Descent.py:257
#, fuzzy
msgid "Helical Abort: Please select helical type"
msgstr "Perçage annulé : Veuillez choisir un parcours"

#: bCNC/plugins/Helical_Descent.py:281
#, fuzzy
msgid "Helical Abort: Please selecte Entry and Exit type"
msgstr "Texte annulé : Veuillez choisir un fichier de police"

#: bCNC/plugins/Helical_Descent.py:275
#, fuzzy
msgid "Helical Abort: Z Feed Multiplier must be greater than 0"
msgstr ""
"L'intervalle de temps minimum d'impulsion doit être plus grand que 3µsec"

#: bCNC/plugins/trochoidal_3D.py:407
#, fuzzy
msgid "Helical Descent Diameter has to be greater than End mill"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/Helical_Descent.py:94
msgid "Helical Type"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:320
#, fuzzy
msgid "Helical abort: Please select some path"
msgstr "Perçage annulé : Veuillez choisir un parcours"

#: bCNC/TerminalPage.py:175
msgid "Help"
msgstr "Aide"

#: bCNC/Ribbon.py:641
msgid "Help [F1]"
msgstr "Aide [F1]"

#: bCNC/plugins/hilbert.py:15
msgid "Hilbert"
msgstr "Hilbert"

#: bCNC/plugins/hilbert.py:136
msgid "Hilbert abort: depth must be minor or equal to zero"
msgstr "Hilbert annulé : La profondeur doit être inférieure ou égale à 0"

#: bCNC/plugins/hilbert.py:131
msgid "Hilbert abort: verify the size"
msgstr "Hilbert annulé : vérifiez la taille"

#: bCNC/controllers/_GenericGRBL.py:160
msgid "Hold complete. Ready to resume."
msgstr "Pause terminée. Prêt à reprendre."

#: bCNC/controllers/_GenericGRBL.py:161
msgid "Hold in-progress. Reset will throw an alarm."
msgstr "Pause en cours. Une ré-initialisation déclenchera une alarme."

#: bCNC/ControlPage.py:93
msgid "Home"
msgstr "Home"

#: bCNC/controllers/_GenericGRBL.py:116
msgid "Home machine to continue. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:39
msgid "Homing cycle is not enabled via settings."
msgstr "Cycle Homing non activé dans le paramétrage."

#: bCNC/controllers/_GenericGRBL.py:142
msgid "Homing fail. Reset during active homing cycle."
msgstr "Échec de l'Homing. Ré-initialisation pendant ce cycle."

#: bCNC/controllers/_GenericGRBL.py:143
msgid "Homing fail. Safety door was opened during active homing cycle."
msgstr ""
"Échec du Homing. La porte de sécurité a été ouverte pendant l'opération de "
"Homing."

#: bCNC/controllers/_GenericGRBL.py:152
msgid "Homing required. Execute homing command ($H) to continue. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:612
msgid "Horizontal"
msgstr "Horizontal"

#: bCNC/Utils.py:848
msgid "Icon to appear on button"
msgstr "Icône du bouton"

#: bCNC/Utils.py:837
msgid "Icon:"
msgstr "Icone :"

#: bCNC/plugins/Helical_Descent.py:97
msgid "If Eddge, Edge Clearance"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:253
msgid "If block selected false, please make a value of x"
msgstr ""

#: bCNC/ProbePage.py:67
msgid "Ignore M6 commands"
msgstr "Ignore les commandes M6"

#: bCNC/plugins/text.py:44
msgid "Image chars width"
msgstr "Largeur des caractères de l'image"

#: bCNC/plugins/halftone.py:50
msgid "Image rotation angle"
msgstr "Angle de rotation de l'image"

#: bCNC/plugins/text.py:43
msgid "Image to Ascii"
msgstr "Image vers Ascii"

#: bCNC/plugins/halftone.py:38 bCNC/plugins/sketch.py:52
#: bCNC/plugins/heightmap.py:78 bCNC/plugins/pyrograph.py:48
msgid "Image to process"
msgstr "Image à graver"

#: bCNC/EditorPage.py:207 bCNC/FilePage.py:126
msgid "Import"
msgstr "Importer"

#: bCNC/bmain.py:2482
msgid "Import Gcode/DXF file"
msgstr "Importer fichier GCode/DXF"

#: bCNC/FilePage.py:131
msgid "Import gcode/dxf file"
msgstr ""

#: bCNC/ControlPage.py:1008 bCNC/ControlPage.py:1484
msgid "Increase step by 1 unit"
msgstr "Augmenter le pas par 1 unité"

#: bCNC/EditorPage.py:742 bCNC/EditorPage.py:765
msgid "Info"
msgstr "Info"

#: bCNC/ToolsPage.py:1630
msgid "Insert a drill cycle on current objects/location"
msgstr "Insérer un cycle de perçage sur les objets/emplacements actuels"

#: bCNC/EditorPage.py:239 bCNC/EditorPage.py:266
msgid "Insert a new block or line of code [Ins or Ctrl-Enter]"
msgstr "Insérer un nouveau bloc ou ligne de code [Ins ou Ctrl-Enter]"

#: bCNC/ToolsPage.py:1646
msgid "Insert holding tabs"
msgstr "Insérer des attaches de maintien"

#: bCNC/Updates.py:60
msgid "Installed Version:"
msgstr "Version installée :"

#: bCNC/plugins/box.py:347
msgid "Internal Dimensions"
msgstr "Dimensions Intérieures"

#: bCNC/plugins/spirograph.py:120
msgid "Internal Radius"
msgstr "Rayon Interne"

#: bCNC/plugins/intersection.py:18
msgid "Intersection"
msgstr ""

#: bCNC/plugins/intersection.py:24
msgid "Intersection of two shapes"
msgstr ""

#: bCNC/Updates.py:103
msgid "Interval (days):"
msgstr "Intervalle (jours) :"

#: bCNC/ProbePage.py:1412
msgid "Invalid X probing region"
msgstr "Région X de sondage invalide"

#: bCNC/ProbePage.py:1421
msgid "Invalid X range [xmin>=xmax]"
msgstr "Région X de sondage invalide [xmin>=xmax]"

#: bCNC/ProbePage.py:1436
msgid "Invalid Y probing region"
msgstr "Région Y de sondage invalide"

#: bCNC/ProbePage.py:1445
msgid "Invalid Y range [ymin>=ymax]"
msgstr "Région Y de sondage invalide [ymin>=ymax]"

#: bCNC/ProbePage.py:1457
msgid "Invalid Z probing region"
msgstr "Région Z de sondage invalide"

#: bCNC/ProbePage.py:1466
msgid "Invalid Z range [zmin>=zmax]"
msgstr "Région Z de sondage invalide [zmin>=zmax]"

#: bCNC/Sender.py:355
#, fuzzy
#| msgid "Invalid command {}"
msgid "Invalid command {}"
msgstr "Commande {} invalide."

#: bCNC/ProbePage.py:988
msgid "Invalid diameter entered"
msgstr "Diamètre saisi invalide"

#: bCNC/bmain.py:1585
#, fuzzy
#| msgid "Invalid direction {} specified"
msgid "Invalid direction {} specified"
msgstr "Direction {} spécifiée non valide"

#: bCNC/ProbePage.py:923 bCNC/ProbePage.py:1475
msgid "Invalid probe feed rate"
msgstr "Vitesse d'avancée de la sonde invalide."

#: bCNC/ProbePage.py:2199
msgid "Invalid tool change position"
msgstr "Position de changement d'outil invalide"

#: bCNC/ProbePage.py:2231
msgid "Invalid tool height or not calibrated"
msgstr "Hauteur de l'outil invalide ou non calibrée"

#: bCNC/ProbePage.py:2211
msgid "Invalid tool probe location"
msgstr "Position de l'outil de sondage invalide"

#: bCNC/ProbePage.py:2221 bCNC/ProbePage.py:2241
msgid "Invalid tool scanning distance entered"
msgstr "Distance erronée de scan de sondage saisie"

#: bCNC/bmain.py:1978
#, fuzzy
#| msgid "Invalid user command {}"
msgid "Invalid user command {}"
msgstr "Commande utilisateur {} invalide"

#: bCNC/plugins/heightmap.py:76 bCNC/EditorPage.py:138 bCNC/EditorPage.py:543
msgid "Invert"
msgstr "Inverser"

#: bCNC/plugins/halftone.py:45
msgid "Invert Colors"
msgstr "Inverser Couleurs"

#: bCNC/EditorPage.py:549
msgid "Invert cutting order of selected blocks"
msgstr "Inverse l'ordre de coupe des blocs sélectionnés"

#: bCNC/EditorPage.py:144
msgid "Invert selection [Ctrl-I]"
msgstr "Inverser la sélection [Ctrl-I]"

#: bCNC/ToolsPage.py:1655
msgid "Island"
msgstr ""

#: bCNC/plugins/endmilloffset.py:494
msgid "Island behaviour"
msgstr ""

#: bCNC/plugins/jigsaw.py:403
msgid "Jigsaw puzzle generated in {}s"
msgstr "Puzzle à la scie à chantourner généré en {}s"

#: bCNC/plugins/jigsaw.py:353
msgid "Jigsaw puzzle generator"
msgstr "Générateur d'un puzzle pour scie à chantourner"

#: bCNC/controllers/_GenericGRBL.py:58
msgid "Jog command with no '=' or contains prohibited g-code."
msgstr "Commande via les touches sans '=' ou avec du g-code interdit."

#: bCNC/controllers/_GenericGRBL.py:57
msgid "Jog target exceeds machine travel. Command ignored."
msgstr ""
"Cible via touches dépasse les limites de déplacement. Commande ignorée."

#: bCNC/EditorPage.py:386
msgid "Join"
msgstr ""

#: bCNC/EditorPage.py:392
msgid "Join selected blocks"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:29
msgid "Keep original Yes/No"
msgstr ""

#: bCNC/plugins/simpleRotate.py:30
msgid "Keep original yes/no"
msgstr ""

#: bCNC/ToolsPage.py:1911
msgid "Language change"
msgstr "Changer de langage"

#: bCNC/ToolsPage.py:638
msgid "Laser Adaptive Power"
msgstr "Puissance du Laser"

#: bCNC/ToolsPage.py:637
msgid "Laser Cutter"
msgstr "Coupe au laser"

#: bCNC/controllers/_GenericGRBL.py:59
msgid "Laser mode requires PWM output."
msgstr "Le mode Laser necessite une sortie PWM."

#: bCNC/plugins/driller.py:70
msgid "Laser power maximum"
msgstr ""

#: bCNC/plugins/driller.py:63
msgid "Laser power minimum"
msgstr ""

#: bCNC/Updates.py:89
msgid "Last Check:"
msgstr "Dernière Vérification :"

#: bCNC/ControlPage.py:603 bCNC/ControlPage.py:910
#, fuzzy
#| msgid "Last error: {}\n"
msgid "Last error: {}\n"
msgstr "Dernière erreur : {}\n"

#: bCNC/Updates.py:67
msgid "Latest Github Version:"
msgstr "Dernière Version Github :"

#: bCNC/Updates.py:73
#, fuzzy
#| msgid "Latest release version on on github"
msgid "Latest release version on github"
msgstr "Dernière version sur Github"

#: bCNC/EditorPage.py:154
msgid "Layer"
msgstr "Couche :"

#: bCNC/ToolsPage.py:811
msgid "Leave islands uncut"
msgstr ""

#: bCNC/EditorPage.py:460
msgid "Left"
msgstr "Gauche"

#: bCNC/ToolsPage.py:716
msgid "Length"
msgstr "Longueur"

#: bCNC/plugins/spiral.py:477
msgid "Length of Stock to Reduce"
msgstr ""

#: bCNC/bmain.py:1204 bCNC/bmain.py:1290
msgid "Length:"
msgstr "Longueur :"

#: bCNC/plugins/spiral.py:484
msgid "Lift before rotate"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:154
msgid "Limit switch engaged. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:245
msgid "Line"
msgstr "Ligne"

#: bCNC/plugins/zigzag.py:107
msgid "Line length"
msgstr "Longueur de la Ligne"

#: bCNC/plugins/linearize.py:11
msgid "Linearize"
msgstr ""

#: bCNC/plugins/endmilloffset.py:503
msgid "Link segments"
msgstr ""

#: bCNC/plugins/slicemesh.py:136
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading mesh: {}"
msgstr "Chargement : {} ..."

#: bCNC/bmain.py:2424
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading: {} ..."
msgstr "Chargement : {} ..."

#: bCNC/ProbePage.py:1635
msgid "Location:"
msgstr "Emplacement :"

#: bCNC/plugins/driller.py:51
msgid "M3 for laser (settings below)"
msgstr ""

#: bCNC/ControlPage.py:314 bCNC/ControlPage.py:693
msgid "MPos:"
msgstr "MPos :"

#: bCNC/ProbePage.py:2002
msgid "MX"
msgstr ""

#: bCNC/ProbePage.py:2004
msgid "MY"
msgstr ""

#: bCNC/ProbePage.py:2006
msgid "MZ"
msgstr ""

#: bCNC/ProbePage.py:775
msgid "Machine X coordinate of orientation point"
msgstr "Coordonnée machine X du point d'orientation"

#: bCNC/ProbePage.py:787
msgid "Machine Y coordinate of orientation point"
msgstr "Coordonnée machine Y du point d'orientation"

#: bCNC/ToolsPage.py:1829
msgid "Machine configuration for bCNC"
msgstr "Configuration de la machine pour bCNC"

#: bCNC/controllers/_GenericGRBL.py:163
msgid "Machine stopped. Door still ajar. Can't resume until closed."
msgstr ""
"Machine arrêtée. Porte encore entrouverte. Reprise Impossible avant sa "
"fermeture."

#: bCNC/ProbePage.py:1962
msgid "Manual Tool Change"
msgstr "Changement d'outil manuel"

#: bCNC/ProbePage.py:70
msgid "Manual Tool Change (NoProbe)"
msgstr "Changement Manuel d'Outil (Pas de Sonde)"

#: bCNC/ProbePage.py:69
msgid "Manual Tool Change (TLO)"
msgstr "Changement manuel d'outil (TLO)"

#: bCNC/ProbePage.py:68
msgid "Manual Tool Change (WCS)"
msgstr "Changement manuel d'outil (WCS)"

#: bCNC/plugins/drillmark.py:317
msgid "Manual drill mark"
msgstr ""

#: bCNC/ProbePage.py:2018
msgid "Manual tool change Machine X location"
msgstr "Position X pour changer d'outil manuellement"

#: bCNC/ProbePage.py:2029
msgid "Manual tool change Machine Y location"
msgstr "Position Y pour changer d'outil manuellement"

#: bCNC/ProbePage.py:2040
msgid "Manual tool change Machine Z location"
msgstr "Position Z pour changer d'outil manuellement"

#: bCNC/ProbePage.py:2064
msgid "Manual tool change Probing MX location"
msgstr "Position MX pour changer d'outil manuellement"

#: bCNC/ProbePage.py:2075
msgid "Manual tool change Probing MY location"
msgstr "Position MY pour changer d'outil manuellement"

#: bCNC/ProbePage.py:2086
msgid "Manual tool change Probing MZ location"
msgstr "Position MZ pour changer d'outil manuellement"

#: bCNC/ProbePage.py:186
msgid "Margins"
msgstr "Marges"

#: bCNC/bmain.py:1159 bCNC/bmain.py:1240
msgid "Margins X:"
msgstr "Marges X :"

#: bCNC/plugins/drillmark.py:46
msgid "Mark X center"
msgstr ""

#: bCNC/plugins/drillmark.py:47
msgid "Mark Y center"
msgstr ""

#: bCNC/ProbePage.py:1752
msgid "Mark camera position for calculating offset"
msgstr "Pointer la position de la caméra pour calculer le décalage"

#: bCNC/plugins/drillmark.py:50
msgid "Mark drawing power"
msgstr ""

#: bCNC/ProbePage.py:1745
msgid "Mark spindle position for calculating offset"
msgstr "Pointer la position de la broche pour le calcul du décalage"

#: bCNC/ProbePage.py:683
msgid "Markers:"
msgstr "Marqueurs :"

#: bCNC/ToolsPage.py:711 bCNC/ToolsPage.py:739 bCNC/ToolsPage.py:1421
msgid "Material"
msgstr "Matériau"

#: bCNC/ProbePage.py:1232
msgid "Max"
msgstr "Max"

#: bCNC/plugins/spiral.py:479
msgid "Max Depth per Pass"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:48
msgid "Max characters per line exceeded. Line was not processed and executed."
msgstr ""
"Nombre de caractères maximum dépassé. La ligne n'a pas été ni traitée ni "
"exécutée."

#: bCNC/plugins/halftone.py:48
msgid "Max diameter, cap limit"
msgstr "Diamètre Maxi, plafond"

#: bCNC/plugins/halftone.py:46
msgid "Max draw size (Width or Height)"
msgstr "Taille maxi du dessin (Largeur ou Hauteur)"

#: bCNC/controllers/_GenericGRBL.py:113
msgid "Max. feed rate exceeded. (grblHAL)"
msgstr ""

#: bCNC/plugins/midi2cnc.py:64
msgid "Maximum X travel"
msgstr "Déplacement Maxi en X"

#: bCNC/plugins/midi2cnc.py:65
msgid "Maximum Y travel"
msgstr "Déplacement Maxi en Y"

#: bCNC/plugins/midi2cnc.py:66
msgid "Maximum Z travel"
msgstr "Déplacement Maxi en Z"

#: bCNC/plugins/pyrograph.py:42
msgid "Maximum feed"
msgstr "Vitesse Maximum"

#: bCNC/plugins/sketch.py:48
msgid "Maximum light"
msgstr ""

#: bCNC/plugins/sketch.py:44 bCNC/plugins/heightmap.py:66
#: bCNC/plugins/pyrograph.py:40
msgid "Maximum size"
msgstr "Taille Maximum"

#: bCNC/plugins/slicemesh.py:194
msgid "Mesh sliced"
msgstr ""

#: bCNC/plugins/midi2cnc.py:71
msgid "Midi to process"
msgstr "Midi à réaliser"

#: bCNC/ProbePage.py:1230
msgid "Min"
msgstr "Min"

#: bCNC/plugins/halftone.py:49
msgid "Min diameter, cut off"
msgstr "Diamètre Mini, coupe"

#: bCNC/ToolsPage.py:1102
msgid "Min. Distance of tabs"
msgstr "Distance mini des attaches"

#: bCNC/plugins/trochoidal_3D.py:420
#, fuzzy
#| msgid "Minimum step pulse time must be greater than 3usec"
msgid "Minimum Adaptative Feed has to be greater than 0"
msgstr ""
"L'intervalle de temps minimum d'impulsion doit être plus grand que 3µsec"

#: bCNC/plugins/pyrograph.py:41
msgid "Minimum feed"
msgstr "Vitesse Minimum"

#: bCNC/controllers/_GenericGRBL.py:40
msgid "Minimum step pulse time must be greater than 3usec"
msgstr ""
"L'intervalle de temps minimum d'impulsion doit être plus grand que 3µsec"

#: bCNC/EditorPage.py:619
msgid "Mirror horizontally X=-X selected gcode"
msgstr "Miroir horizontal X=-X du G-Code sélectionné"

#: bCNC/EditorPage.py:634
msgid "Mirror vertically Y=-Y selected gcode"
msgstr "Miroir vertical Y=-Y du G-Code sélectionné"

#: bCNC/ControlPage.py:2182
msgid "Mist"
msgstr ""

#: bCNC/ControlPage.py:2009
msgid "Mode:"
msgstr "Mode :"

#: bCNC/ProbePage.py:261
msgid "Modify selected G-Code to match autolevel"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:61
msgid "More than one g-code command from same modal group found in block."
msgstr "Plus d'une commande g-code du même groupe modal trouvée dans le bloc."

#: bCNC/ToolsPage.py:714
msgid "Mount Axis"
msgstr "Axe"

#: bCNC/EditorPage.py:416 bCNC/EditorPage.py:424
msgid "Move"
msgstr "Déplacer"

#: bCNC/ControlPage.py:1428
#, fuzzy
#| msgid "Move +X"
msgid "Move +A"
msgstr "Déplacer +X"

#: bCNC/ControlPage.py:1530
#, fuzzy
#| msgid "Move +X"
msgid "Move +B"
msgstr "Déplacer +X"

#: bCNC/ControlPage.py:1468
#, fuzzy
#| msgid "Move +X +Y"
msgid "Move +B +C"
msgstr "Déplacer +X +Y"

#: bCNC/ControlPage.py:1635
#, fuzzy
#| msgid "Move +X -Y"
msgid "Move +B -C"
msgstr "Déplacer +X -Y"

#: bCNC/ControlPage.py:1455
#, fuzzy
#| msgid "Move +X"
msgid "Move +C"
msgstr "Déplacer +X"

#: bCNC/ControlPage.py:1060
msgid "Move +X"
msgstr "Déplacer +X"

#: bCNC/ControlPage.py:992
msgid "Move +X +Y"
msgstr "Déplacer +X +Y"

#: bCNC/ControlPage.py:1163
msgid "Move +X -Y"
msgstr "Déplacer +X -Y"

#: bCNC/ControlPage.py:980
msgid "Move +Y"
msgstr "Déplacer +Y"

#: bCNC/ControlPage.py:954
msgid "Move +Z"
msgstr "Déplacer +Z"

#: bCNC/ControlPage.py:1596
#, fuzzy
#| msgid "Move -X"
msgid "Move -A"
msgstr "Déplacer -X"

#: bCNC/ControlPage.py:1504
#, fuzzy
#| msgid "Move -X"
msgid "Move -B"
msgstr "Déplacer -X"

#: bCNC/ControlPage.py:1442
#, fuzzy
#| msgid "Move -X +Y"
msgid "Move -B +C"
msgstr "Déplacer -X +Y"

#: bCNC/ControlPage.py:1609
#, fuzzy
#| msgid "Move -X -Y"
msgid "Move -B -C"
msgstr "Déplacer -X -Y"

#: bCNC/ControlPage.py:1622
#, fuzzy
#| msgid "Move -X"
msgid "Move -C"
msgstr "Déplacer -X"

#: bCNC/ControlPage.py:1028
msgid "Move -X"
msgstr "Déplacer -X"

#: bCNC/ControlPage.py:967
msgid "Move -X +Y"
msgstr "Déplacer -X +Y"

#: bCNC/ControlPage.py:1137
msgid "Move -X -Y"
msgstr "Déplacer -X -Y"

#: bCNC/ControlPage.py:1150
msgid "Move -Y"
msgstr "Déplacer -Y"

#: bCNC/ControlPage.py:1124
msgid "Move -Z"
msgstr "Déplacer -Z"

#: bCNC/CNCCanvas.py:451
msgid "Move CNC gantry to mouse location"
msgstr "Déplacer le portique de la CNC à l'emplacement de la souris"

#: bCNC/ControlPage.py:451
msgid "Move Gantry"
msgstr "Déplacer Portique"

#: bCNC/EditorPage.py:451
msgid "Move all gcode such as origin is on mouse location [O]"
msgstr ""
"Changer tout le Gcode de façon à ce que l'origine corresponde à "
"l'emplacement de la souris [O]"

#: bCNC/CNCCanvas.py:760
#, fuzzy
#| msgid "Move by {:g}, {:g}, {:g}"
msgid "Move by {:g}, {:g}, {:g}"
msgstr "Déplacer de {:g}, {:g}, {:g}"

#: bCNC/ControlPage.py:461
msgid "Move gantry to mouse location [g]"
msgstr "Déplacer le portique  à l'emplacement de la souris [g]"

#: bCNC/CNCCanvas.py:445
msgid "Move graphically objects"
msgstr "Déplacer les objets graphiquement"

#: bCNC/EditorPage.py:433
msgid "Move objects [M]"
msgstr "Déplacer les objets [M]"

#: bCNC/EditorPage.py:532
msgid "Move selected g-code down [Ctrl-Down, Ctrl-PgDn]"
msgstr "Déplace le G-code sélectionné en bas [Ctrl-Bas, Ctrl-PgDn]"

#: bCNC/EditorPage.py:513
msgid "Move selected g-code up [Ctrl-Up, Ctrl-PgUp]"
msgstr "Déplace le G-code sélectionné en haut [Ctrl-Haut, Ctrl-PgUp]"

#: bCNC/plugins/simpleTranslate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Moved selected blocks"
msgstr "Pavage des blocs sélectionnés"

#: bCNC/ControlPage.py:1001 bCNC/ControlPage.py:1477
msgid "Multiply step by 10"
msgstr "Multiplier le pas par 10"

#: bCNC/ProbePage.py:1236
msgid "N"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:72
msgid "N line number value is not within the valid range of 1 - 9,999,999."
msgstr ""
"La valeur N du nombre de ligne n'est pas dans l'intervalle valide 1 - 9 999 "
"999."

#: bCNC/lib/bFileDialog.py:304 bCNC/plugins/Helical_Descent.py:77
#: bCNC/plugins/Random.py:29 bCNC/plugins/arcfit.py:32 bCNC/plugins/bowl.py:112
#: bCNC/plugins/box.py:346 bCNC/plugins/center.py:35
#: bCNC/plugins/difference.py:40 bCNC/plugins/dragknife.py:41
#: bCNC/plugins/driller.py:44 bCNC/plugins/drillmark.py:38
#: bCNC/plugins/endmilloffset.py:437 bCNC/plugins/flatten.py:288
#: bCNC/plugins/function_plot.py:23 bCNC/plugins/gear.py:160
#: bCNC/plugins/halftone.py:37 bCNC/plugins/hilbert.py:111
#: bCNC/plugins/intersection.py:41 bCNC/plugins/jigsaw.py:360
#: bCNC/plugins/linearize.py:33 bCNC/plugins/midi2cnc.py:60
#: bCNC/plugins/scaling.py:38 bCNC/plugins/sketch.py:36
#: bCNC/plugins/spiral.py:471 bCNC/plugins/spirograph.py:118
#: bCNC/plugins/text.py:37 bCNC/plugins/tile.py:27
#: bCNC/plugins/trochoidPath.py:37 bCNC/plugins/trochoidal.py:43
#: bCNC/plugins/trochoidal_3D.py:46 bCNC/plugins/zigzag.py:105
#: bCNC/plugins/heightmap.py:64 bCNC/plugins/pyrograph.py:37
#: bCNC/plugins/slicemesh.py:55 bCNC/ToolsPage.py:680 bCNC/ToolsPage.py:707
#: bCNC/ToolsPage.py:737 bCNC/ToolsPage.py:765 bCNC/ToolsPage.py:937
#: bCNC/ToolsPage.py:998 bCNC/ToolsPage.py:1073 bCNC/ToolsPage.py:1099
#: bCNC/ToolsPage.py:1981
msgid "Name"
msgstr "Nom"

#: bCNC/Utils.py:833
msgid "Name to appear on button"
msgstr "Nom du bouton"

#: bCNC/Utils.py:829
msgid "Name:"
msgstr "Nom"

#: bCNC/controllers/_GenericGRBL.py:38
msgid "Negative value received for an expected positive value."
msgstr "Valeur négative reçue au lieu d'une valeur attendue positive."

#: bCNC/FilePage.py:85
msgid "New"
msgstr "Nouveau"

#: bCNC/Ribbon.py:574
msgid "New file"
msgstr "Nouveau fichier"

#: bCNC/FilePage.py:90
msgid "New gcode/dxf file"
msgstr "Nouveau fichier GCode/DXF"

#: bCNC/lib/bFileDialog.py:863
msgid "NewFolder"
msgstr "Nouveau dossier"

#: bCNC/plugins/Random.py:45 bCNC/plugins/simpleRotate.py:49
#: bCNC/plugins/simpleTranslate.py:47 bCNC/plugins/tile.py:44
msgid "No g-code blocks selected"
msgstr "Aucun bloc G-Code sélectionné"

#: bCNC/plugins/gear.py:161
msgid "No of teeth"
msgstr "Nb de dents"

#: bCNC/EditorPage.py:122
msgid "None"
msgstr "Aucun"

#: bCNC/bmain.py:2658
msgid "Not gcode file was loaded"
msgstr "Aucun fichier GCode chargé"

#: bCNC/bmain.py:2035
msgid "Nothing to do"
msgstr "Rien à faire"

#: bCNC/ToolsPage.py:943
msgid "Number"
msgstr "Nombre"

#: bCNC/plugins/trochoidPath.py:60
#, fuzzy
#| msgid "Number of tabs"
msgid "Number of Tabs 0 = Not Tabs"
msgstr "Nombre d'attaches"

#: bCNC/plugins/zigzag.py:106
msgid "Number of lines"
msgstr "Nombre de lignes"

#: bCNC/ToolsPage.py:1101
msgid "Number of tabs"
msgstr "Nombre d'attaches"

#: bCNC/controllers/_GenericGRBL.py:36
msgid "Numeric value format is not valid or missing an expected value."
msgstr "Format numérique non valable ou sans valeur attendue."

#: bCNC/ControlPage.py:2154
msgid "OFF"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "ONLY before probing"
msgstr "SEULEMENT avant sondage"

#: bCNC/plugins/endmilloffset.py:14
#, fuzzy
#| msgid "Offset:"
msgid "Offset"
msgstr "Décalage (Offset) :"

#: bCNC/plugins/spirograph.py:121
msgid "Offset radius"
msgstr "Rayon de décalage"

#: bCNC/plugins/endmilloffset.py:462
#, fuzzy
#| msgid "Offset radius"
msgid "Offset side"
msgstr "Rayon de décalage"

#: bCNC/ProbePage.py:836 bCNC/ProbePage.py:1710
msgid "Offset:"
msgstr "Décalage (Offset) :"

#: bCNC/Utils.py:877
msgid "Ok"
msgstr "Ok"

#: bCNC/controllers/_GenericGRBL.py:115
msgid "Only homing is allowed when a limit switch is engaged. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:816
msgid "Only leave selected islands uncut"
msgstr ""

#: bCNC/lib/bFileDialog.py:337 bCNC/lib/bFileDialog.py:938
#: bCNC/lib/bFileDialog.py:1063 bCNC/FilePage.py:110 bCNC/FilePage.py:376
#: bCNC/bmain.py:2546
msgid "Open"
msgstr "Ouvrir"

#: bCNC/FilePage.py:103
msgid "Open existing gcode/dxf file [Ctrl-O]"
msgstr "Ouvrir un fichier GCode/DXF existant [Ctrl-O]"

#: bCNC/bmain.py:2345
msgid "Open file"
msgstr "Ouvrir fichier"

#: bCNC/Ribbon.py:584
msgid "Open file [Ctrl-O]"
msgstr "Ouvrir fichier [Ctrl-O]"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
msgid "Open paths"
msgstr "Parcours ouverts"

#: bCNC/FilePage.py:116
msgid "Open recent file"
msgstr "Ouvrir fichier récent"

#: bCNC/Updates.py:186
msgid "Open web browser to download bCNC"
msgstr "Ouvrir le navigateur Internet pour télécharger bCNC"

#: bCNC/ControlPage.py:129
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Open/Close connection"
msgstr "Ouvrir/Fermer le port série"

#: bCNC/FilePage.py:384
msgid "Open/Close serial port"
msgstr "Ouvrir/Fermer le port série"

#: bCNC/plugins/endmilloffset.py:443
#, fuzzy
#| msgid "Operation error"
msgid "Operation"
msgstr "Erreur d'opération"

#: bCNC/bmain.py:2083
msgid "Operation error"
msgstr "Erreur d'opération"

#: bCNC/bmain.py:2036
#, fuzzy
#| msgid "Operation {} requires some gcode to be selected"
msgid "Operation {} requires some gcode to be selected"
msgstr "L'opération {} impose de sélectionner du gcode"

#: bCNC/EditorPage.py:490 bCNC/bmain.py:1780
msgid "Optimize"
msgstr "Optimiser"

#: bCNC/FilePage.py:168
msgid "Options"
msgstr "Options"

#: bCNC/plugins/hilbert.py:113 bCNC/EditorPage.py:486
msgid "Order"
msgstr "Ordre"

#: bCNC/ProbePage.py:677 bCNC/ProbePage.py:822 bCNC/bmain.py:138
msgid "Orient"
msgstr "Orienter"

#: bCNC/EditorPage.py:441
msgid "Origin"
msgstr "Origine"

#: bCNC/plugins/box.py:355 bCNC/plugins/trochoidPath.py:52
#: bCNC/plugins/trochoidal_3D.py:76 bCNC/ToolsPage.py:1018
msgid "Overcut"
msgstr "Surcoupe"

#: bCNC/plugins/endmilloffset.py:485
#, fuzzy
#| msgid "Overcut"
msgid "Overcut corners"
msgstr "Surcoupe"

#: bCNC/lib/bFileDialog.py:985
#, fuzzy
#| msgid "Overwrite existing file %r?"
msgid "Overwrite existing file {}?"
msgstr "Écraser le fichier {} existant ?"

#: bCNC/ProbePage.py:496
msgid "POINT"
msgstr ""

#: bCNC/CNCCanvas.py:435
msgid "Pan viewport"
msgstr "Nettoyer la vue"

#: bCNC/CNCCanvas.py:2300
msgid "Pan viewport [X]"
msgstr "Nettoyer la vue [X]"

#: bCNC/TerminalPage.py:96
msgid "Parameters"
msgstr "Paramètres"

#: bCNC/EditorPage.py:49
msgid "Paste"
msgstr "Coller"

#: bCNC/EditorPage.py:55
msgid "Paste [Ctrl-V]"
msgstr "Coller [Ctrl-V]"

#: bCNC/ControlPage.py:192
msgid "Pause"
msgstr "Pause"

#: bCNC/ControlPage.py:214
msgid "Pause running program and soft reset controller to empty the buffer."
msgstr ""
"Mise en pause du travail en cours et soft-reset du contrôleur pour vider la "
"mémoire tampon."

#: bCNC/ControlPage.py:199
msgid "Pause running program. Sends either FEED_HOLD ! or CYCLE_START ~"
msgstr ""
"Mise en pause du travail en cours. Envoie soit FEED_HOLD ! ou CYCLE_START ~"

#: bCNC/ProbePage.py:1985
msgid "Pause:"
msgstr "Pause :"

#: bCNC/ToolsPage.py:940
msgid "Peck depth"
msgstr "Profondeur des paliers"

#: bCNC/plugins/simpleDrill.py:70
#, fuzzy
#| msgid "Peck depth"
msgid "Peck depth (positive)"
msgstr "Profondeur des paliers"

#: bCNC/plugins/driller.py:47
#, fuzzy
#| msgid "Peck, 0 meas None"
msgid "Peck, 0 means None"
msgstr "Palier, 0 signifie Aucun"

#: bCNC/FilePage.py:232 bCNC/bmain.py:2713 bCNC/bmain.py:2719
#: bCNC/bmain.py:2733
msgid "Pendant"
msgstr "Boîtier de contrôle"

#: bCNC/bmain.py:2720
msgid "Pendant already started:\n"
msgstr "Boîtier de contrôle déjà activé :\n"

#: bCNC/bmain.py:2714
msgid "Pendant started:\n"
msgstr "Boîtier de contrôle activé :\n"

#: bCNC/bmain.py:2734
msgid "Pendant stopped"
msgstr "Boîtier de contrôle arrêté"

#: bCNC/ProbePage.py:2135
msgid "Perform a calibration probing to determine the height"
msgstr "Réaliser une calibration du sondage pour déterminer la hauteur"

#: bCNC/ControlPage.py:100
#, fuzzy
#| msgid "Perform a homing cycle [$H]"
msgid "Perform a homing cycle [$H] now"
msgstr "Réaliser un homing complet [$H]"

#: bCNC/ToolsPage.py:1613
msgid "Perform a pocket operation on selected code"
msgstr "Réalise une poche sur le code sélectionné"

#: bCNC/ToolsPage.py:1595
msgid "Perform a profile operation on selected code"
msgstr "Réalise une opération de contour sur le code sélectionné"

#: bCNC/ProbePage.py:1935
msgid "Perform a single a tool change cycle to set the calibration field"
msgstr ""
"Réalise un seul cycle de changement d'outil pour régler le champ de "
"calibration"

#: bCNC/ProbePage.py:636
msgid "Perform a single probe cycle"
msgstr "Lancer un cycle de sondage simple"

#: bCNC/ProbePage.py:1951
msgid "Perform a tool change cycle"
msgstr "Opération pour Changer d'outil"

#: bCNC/plugins/jigsaw.py:363
msgid "Piece count"
msgstr "Comptage de la pièce"

#: bCNC/ControlPage.py:1978
msgid "Plane [G17,G18,G19]"
msgstr "Plan [G17,G18,G19]"

#: bCNC/controllers/_GenericGRBL.py:112
msgid "Plane must be ZX for threading. (grblHAL)"
msgstr ""

#: bCNC/ControlPage.py:1967
msgid "Plane:"
msgstr "Plan :"

#: bCNC/ToolsPage.py:1912
msgid "Please restart the program."
msgstr "Veuillez redémarrer le programme."

#: bCNC/bmain.py:1781
msgid "Please select the blocks of gcode you want to optimize."
msgstr "Veuillez sélectionner les blocs de gcode à optimiser."

#: bCNC/bmain.py:2609
msgid "Please stop before"
msgstr "Veuillez stopper avant"

#: bCNC/ToolsPage.py:651
msgid "Plotting Arc accuracy"
msgstr "Précision du Tracé des Arcs"

#: bCNC/plugins/scaling.py:44 bCNC/plugins/trochoidal_3D.py:96
#: bCNC/ToolsPage.py:683 bCNC/ToolsPage.py:770
msgid "Plunge Feed"
msgstr "Vitesse de plongée"

#: bCNC/plugins/trochoidal_3D.py:415
msgid "Plunge Feed has to be greater than 0"
msgstr ""

#: bCNC/ToolsPage.py:1025 bCNC/ToolsPage.py:1604
msgid "Pocket"
msgstr "Poche"

#: bCNC/plugins/flatten.py:299
msgid "Pocket type"
msgstr "Type de Poche"

#: bCNC/ProbePage.py:1164
msgid "Pointrec"
msgstr ""

#: bCNC/ProbePage.py:1967
msgid "Policy:"
msgstr "Politique :"

#: bCNC/FilePage.py:294
msgid "Port:"
msgstr "Port :"

#: bCNC/ProbePage.py:596
msgid "Pos:"
msgstr "Pos :"

#: bCNC/EditorPage.py:209
msgid "Postprocess Inkscape g-code"
msgstr ""

#: bCNC/plugins/gear.py:162
msgid "Pressure angle"
msgstr "Angle de pression"

#: bCNC/ProbePage.py:106 bCNC/ProbePage.py:114 bCNC/ProbePage.py:539
#: bCNC/ProbePage.py:627 bCNC/bmain.py:137
msgid "Probe"
msgstr "Sonde"

#: bCNC/ProbePage.py:987
msgid "Probe Center Error"
msgstr "Erreur de centrage de la sonde"

#: bCNC/ProbePage.py:369
msgid "Probe Command"
msgstr "Commande de la sonde"

#: bCNC/ProbePage.py:922 bCNC/ProbePage.py:955 bCNC/ProbePage.py:1411
#: bCNC/ProbePage.py:1420 bCNC/ProbePage.py:1435 bCNC/ProbePage.py:1444
#: bCNC/ProbePage.py:1456 bCNC/ProbePage.py:1465 bCNC/ProbePage.py:1474
msgid "Probe Error"
msgstr "Erreur avec la sonde"

#: bCNC/ProbePage.py:328
msgid "Probe Feed:"
msgstr "Vitesse de la sonde :"

#: bCNC/bmain.py:2390
msgid "Probe File modified"
msgstr "Fichier de sondage modifié"

#: bCNC/ProbePage.py:2198 bCNC/ProbePage.py:2210 bCNC/ProbePage.py:2220
#: bCNC/ProbePage.py:2230 bCNC/ProbePage.py:2240
msgid "Probe Tool Change Error"
msgstr "Erreur lors du changement de l'outil de sondage"

#: bCNC/ProbePage.py:603
msgid "Probe along X direction"
msgstr "Sonde suivant la direction X"

#: bCNC/ProbePage.py:611
msgid "Probe along Y direction"
msgstr "Sonde suivant la direction Y"

#: bCNC/ProbePage.py:619
msgid "Probe along Z direction"
msgstr "Sonde suivant la direction Z"

#: bCNC/ProbePage.py:2366
msgid "Probe configuration and probing"
msgstr "Configuration de la sonde et sondage"

#: bCNC/ProbePage.py:902
msgid "Probe connected?"
msgstr "Sonde connectée ?"

#: bCNC/controllers/_GenericGRBL.py:155
msgid "Probe protection triggered. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2391
msgid "Probe was modified do you want to save it first?"
msgstr "La sonde a été modifiée, voulez-vous d'abord l'enregistrer ?"

#: bCNC/ProbePage.py:543 bCNC/ProbePage.py:2057
msgid "Probe:"
msgstr "Sonde :"

#: bCNC/ProbePage.py:655
msgid "Probing ring internal diameter"
msgstr "Diamètre interne de l'anneau de sondage"

#: bCNC/plugins/box.py:354 bCNC/ToolsPage.py:1586
msgid "Profile"
msgstr "Profil"

#: bCNC/bmain.py:2129 bCNC/bmain.py:2231
#, fuzzy
#| msgid "Profile block distance={:g}"
msgid "Profile block distance={:g}"
msgstr "Distance du bloc contour = {:g}"

#: bCNC/plugins/bowl.py:115
msgid "Progressive"
msgstr "Progressif"

#: bCNC/Updates.py:74
msgid "Published at:"
msgstr "Publié le :"

#: bCNC/Updates.py:80
msgid "Published date of the latest github release"
msgstr "Date de publication du dernier release sur Github"

#: bCNC/plugins/midi2cnc.py:61
msgid "Pulse per unit for X"
msgstr "Impulsions par unité pour X"

#: bCNC/plugins/midi2cnc.py:62
msgid "Pulse per unit for Y"
msgstr "Impulsions par unité pour Y"

#: bCNC/plugins/midi2cnc.py:63
msgid "Pulse per unit for Z"
msgstr "Impulsions par unité pour Z"

#: bCNC/plugins/pyrograph.py:13
msgid "Pyrograph"
msgstr "Pyrograveur"

#: bCNC/plugins/pyrograph.py:97
msgid "Pyrograph abort: Can't read image file"
msgstr "Pyrograph annulé : impossible de lire le fichier"

#: bCNC/plugins/pyrograph.py:85
msgid "Pyrograph abort: Please check feed rate parameters"
msgstr "Pyrograph annulé : Veuillez vérifier la vitesse de déplacement"

#: bCNC/plugins/pyrograph.py:58
msgid "Pyrograph abort: This plugin requires PIL/Pillow"
msgstr "Pyrograph annulé : Ce plugin nécessite PIL/Pillow"

#: bCNC/plugins/pyrograph.py:80
msgid "Pyrograph abort: Tool Size must be > 0"
msgstr "Pyrograph annulé : La taille de l'outil doit être > 0"

#: bCNC/plugins/pyrograph.py:76
msgid "Pyrograph abort: please define a scan Direction"
msgstr "Pyrograph annulé : Veuillez définir une Direction de scan"

#: bCNC/plugins/pyrograph.py:38
msgid "Pyrograph tip size"
msgstr "Taille du bout du pyrograveur"

#: bCNC/ProbePage.py:474
msgid "RAPID"
msgstr ""

#: bCNC/plugins/scaling.py:45 bCNC/plugins/trochoidal_3D.py:94
msgid "RPM"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:114
msgid "RPM out of range. (grblHAL)"
msgstr ""

#: bCNC/plugins/trochoidal.py:51
msgid "Radial depth of cut (<= cutter D * 0.4)"
msgstr ""

#: bCNC/plugins/simpleArc.py:67
msgid "Radius"
msgstr ""

#: bCNC/ToolsPage.py:781
msgid "Ramp length"
msgstr "Longueur de rampe"

#: bCNC/plugins/Random.py:15
msgid "Random"
msgstr ""

#: bCNC/plugins/jigsaw.py:364
msgid "Random seed"
msgstr "Germe aléatoire"

#: bCNC/plugins/function_plot.py:26
msgid "Range of X"
msgstr ""

#: bCNC/plugins/function_plot.py:27
msgid "Range of Y"
msgstr ""

#: bCNC/ProbePage.py:592
msgid "Rapid goto to last probe location"
msgstr "Retour rapide au dernier emplacement de la sonde"

#: bCNC/bmain.py:1213
msgid "Rapid:"
msgstr "Rapide :"

#: bCNC/plugins/flatten.py:294
msgid "Raster border"
msgstr "Trame de bordure"

#: bCNC/ProbePage.py:452
msgid "Record"
msgstr ""

#: bCNC/ProbePage.py:468
msgid "Record Z coordinate?"
msgstr ""

#: bCNC/Ribbon.py:624
msgid "Redo [Ctrl-Y]"
msgstr "Refaire [Ctrl-Y]"

#: bCNC/CNCCanvas.py:2425
msgid "Redraw display [Ctrl-R]"
msgstr "Retracer l'affichage [Ctrl-R]"

#: bCNC/plugins/spiral.py:464
msgid "Reduce Diameter of 4th Axis Stock"
msgstr ""

#: bCNC/FilePage.py:361
msgid "Refresh"
msgstr ""

#: bCNC/FilePage.py:367
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Refresh list of serial ports"
msgstr "Ouvrir/Fermer le port série"

#: bCNC/ProbePage.py:1740
msgid "Register:"
msgstr "S'enregistrer :"

#: bCNC/ToolsPage.py:1455
msgid "Rename"
msgstr "Renommer"

#: bCNC/plugins/sketch.py:51
msgid "Repetition of a point"
msgstr ""

#: bCNC/FilePage.py:189 bCNC/Utils.py:568
msgid "Report"
msgstr "Rapport"

#: bCNC/Utils.py:701
msgid "Report successfully send"
msgstr "Rapport envoyé avec succès"

#: bCNC/Utils.py:702
msgid "Report was successfully uploaded to web site"
msgstr "Rapport envoyé avec succès au site Web"

#: bCNC/ControlPage.py:136 bCNC/ControlPage.py:2080
msgid "Reset"
msgstr "Remise à zéro"

#: bCNC/ControlPage.py:2082
msgid "Reset override to 100%"
msgstr "Ré-initialiser le forçage à 100%"

#: bCNC/plugins/function_plot.py:25
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "Resolution"
msgstr "Résolution (degrés)"

#: bCNC/plugins/bowl.py:114
msgid "Resolution (degrees)"
msgstr "Résolution (degrés)"

#: bCNC/TerminalPage.py:67
msgid "Restore All"
msgstr "Restaurer Tout"

#: bCNC/TerminalPage.py:64
msgid "Restore Settings"
msgstr "Restaurer les Réglages"

#: bCNC/TerminalPage.py:66
msgid "Restore Workspace"
msgstr "Restaurer l'espace de travail"

#: bCNC/ControlPage.py:493
msgid "Resume"
msgstr "Reprendre"

#: bCNC/ControlPage.py:1517
msgid "Return ABC to 0."
msgstr ""

#: bCNC/EditorPage.py:691
msgid "Reverse"
msgstr "Inverser"

#: bCNC/EditorPage.py:699
msgid "Reverse cut direction for selected gcode blocks"
msgstr "Inverser la direction de coupe pour les blocs de GCode sélectionnés"

#: bCNC/EditorPage.py:466
msgid "Right"
msgstr "Droite"

#: bCNC/plugins/drillmark.py:58
msgid "Ring mark (d/2)"
msgstr ""

#: bCNC/plugins/spiral.py:94
#, fuzzy
#| msgid "All accepted"
msgid "Risk Accepted"
msgstr "Tout accepté"

#: bCNC/plugins/spiral.py:475
msgid "Rotary Alignment Axis"
msgstr ""

#: bCNC/plugins/spiral.py:476
#, fuzzy
#| msgid "Mount Axis"
msgid "Rotary Axis"
msgstr "Axe"

#: bCNC/EditorPage.py:588
msgid "Rotate selected gcode by 180deg"
msgstr "Rotation à 180 ° du G-Code sélectionné"

#: bCNC/EditorPage.py:573
msgid "Rotate selected gcode clock-wise (-90deg)"
msgstr "Rotation à sens horaire (à -90 °) du G-Code sélectionné"

#: bCNC/EditorPage.py:604
msgid "Rotate selected gcode counter-clock-wise (90deg)"
msgstr "Rotation à sens anti-horaire (à 90 °) du G-Code sélectionné"

#: bCNC/plugins/simpleRotate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Rotated selected blocks"
msgstr "Pavage des blocs sélectionnés"

#: bCNC/plugins/simpleRotate.py:19
msgid "Rotates a block to a new position"
msgstr ""

#: bCNC/ProbePage.py:1647
msgid "Rotation:"
msgstr "Rotation :"

#: bCNC/EditorPage.py:213
msgid "Round"
msgstr "Arrondi"

#: bCNC/EditorPage.py:647
msgid "Route"
msgstr "Parcours"

#: bCNC/CNCCanvas.py:2329
msgid "Ruler [R]"
msgstr "Règle [R]"

#: bCNC/Sender.py:685
msgid "Run ended"
msgstr "Travail terminé"

#: bCNC/ControlPage.py:184
msgid "Run g-code commands from editor to controller"
msgstr "Exécute les commandes G-Code de l'éditeur vers le contrôleur"

#: bCNC/bmain.py:635
msgid "Running"
msgstr "En marche"

#: bCNC/Updates.py:65
msgid "Running version of bCNC"
msgstr "Version actuelle de bCNC"

#: bCNC/bmain.py:2695
msgid "Running..."
msgstr "En Marche ..."

#: bCNC/controllers/_GenericGRBL.py:123
msgid "SD Card directory listing failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:124
msgid "SD Card directory not found. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:125
msgid "SD Card file empty. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:122
msgid "SD Card file open/read failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:121
msgid "SD Card mount failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/slicemesh.py:40
msgid "STL/PLY Slicer"
msgstr ""

#: bCNC/ToolsPage.py:740
msgid "Safe Z"
msgstr "Sécurité Z"

#: bCNC/plugins/simpleDrill.py:73
msgid "Safe z secu for G0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:53
msgid "Safety door detected as opened and door state initiated."
msgstr "Porte de sécurité détectée ouverte et état de la porte enclenché."

#: bCNC/lib/bFileDialog.py:977 bCNC/FilePage.py:153
msgid "Save"
msgstr "Enregistrer"

#: bCNC/lib/bFileDialog.py:972
msgid "Save As"
msgstr "Enregistrer Sous"

#: bCNC/Ribbon.py:594
msgid "Save all [Ctrl-S]"
msgstr "Enregistrer tout [Ctrl-S]"

#: bCNC/bmain.py:2366
msgid "Save file"
msgstr "Enregistrer fichier"

#: bCNC/FilePage.py:159
msgid "Save gcode/dxf AS"
msgstr "Enregistrer gcode/DXF sous"

#: bCNC/FilePage.py:145
msgid "Save gcode/dxf file [Ctrl-S]"
msgstr "Enregistrer fichier gcode/DXF [Ctrl-S]"

#: bCNC/ProbePage.py:1680
msgid "Scale:"
msgstr "Échelle :"

#: bCNC/plugins/scaling.py:22
msgid "Scaling"
msgstr ""

#: bCNC/plugins/scaling.py:291
msgid "Scaling Generated"
msgstr ""

#: bCNC/plugins/scaling.py:175
msgid "Scaling abort: Please select some path"
msgstr ""

#: bCNC/plugins/scaling.py:30
msgid "Scaling the selected block"
msgstr ""

#: bCNC/plugins/heightmap.py:67 bCNC/ProbePage.py:241 bCNC/ProbePage.py:271
msgid "Scan"
msgstr "Scanner"

#: bCNC/ProbePage.py:247
msgid "Scan Autolevel Margins"
msgstr ""

#: bCNC/ProbePage.py:280
msgid "Scan probed area for level information on Z plane"
msgstr "Scanne a zone sondée pour les informations de niveau sur le plan Z"

#: bCNC/plugins/heightmap.py:72
msgid "ScanDir"
msgstr "ScanDir"

#: bCNC/EditorPage.py:96
msgid "Select"
msgstr "Sélectionner"

#: bCNC/FilePage.py:307
msgid "Select (or manual enter) port to connect"
msgstr "Choisir (ou saisir) le port de connexion"

#: bCNC/EditorPage.py:112
msgid "Select all blocks [Ctrl-A]"
msgstr "Sélectionner tous les blocs [Ctrl-A]"

#: bCNC/EditorPage.py:160
msgid "Select all blocks from current layer"
msgstr "Sélectionner tous les blocs de la couche actuelle"

#: bCNC/FilePage.py:323
msgid "Select connection baud rate"
msgstr "Choisir la vitesse de connexion en baud"

#: bCNC/FilePage.py:340
msgid "Select controller board"
msgstr "Choisir la carte contrôleur"

#: bCNC/CNCCanvas.py:430
msgid "Select objects with mouse"
msgstr "Choisir des objets à la souris"

#: bCNC/ProbePage.py:696
msgid "Select orientation marker"
msgstr "Choisir le marqueur d'orientation"

#: bCNC/ControlPage.py:2078
msgid "Select override type."
msgstr "Sélectionner le type de forçage."

#: bCNC/CNCCanvas.py:2288
msgid "Select tool [S]"
msgstr "Choisir outil [S]"

#: bCNC/plugins/Helical_Descent.py:79
#, fuzzy
msgid "Selected Block"
msgstr "Pavage des blocs sélectionnés"

#: bCNC/FilePage.py:197
msgid "Send Error Report"
msgstr "Envoyer Rapport d'Erreur"

#: bCNC/ProbePage.py:66
msgid "Send M6 commands"
msgstr "Envoie les commandes M6"

#: bCNC/Utils.py:617
msgid "Send report"
msgstr "Envoyer rapport"

#: bCNC/FilePage.py:289
msgid "Serial"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial Error"
msgstr "Erreur port série"

#: bCNC/TerminalPage.py:241
msgid "Serial Terminal"
msgstr "Terminal Série"

#: bCNC/bmain.py:2601
msgid "Serial is not connected"
msgstr "Port série non connecté"

#: bCNC/ControlPage.py:735
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set A coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:801
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set ABC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:751
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set B coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:785
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set BC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:767
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set C coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:2101
msgid "Set Feed/Rapid/Spindle Override. Right or Double click to reset."
msgstr ""
"Régler forçage Avancement/Rapidité/Rotation. Droit ou double-clic pour ré-"
"initialiser."

#: bCNC/ControlPage.py:436
msgid "Set WPOS"
msgstr "Régler WPOS"

#: bCNC/ControlPage.py:445
msgid "Set WPOS to mouse location"
msgstr "Placer WPOS à l'emplacement de la souris"

#: bCNC/ControlPage.py:357
msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées X à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:407
msgid "Set XY coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:424
msgid "Set XYZ coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:373
msgid "Set Y coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées Y à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ControlPage.py:389
msgid "Set Z coordinate to zero (or to typed coordinate in WPos)"
msgstr "Met les coordonnées Z à zéro (ou aux coordonnées saisies dans WPos)"

#: bCNC/ProbePage.py:320
msgid "Set initial probe feed rate for tool change and calibration"
msgstr ""
"Régler la vitesse initiale de la sonde pour le changement d'outil et la "
"calibration"

#: bCNC/CNCCanvas.py:458
msgid "Set mouse location as current machine position (X/Y only)"
msgstr ""
"Fixer la position actuelle de la machine à l'emplacement de la souris (X/Y "
"seulement)"

#: bCNC/ControlPage.py:2056
msgid "Set position [G92 X# Y# Z#]"
msgstr ""

#: bCNC/ProbePage.py:340
msgid "Set probe feed rate"
msgstr "Régler la vitesse d'avancement de la sonde"

#: bCNC/ControlPage.py:2135
msgid "Set spindle RPM"
msgstr "Fixer la rotation RPM de la broche"

#: bCNC/ProbePage.py:354
msgid "Set tool offset for probing"
msgstr "Régler le décalage de l'outil pour le sondage"

#: bCNC/controllers/_GenericController.py:163
#, fuzzy
#| msgid "Set workspace {} to {}"
msgid "Set workspace {} to {}"
msgstr "Réglez l'espace de travail {} à {}"

#: bCNC/ToolsPage.py:1019
msgid "Sets if we want to overcut or not."
msgstr ""

#: bCNC/TerminalPage.py:80
msgid "Settings"
msgstr "Réglages"

#: bCNC/ProbePage.py:165
msgid "Setup probing for manual tool change"
msgstr "Réglage sonde pour le changement manuel d'outil"

#: bCNC/ToolsPage.py:710
msgid "Shape"
msgstr "Forme"

#: bCNC/plugins/jigsaw.py:369
msgid "Shape of the tap"
msgstr "Forme du robinet"

#: bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:581
#: bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:584
#: bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:587
#: bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:590
msgid "Shift-"
msgstr "Maj-"

#: bCNC/ToolsPage.py:1837
msgid "Shortcuts"
msgstr "Raccourcis"

#: bCNC/ToolsPage.py:1845
msgid "Shortcuts configuration"
msgstr "Configuration des raccourcis"

#: bCNC/ToolsPage.py:1011
msgid "Should we machine on inside or outside of the shape?"
msgstr ""

#: bCNC/ControlPage.py:473
msgid "Show Info"
msgstr "Montrer Info"

#: bCNC/EditorPage.py:773
msgid "Show cutting information on selected blocks [Ctrl-n]"
msgstr "Afficher l'information de coupe sur les blocs sélectionnés [Ctrl-n]"

#: bCNC/EditorPage.py:757
msgid "Show statistics for enabled gcode"
msgstr "Affiche les statistiques pour le Gcode activé"

#: bCNC/ProbePage.py:121
msgid "Simple probing along a direction"
msgstr "Sondage simple selon une direction"

#: bCNC/plugins/heightmap.py:77
msgid "Single pass"
msgstr "Passe unique"

#: bCNC/lib/bFileDialog.py:306 bCNC/plugins/hilbert.py:112
msgid "Size"
msgstr "Taille"

#: bCNC/ToolsPage.py:1004
msgid "Size of this endmill will be used as offset distance"
msgstr ""

#: bCNC/plugins/sketch.py:20
msgid "Sketch"
msgstr "Sketch"

#: bCNC/plugins/sketch.py:255
msgid "Sketch abort: Can't read image file"
msgstr "Esquisse annulée : impossible de lire le fichier image"

#: bCNC/plugins/sketch.py:244
msgid "Sketch abort: Please let me draw at least 1 squiggle"
msgstr "Esquisse annulée : Merci de me laisser dessiner au moins 1 gribouillis"

#: bCNC/plugins/sketch.py:248
msgid "Sketch abort: Squiggle Length must be > 0"
msgstr "Esquisse annulée : La longueur du gribouillis doit être >0"

#: bCNC/plugins/sketch.py:200
msgid "Sketch abort: This plugin requires PIL/Pillow to read image data"
msgstr "Esquisse annulée : Ce plugin nécessite PIL/Pillow"

#: bCNC/plugins/sketch.py:234
msgid "Sketch abort: Too small to draw anything!"
msgstr "Esquisse annulée : Trop petit pour dessiner quoi que ce soit !"

#: bCNC/plugins/slicemesh.py:179
msgid "Slicing {} {:f} in {:f} -> {:f} of {}"
msgstr ""

#: bCNC/plugins/function_plot.py:34
#, fuzzy
#| msgid "Line length"
msgid "Small line length"
msgstr "Longueur de la Ligne"

#: bCNC/controllers/_GenericGRBL.py:46
msgid "Soft limits cannot be enabled without homing also enabled."
msgstr ""
"Les fins de course logicielles ne peuvant pas être activées si le Homing ne "
"l'est pas non plus."

#: bCNC/ControlPage.py:143
msgid "Software reset of controller [ctrl-x]"
msgstr "Soft-Reset du contrôleur [Ctrl-X]"

#: bCNC/plugins/midi2cnc.py:32
msgid "Sound your machine from a midi file"
msgstr "Faire sonner votre machine à partir d'un fichier midi"

#: bCNC/ControlPage.py:2112
msgid "Spindle"
msgstr "Broche"

#: bCNC/ProbePage.py:1737
msgid "Spindle Z position when camera was registered"
msgstr "Position Z de la broche quand la caméra a été enregistrée"

#: bCNC/controllers/_GenericGRBL.py:156
msgid "Spindle at speed timeout. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:654
msgid "Spindle max (RPM)"
msgstr "Rotation max (RPM)"

#: bCNC/ToolsPage.py:653
msgid "Spindle min (RPM)"
msgstr "Rotation min (RPM)"

#: bCNC/ProbePage.py:1854
msgid "Spindle position is not registered"
msgstr "Position de la broche non enregistrée"

#: bCNC/ProbePage.py:1846
msgid "Spindle position is registered"
msgstr "Position de la broche enregistrée"

#: bCNC/ProbePage.py:1855
msgid "Spindle position must be registered before camera"
msgstr "La position de la broche doit être enregistrée avant la caméra"

#: bCNC/plugins/spiral.py:18
#, fuzzy
#| msgid "Spirograph"
msgid "Spiral"
msgstr "Spirograph"

#: bCNC/plugins/spiral.py:69
#, fuzzy
msgid "Spiral abort: Approach height must be greater than Z Start"
msgstr "Carte 3D annulée : La profondeur doit être < 0"

#: bCNC/plugins/spiral.py:74
msgid "Spiral abort: Depth Reduction must be negative"
msgstr ""

#: bCNC/plugins/spiral.py:156 bCNC/plugins/spiral.py:179
#: bCNC/plugins/spiral.py:192 bCNC/plugins/spiral.py:220
#: bCNC/plugins/spiral.py:284 bCNC/plugins/spiral.py:318
#: bCNC/plugins/spiral.py:357 bCNC/plugins/spiral.py:379
#: bCNC/plugins/spiral.py:430
msgid "Spiral abort: Rotary Axis Not Assigned."
msgstr ""

#: bCNC/plugins/spiral.py:60
#, fuzzy
#| msgid "Flatten abort: Pocket Type is undefined"
msgid "Spiral abort: Rotary Axis is undefined"
msgstr "Surfaçage annulé : Type de poche non définie"

#: bCNC/plugins/spiral.py:64
#, fuzzy
#| msgid "Flatten abort: Pocket Type is undefined"
msgid "Spiral abort: Spiral Type is undefined"
msgstr "Surfaçage annulé : Type de poche non définie"

#: bCNC/plugins/spiral.py:80
msgid "Spiral abort: Step Over exceeds tool limits"
msgstr ""

#: bCNC/plugins/spiral.py:124
#, fuzzy
#| msgid "Flatten abort: Flatten area is too small for this End Mill."
msgid "Spiral abort: Stock Length is too small for this End Mill."
msgstr ""
"Surfaçage annulé : La zone à aplanir est trop petite pour cette fraise."

#: bCNC/plugins/spiral.py:100
#, fuzzy
msgid "Spiral abort: Stock Length to cut must be positive"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/spiral.py:518
msgid "Spiral: Reduced 4th Axis Stock"
msgstr ""

#: bCNC/plugins/spirograph.py:17
msgid "Spirograph"
msgstr "Spirograph"

#: bCNC/EditorPage.py:401
msgid "Split"
msgstr ""

#: bCNC/EditorPage.py:407
msgid "Split selected blocks"
msgstr ""

#: bCNC/ToolsPage.py:795
msgid "Spring pass"
msgstr ""

#: bCNC/plugins/sketch.py:46
msgid "Squiggle Length"
msgstr "Longueur du Gribouillis"

#: bCNC/plugins/sketch.py:45
msgid "Squiggle total count"
msgstr "Total du Gribouillis"

#: bCNC/ControlPage.py:178 bCNC/FilePage.py:238
msgid "Start"
msgstr "Début"

#: bCNC/plugins/simpleArc.py:68
msgid "Start Angle in Degrees"
msgstr ""

#: bCNC/ControlPage.py:2175
msgid "Start flood (M8)"
msgstr ""

#: bCNC/ControlPage.py:2189
msgid "Start mist (M7)"
msgstr ""

#: bCNC/FilePage.py:246
msgid "Start pendant"
msgstr "Activer le boîtier de contrôle"

#: bCNC/ToolsPage.py:652
msgid "Start up"
msgstr "Démarrage"

#: bCNC/ControlPage.py:2121
msgid "Start/Stop spindle (M3/M5)"
msgstr "Lancer/Stopper la broche (M3/M5)"

#: bCNC/TerminalPage.py:140
msgid "Startup"
msgstr "Démarrage"

#: bCNC/ControlPage.py:1877 bCNC/TerminalPage.py:110
msgid "State"
msgstr "État"

#: bCNC/ControlPage.py:611 bCNC/ControlPage.py:918
#, fuzzy
#| msgid "State: {}"
msgid "State: {}"
msgstr "État :{}"

#: bCNC/EditorPage.py:750 bCNC/bmain.py:1133
msgid "Statistics"
msgstr "Statistiques"

#: bCNC/ControlPage.py:236
msgid "Status:"
msgstr "Status :"

#: bCNC/ProbePage.py:1234
msgid "Step"
msgstr "Pas"

#: bCNC/plugins/zigzag.py:109
msgid "Step distance"
msgstr "Longueur du Pas :"

#: bCNC/ControlPage.py:1562
#, fuzzy
#| msgid "Step for Z move operation"
msgid "Step for A move operation"
msgstr "Pas pour déplacement en Z"

#: bCNC/ControlPage.py:1090
msgid "Step for Z move operation"
msgstr "Pas pour déplacement en Z"

#: bCNC/ControlPage.py:1075 bCNC/ControlPage.py:1546
msgid "Step for every move operation"
msgstr "Pas pour toute opération de déplacement"

#: bCNC/ControlPage.py:1265 bCNC/ControlPage.py:1739
#, fuzzy
#| msgid "Step: {:g}"
msgid "Step: {:g}"
msgstr "Pas : {:g}"

#: bCNC/ControlPage.py:1744
#, fuzzy
#| msgid "Step: {:g}    Zstep:{:g} "
msgid "Step: {:g}   Astep:{:g} "
msgstr "Pas : {:g}\tPas Z : {:g}"

#: bCNC/ControlPage.py:1270
#, fuzzy
#| msgid "Step: {:g}    Zstep:{:g} "
msgid "Step: {:g}  Zstep: {:g} "
msgstr "Pas : {:g}\tPas Z : {:g}"

#: bCNC/ToolsPage.py:718
msgid "Stepover %"
msgstr "% de fraise engagée pour couper"

#: bCNC/plugins/spiral.py:480
msgid "Stepover (spiral=mm, lines=deg)"
msgstr ""

#: bCNC/ToolsPage.py:1405
msgid "Stock"
msgstr "Stock"

#: bCNC/ToolsPage.py:1413
msgid "Stock material currently on machine"
msgstr "Matériau enregistré actuellement machiné"

#: bCNC/ControlPage.py:208 bCNC/FilePage.py:251
msgid "Stop"
msgstr "Arrêt"

#: bCNC/ControlPage.py:2161
msgid "Stop cooling (M9)"
msgstr ""

#: bCNC/FilePage.py:259
msgid "Stop pendant"
msgstr "Arrêter le boîtier de contrôle"

#: bCNC/ToolsPage.py:741 bCNC/ToolsPage.py:766
msgid "Surface Z"
msgstr "Surface Z"

#: bCNC/ProbePage.py:1543
msgid "Switch To"
msgstr "Basculer Vers"

#: bCNC/ProbePage.py:1553
msgid "Switch between camera and spindle"
msgstr "Basculer entre la caméra et la broche"

#: bCNC/ControlPage.py:1904
#, fuzzy
#| msgid "Switch to workspace {}"
msgid "Switch to workspace {}"
msgstr "Basculer vers l'espace de travail {}"

#: bCNC/ProbePage.py:347
msgid "TLO"
msgstr "TLO"

#: bCNC/ControlPage.py:2029
msgid "TLO:"
msgstr ""

#: bCNC/ToolsPage.py:1638
msgid "Tabs"
msgstr "Attaches"

#: bCNC/plugins/trochoidPath.py:63
#, fuzzy
#| msgid "Diameter"
msgid "Tabs Diameter"
msgstr "Diamètre"

#: bCNC/plugins/trochoidPath.py:64
#, fuzzy
#| msgid "Height"
msgid "Tabs Height"
msgstr "Hauteur"

#: bCNC/ToolsPage.py:1153
msgid "Tabs error"
msgstr "Erreur d'attaches"

#: bCNC/plugins/driller.py:46 bCNC/plugins/spirograph.py:122
#: bCNC/plugins/trochoidPath.py:53 bCNC/plugins/trochoidal_3D.py:82
#: bCNC/ToolsPage.py:767 bCNC/ToolsPage.py:939
msgid "Target Depth"
msgstr "Profondeur finale"

#: bCNC/plugins/simpleDrill.py:69
msgid "Target z (negative under surface)"
msgstr ""

#: bCNC/TerminalPage.py:38 bCNC/TerminalPage.py:189
msgid "Terminal"
msgstr "Terminal"

#: bCNC/TerminalPage.py:205
msgid "Terminal communication with controller"
msgstr "Communication avec le controleur dans le terminal"

#: bCNC/plugins/text.py:94
#, fuzzy
#| msgid "Text abort: That embarrassing, I can't read this font file!"
msgid "Text abort: That's embarrassing, I can't read this font file!"
msgstr ""
"Texte annulé : C'est ennuyeux, impossible de lire le fichier de police !"

#: bCNC/plugins/text.py:62
msgid "Text abort: please input a Font size > 0"
msgstr "Texte annulé : Veuillez entrer une taille de Police > 0"

#: bCNC/plugins/text.py:65
msgid "Text abort: please select a font file"
msgstr "Texte annulé : Veuillez choisir un fichier de police"

#: bCNC/plugins/text.py:38
msgid "Text to generate"
msgstr "Texte à générer"

#: bCNC/Utils.py:573
#, fuzzy
#| msgid "The following report is about to be send to the author of {}"
msgid "The following report is about to be send to the author of {}"
msgstr "Le rapport suivant est sur le point d'être envoyé à l'auteur de {}"

#: bCNC/plugins/sketch.py:239
msgid "The maximum illumination shouldn't be more than 250!"
msgstr ""

#: bCNC/Utils.py:695
msgid "There was a problem connecting to the web site"
msgstr "Il y a eu un problème de connexion au site Web"

#: bCNC/ToolsPage.py:742
msgid "Thickness"
msgstr "Épaisseur"

#: bCNC/plugins/Helical_Descent.py:57
msgid "This is my Helical Descent"
msgstr ""

#: bCNC/plugins/tile.py:13
msgid "Tile"
msgstr "Pavage"

#: bCNC/plugins/Random.py:44 bCNC/plugins/simpleRotate.py:48
#: bCNC/plugins/simpleTranslate.py:46 bCNC/plugins/tile.py:44
msgid "Tile error"
msgstr "Erreur de pavage"

#: bCNC/plugins/Random.py:80 bCNC/plugins/tile.py:85
msgid "Tiled selected blocks"
msgstr "Pavage des blocs sélectionnés"

#: bCNC/bmain.py:1222 bCNC/bmain.py:1301
msgid "Time:"
msgstr "Temps:"

#: bCNC/CNCCanvas.py:2437
#, fuzzy
msgid "Timeout:"
msgstr "Temps:"

#: bCNC/CNCCanvas.py:2347
msgid "Toggle display of axes"
msgstr "Activer (ou pas) l'affichage des axes"

#: bCNC/CNCCanvas.py:2418
msgid "Toggle display of camera"
msgstr "Activer (ou pas) l'affichage de la caméra"

#: bCNC/CNCCanvas.py:2357
msgid "Toggle display of grid lines"
msgstr "Activer (ou pas) l'affichage des lignes"

#: bCNC/CNCCanvas.py:2367
msgid "Toggle display of margins"
msgstr "Activer (ou pas) l'affichage des marges"

#: bCNC/CNCCanvas.py:2388
msgid "Toggle display of paths (G1,G2,G3)"
msgstr "Activer (ou pas) l'affichage des parcours (G1, G2, G3)"

#: bCNC/CNCCanvas.py:2378
msgid "Toggle display of probe"
msgstr "Activer (ou pas) l'affichage de la sonde"

#: bCNC/CNCCanvas.py:2398
msgid "Toggle display of rapid motion (G0)"
msgstr "Activer (ou pas) l'affichage des mouvements rapides de déplacement"

#: bCNC/CNCCanvas.py:2408
msgid "Toggle display of workarea"
msgstr "Activer (ou pas) l'affichage de la zone de travail"

#: bCNC/EditorPage.py:316
msgid "Toggle enable/disable block of g-code [Ctrl-L]"
msgstr "Bouton qui active/désactive bloc de G-Code [Ctrl-L]"

#: bCNC/EditorPage.py:359
msgid "Toggle expand/collapse blocks of gcode [Ctrl-E]"
msgstr "Bouton qui étend/replie des blocs de G-Code [Ctrl-L]"

#: bCNC/ToolsPage.py:1662
msgid "Toggle island"
msgstr ""

#: bCNC/ProbePage.py:158
msgid "Tool"
msgstr "Outil"

#: bCNC/Utils.py:852
msgid "Tool Tip:"
msgstr "Info-bulle :"

#: bCNC/ProbePage.py:1979
msgid "Tool change policy"
msgstr "Politique de changement d'outil"

#: bCNC/ControlPage.py:2041
msgid "Tool length offset [G43.1#]"
msgstr ""

#: bCNC/ControlPage.py:1957
msgid "Tool number [T#]"
msgstr "N° d'outil [T#]"

#: bCNC/ProbePage.py:2127
msgid "Tool probe height"
msgstr "Hauteur de l'outil de sondage"

#: bCNC/CNCCanvas.py:2273 bCNC/ControlPage.py:1950
msgid "Tool:"
msgstr "Outil :"

#: bCNC/Utils.py:857
msgid "Tooltip for button"
msgstr "Info-bulle du bouton"

#: bCNC/EditorPage.py:462
msgid "Top"
msgstr "Dessus"

#: bCNC/EditorPage.py:459
msgid "Top-Left"
msgstr "Haut-Gauche"

#: bCNC/EditorPage.py:465
msgid "Top-Right"
msgstr "Haut-Droit"

#: bCNC/EditorPage.py:558
msgid "Transform"
msgstr "Transformer"

#: bCNC/plugins/simpleTranslate.py:19
msgid "Translates a block to a new position"
msgstr ""

#: bCNC/ToolsPage.py:647
msgid "Travel x"
msgstr "Déplacement x"

#: bCNC/ToolsPage.py:648
msgid "Travel y"
msgstr "Déplacement y"

#: bCNC/ToolsPage.py:649
msgid "Travel z"
msgstr "Déplacement z"

#: bCNC/plugins/trochoidal_3D.py:47
msgid "Trochoid Cut Diameter"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:402
#, fuzzy
msgid "Trochoid Cut Diameter has to be greater than End mill"
msgstr ""
"Pixellisation annulée : Le diamètre Minimum doit être inférieur à celui "
"Maximum"

#: bCNC/plugins/trochoidal_3D.py:1672
msgid "Trochoid Generated"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:397
#, fuzzy
#| msgid "Driller abort: Please select some path"
msgid "Trochoid abort: Please select some path"
msgstr "Perçage annulé : Veuillez choisir un parcours"

#: bCNC/plugins/trochoidal.py:52
msgid "Trochoid diameter (<= cutter D)"
msgstr ""

#: bCNC/plugins/trochoidal.py:49
msgid "Trochoid entry (prepare for helicut)"
msgstr ""

#: bCNC/plugins/trochoidal.py:18
msgid "Trochoidal"
msgstr ""

#: bCNC/plugins/trochoidPath.py:20
msgid "Trochoidal Path"
msgstr ""

#: bCNC/plugins/trochoidal.py:24
msgid "Trochoidal g-code postprocessor"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:30
msgid "Trochoidcut"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:50
msgid "Trochoids Advance"
msgstr ""

#: bCNC/ProbePage.py:1568
msgid "Turn on/off edge detection"
msgstr "Activer/Éteindre la détection de bord"

#: bCNC/ProbePage.py:1583
msgid "Turn on/off freeze image"
msgstr "Activer/Éteindre le freeze image"

#: bCNC/lib/bFileDialog.py:305 bCNC/ToolsPage.py:709
msgid "Type"
msgstr "Type"

#: bCNC/plugins/trochoidal_3D.py:56
msgid "Type of Splice"
msgstr ""

#: bCNC/plugins/drillmark.py:56
#, fuzzy
#| msgid "Shape of the tap"
msgid "Type of the mark"
msgstr "Forme du robinet"

#: bCNC/Ribbon.py:604
msgid "Undo [Ctrl-Z]"
msgstr "Défaire [Ctrl-Z]"

#: bCNC/ToolsPage.py:636
msgid "Units (inches)"
msgstr "Unités (pouces)"

#: bCNC/ControlPage.py:1942
msgid "Units [G20, G21]"
msgstr "Unités [G20, G21]"

#: bCNC/ControlPage.py:1931
msgid "Units:"
msgstr "Unités :"

#: bCNC/ControlPage.py:108
msgid "Unlock"
msgstr "Débloquer"

#: bCNC/ControlPage.py:115
msgid "Unlock controller [$X]"
msgstr "Débloquer le contrôleur [$X]"

#: bCNC/EditorPage.py:128
msgid "Unselect all blocks [Ctrl-Shift-A]"
msgstr "Désélectionner tous les blocs [Ctrl-Shift-A]"

#: bCNC/controllers/_GenericGRBL.py:60
msgid "Unsupported or invalid g-code command found in block."
msgstr "Commande G-code non supportée ou invalide trouvée dans le bloc."

#: bCNC/EditorPage.py:505
msgid "Up"
msgstr "Haut"

#: bCNC/FilePage.py:203
msgid "Updates"
msgstr "Mises à Jour"

#: bCNC/plugins/heightmap.py:56
#, fuzzy
#| msgid "Use a brightess map to create a variable Z path"
msgid "Use a brightness map to create a variable Z path"
msgstr "Utiliser une carte d'intensité pour créer un parcours Z variable"

#: bCNC/plugins/driller.py:49
msgid "Use anchor"
msgstr ""

#: bCNC/ToolsPage.py:1921
msgid "User File"
msgstr "Fichier de l'Utilisateur"

#: bCNC/Utils.py:823
msgid "User configurable button"
msgstr "Bouton configurable par l'utilisateur"

#: bCNC/ToolsPage.py:1981
msgid "Value"
msgstr "Valeur"

#: bCNC/controllers/_GenericGRBL.py:107
msgid "Value out of range. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:118
msgid "Value word conflict. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:627
msgid "Vertical"
msgstr "Vertical"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
#, fuzzy
#| msgid "WARNING: {}"
msgid "WARNING: {}"
msgstr "ATTENTION : {}"

#: bCNC/ControlPage.py:261 bCNC/ProbePage.py:765
msgid "WPos:"
msgstr "WPos :"

#: bCNC/bmain.py:2524
msgid "Warning"
msgstr "Alerte"

#: bCNC/plugins/function_plot.py:110
#, fuzzy
#| msgid "Warning"
msgid "Warning: "
msgstr "Alerte"

#: bCNC/ToolsPage.py:621
msgid "Web Camera"
msgstr "Caméra"

#: bCNC/ToolsPage.py:624
msgid "Web Camera Angle"
msgstr "Angle de la Webcam"

#: bCNC/ToolsPage.py:623
msgid "Web Camera Height"
msgstr "Hauteur de la Webcam"

#: bCNC/ToolsPage.py:622
msgid "Web Camera Width"
msgstr "Largeur de la Webcam"

#: bCNC/plugins/box.py:348
msgid "Width Dx"
msgstr "Largeur Dx"

#: bCNC/plugins/flatten.py:291
msgid "Width to flatten"
msgstr "Largeur à surfacer"

#: bCNC/ProbePage.py:149
msgid "Work surface camera view and alignment"
msgstr "Vue caméra de la surface de travail et alignement"

#: bCNC/plugins/halftone.py:52 bCNC/plugins/sketch.py:43
#: bCNC/plugins/text.py:39 bCNC/plugins/heightmap.py:65
#: bCNC/plugins/pyrograph.py:39
msgid "Working Depth"
msgstr "Profondeur de travail"

#: bCNC/plugins/simpleDrill.py:67 bCNC/ControlPage.py:1015
msgid "X"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:80
msgid "X Initial"
msgstr ""

#: bCNC/plugins/scaling.py:39
msgid "X Scale"
msgstr ""

#: bCNC/plugins/scaling.py:42
#, fuzzy
#| msgid "Center"
msgid "X Y Center"
msgstr "Centrer"

#: bCNC/ProbePage.py:1275
msgid "X bins"
msgstr "X bins"

#: bCNC/plugins/function_plot.py:30
#, fuzzy
#| msgid "Internal Dimensions"
msgid "X dimension"
msgstr "Dimensions Intérieures"

#: bCNC/ProbePage.py:1255
msgid "X maximum"
msgstr "X maximum"

#: bCNC/ProbePage.py:1247
msgid "X minimum"
msgstr "X minimum"

#: bCNC/plugins/function_plot.py:32
msgid "X number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:289 bCNC/plugins/spiral.py:472
msgid "X start"
msgstr "Départ X"

#: bCNC/ProbePage.py:1263
msgid "X step"
msgstr "Pas X"

#: bCNC/ControlPage.py:274
msgid "X work position (click to set)"
msgstr "Position de travail X (cliquer pour la régler)"

#: bCNC/ProbePage.py:1241
msgid "X:"
msgstr ""

#: bCNC/ControlPage.py:349
msgid "X=0"
msgstr ""

#: bCNC/ControlPage.py:399
msgid "XY=0"
msgstr ""

#: bCNC/ControlPage.py:415
msgid "XYZ=0"
msgstr ""

#: bCNC/plugins/simpleDrill.py:68 bCNC/ControlPage.py:936
msgid "Y"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:81
msgid "Y Initial"
msgstr ""

#: bCNC/plugins/scaling.py:40
msgid "Y Scale"
msgstr ""

#: bCNC/ProbePage.py:1315
msgid "Y bins"
msgstr "Y bins"

#: bCNC/plugins/function_plot.py:31
#, fuzzy
#| msgid "Internal Dimensions"
msgid "Y dimension"
msgstr "Dimensions Intérieures"

#: bCNC/ProbePage.py:1295
msgid "Y maximum"
msgstr "Y maximum"

#: bCNC/ProbePage.py:1287
msgid "Y minimum"
msgstr "Y minimum"

#: bCNC/plugins/function_plot.py:33
msgid "Y number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:290 bCNC/plugins/spiral.py:473
msgid "Y start"
msgstr "Départ Y"

#: bCNC/ProbePage.py:1303
msgid "Y step"
msgstr "Pas Y"

#: bCNC/ControlPage.py:290
msgid "Y work position (click to set)"
msgstr "Position de travail Y (cliquer pour la régler)"

#: bCNC/ProbePage.py:1281
msgid "Y:"
msgstr ""

#: bCNC/ControlPage.py:365
msgid "Y=0"
msgstr ""

#: bCNC/ToolsPage.py:1154
msgid "You cannot have both the number of tabs or distance equal to zero"
msgstr ""
"Vous ne pouvez avoir ni le nombre d'attaches ni la distance égal à zéro."

#: bCNC/ToolsPage.py:807
msgid "You should probably always use 'on path', unless you are threadmilling!"
msgstr ""

#: bCNC/Utils.py:591
msgid "Your email"
msgstr "Votre courriel"

#: bCNC/ControlPage.py:933 bCNC/ProbePage.py:462
msgid "Z"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:88
msgid "Z Feed Multiplier"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:82
msgid "Z Initial"
msgstr ""

#: bCNC/ProbePage.py:1328
msgid "Z Minimum depth to scan"
msgstr "Profondeur minimale en Z de scannage"

#: bCNC/plugins/scaling.py:41
msgid "Z Scale"
msgstr ""

#: bCNC/plugins/simpleDrill.py:72
msgid "Z feed for drilling"
msgstr ""

#: bCNC/ProbePage.py:1336
msgid "Z safe to move"
msgstr "Z de sécurité pour les déplacements"

#: bCNC/plugins/spiral.py:474
#, fuzzy
#| msgid "X start"
msgid "Z start"
msgstr "Départ X"

#: bCNC/ControlPage.py:306
msgid "Z work position (click to set)"
msgstr "Position de travail Z (cliquer pour la régler)"

#: bCNC/ProbePage.py:1322
msgid "Z:"
msgstr ""

#: bCNC/ControlPage.py:381
msgid "Z=0"
msgstr ""

#: bCNC/ProbePage.py:202
msgid "Zero"
msgstr "Zéro"

#: bCNC/plugins/zigzag.py:15
msgid "Zig-Zag"
msgstr "Zig-Zag"

#: bCNC/plugins/zigzag.py:148
msgid "Zig-Zag abort: depth must be minor or equal to zero"
msgstr "Zig-Zag annulé : La profondeur doit être inférieure ou égale à 0"

#: bCNC/plugins/zigzag.py:143
msgid "Zig-Zag abort: verify CornerRes >= 0"
msgstr "Zig-Zag annulé : vérifiez CornerRes >= 0"

#: bCNC/plugins/zigzag.py:135
msgid "Zig-Zag abort: verify LineLen > 0"
msgstr "Zig-Zag annulé : vérifiez LineLen > 0"

#: bCNC/plugins/zigzag.py:131
msgid "Zig-Zag abort: verify Nlines > 0"
msgstr "Zig-Zag annulé : vérifiez NLines > 0"

#: bCNC/plugins/zigzag.py:139
msgid "Zig-Zag abort: verify Step > 0"
msgstr "Zig-Zag annulé : vérifiez Pas > 0"

#: bCNC/CNCCanvas.py:2256
msgid "Zoom In [Ctrl-=]"
msgstr "Zoomer [Ctrl-=]"

#: bCNC/CNCCanvas.py:2263
msgid "Zoom Out [Ctrl--]"
msgstr "Dézoomer [Ctrl--]"

#: bCNC/ControlPage.py:1400
#, fuzzy
#| msgid "Control"
msgid "abcControl"
msgstr "Contrôle"

#: bCNC/ControlPage.py:625
msgid "abcDRO"
msgstr ""

#: bCNC/ControlPage.py:637
#, fuzzy
#| msgid "WPos:"
msgid "abcWPos:"
msgstr "WPos :"

#: bCNC/plugins/endmilloffset.py:479
msgid "acts like a tool corrector inside the profile"
msgstr ""

#: bCNC/plugins/simpleRotate.py:28
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "angle step (degrees)"
msgstr "Résolution (degrés)"

#: bCNC/plugins/dragknife.py:54
msgid "angle threshold"
msgstr ""

#: bCNC/plugins/arcfit.py:38
msgid "arc precision (mm)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:12
msgid "bCNC is currently sending a gcode program to Grbl"
msgstr "bCNC est en train d'envoyer un programme gcode à Grbl."

#: bCNC/plugins/simpleRectangle.py:97
msgid "clockwise"
msgstr ""

#: bCNC/plugins/dragknife.py:48
msgid "distance from dragknife rotation center to the tip of the blade"
msgstr ""

#: bCNC/plugins/dragknife.py:55
msgid "do not perform pivot action for angles smaller than this"
msgstr ""

#: bCNC/plugins/dragknife.py:47
msgid "dragknife offset"
msgstr ""

#: bCNC/CNCCanvas.py:678
#, fuzzy
#| msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgstr "dx={:g}  dy={:g}  dz={:g}  longueur={:g}  angle={:g}"

#: bCNC/plugins/dragknife.py:75
msgid "feedrate"
msgstr ""

#: bCNC/ProbePage.py:2047 bCNC/ProbePage.py:2092
msgid "get"
msgstr "obtenir"

#: bCNC/plugins/arcfit.py:39
msgid "how precisely must arc fit. set to 0 to disable arc fitting"
msgstr ""

#: bCNC/plugins/endmilloffset.py:463
msgid "indicates the direction (inside / outside) for making profiles"
msgstr ""

#: bCNC/plugins/dragknife.py:70
msgid "initial direction"
msgstr ""

#: bCNC/plugins/slicemesh.py:83
msgid "layer height (0 = only single zmin)"
msgstr ""

#: bCNC/plugins/arcfit.py:45
msgid "line precision (mm)"
msgstr ""

#: bCNC/plugins/slicemesh.py:88
msgid "maximum Z height"
msgstr ""

#: bCNC/plugins/arcfit.py:50
msgid "minimal number of segments to create arc"
msgstr ""

#: bCNC/plugins/slicemesh.py:86
msgid "minimum Z height"
msgstr ""

#: bCNC/plugins/simpleRotate.py:29 bCNC/plugins/simpleTranslate.py:28
msgid "nb repeat including original"
msgstr ""

#: bCNC/CNCCanvas.py:627
#, fuzzy
#| msgid "origin {:g} {:g} {:g}"
msgid "origin {:g} {:g} {:g}"
msgstr "origine {:g} {:g} {:g}"

#: bCNC/bmain.py:3097
msgid "python serial missing"
msgstr "python serial manquant"

#: bCNC/plugins/slicemesh.py:74
msgid "scale factor"
msgstr ""

#: bCNC/plugins/linearize.py:39
msgid "segment size"
msgstr ""

#: bCNC/ControlPage.py:1961 bCNC/ControlPage.py:2003 bCNC/ControlPage.py:2045
#: bCNC/ProbePage.py:360
msgid "set"
msgstr "régler"

#: bCNC/plugins/dragknife.py:80
msgid "simulate"
msgstr ""

#: bCNC/plugins/dragknife.py:89
msgid "simulation precision"
msgstr ""

#: bCNC/plugins/slicemesh.py:23
msgid "slicemesh"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:101
msgid "splice steps every 360 degrees"
msgstr ""

#: bCNC/plugins/linearize.py:48
msgid "subdiv lines"
msgstr ""

#: bCNC/plugins/dragknife.py:61
msgid "swivel height"
msgstr ""

#: bCNC/Sender.py:355
msgid "unknown command"
msgstr "commande inconnue."

#: bCNC/plugins/simpleRotate.py:26
#, fuzzy
#| msgid "Center"
msgid "x center"
msgstr "Centrer"

#: bCNC/plugins/simpleTranslate.py:26
#, fuzzy
#| msgid "Depth Increment"
msgid "x increment"
msgstr "Hauteur de passage"

#: bCNC/plugins/simpleLine.py:52 bCNC/plugins/simpleRectangle.py:93
msgid "xEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:51 bCNC/plugins/simpleRectangle.py:92
#, fuzzy
#| msgid "Start"
msgid "xStart"
msgstr "Début"

#: bCNC/plugins/simpleRotate.py:27
#, fuzzy
#| msgid "Center"
msgid "y center"
msgstr "Centrer"

#: bCNC/plugins/simpleTranslate.py:27
#, fuzzy
#| msgid "Depth Increment"
msgid "y increment"
msgstr "Hauteur de passage"

#: bCNC/plugins/simpleLine.py:54 bCNC/plugins/simpleRectangle.py:95
msgid "yEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:53 bCNC/plugins/simpleRectangle.py:94
#, fuzzy
#| msgid "Start"
msgid "yStart"
msgstr "Début"

#: bCNC/plugins/slicemesh.py:77
msgid "z offset"
msgstr ""

#~ msgid ""
#~ "ERROR: Please install the python pyserial module\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tor yum install python-serial\n"
#~ "\tor dnf install python-pyserial"
#~ msgstr ""
#~ "ERREUR : Veuillez installer le module python pyserial\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tor yum install python-serial\n"
#~ "\tor dnf install python-pyserial"

#~ msgid "Color configuration"
#~ msgstr "Configuration des Couleurs"

#~ msgid "Font configuration"
#~ msgstr "Configuration de la police"

#~ msgid "Tools"
#~ msgstr "Outils"

#~ msgid "Set current location as Z-zero for leveling"
#~ msgstr "Déclarer la position actuelle comme le Z-zero pour la mise à niveau"

#~ msgid "Inkscape"
#~ msgstr "Inkscape"

#~ msgid "An invalid tool number sent to the parser"
#~ msgstr "Numéro d'outil invalide envoyé au parser"

#~ msgid "$6 Invert probe pin"
#~ msgstr "$6 Inversion de la broche de la sonde"

#~ msgid ""
#~ "G-code is composed of G-code 'words', which consists of a letter followed "
#~ "by a number value. This error occurs when the letter prefix of a G-code "
#~ "word is missing in the G-code block (aka line)."
#~ msgstr ""
#~ "G-code est composé de 'mots' G-code commençant par une lettre suivie d'un "
#~ "nombre. Cette erreur apparaît quand cette lettre en préfixe est absente "
#~ "dans  le bloc de G-code (c'est-à-dire dans la ligne)."

#~ msgid ""
#~ "The number value suffix of a G-code word is missing in the G-code block, "
#~ "or when configuring a $Nx=line or $x=val Grbl setting and the x is not a "
#~ "number value."
#~ msgstr ""
#~ "Il manque une valeur dans le bloc de G-code, ou en configurant un "
#~ "paramètre dans Grbl comme $Nx=line ou $x=val x n'est pas une valeur "
#~ "numérique."

#~ msgid ""
#~ "The value of a $x=val Grbl setting, F feed rate, N line number, P word, T "
#~ "tool number, or S spindle speed is negative."
#~ msgstr ""
#~ "La valeur $x=val du paramètre Grbl, le taux d'avance F, le numéro de "
#~ "ligne N,  le mot P, le numéro d'outil T ou la vitesse S de la fraiseuse "
#~ "est négative."

#~ msgid "Homing is disabled when issuing a $H command."
#~ msgstr ""
#~ "Le calage à l'origine (homing) est désactivé lors de l'envoi d'une "
#~ "commande $H."

#~ msgid ""
#~ "Step pulse time length cannot be less than 3 microseconds (for technical "
#~ "reasons)."
#~ msgstr ""
#~ "La durée de l'impulsion du pas ne peut être inférieur à 3 microsecondes "
#~ "(pour des raisons techniques)."

#~ msgid ""
#~ "If Grbl can't read data contained in the EEPROM, this error is returned. "
#~ "Grbl will also clear and restore the effected data back to defaults."
#~ msgstr ""
#~ "Erreur retournée si Grbl ne peut lire les données contenues dans "
#~ "l'EEPROM. Grbl effacera et restaurera les données à leurs valeurs par "
#~ "défaut."

#~ msgid ""
#~ "Certain Grbl $ commands are blocked depending Grbl's current state, or "
#~ "what its doing. In general, Grbl blocks any command that fetches from or "
#~ "writes to the EEPROM since the AVR microcontroller will shutdown all of "
#~ "the interrupts for a few clock cycles when this happens. There is no work "
#~ "around, other than blocking it. This ensures both the serial and step "
#~ "generator interrupts are working smoothly throughout operation."
#~ msgstr ""
#~ "Certaines commandes Grbl $ sont bloqués en fonction de l'état actuel de "
#~ "Grbl ou de ce qu'il est en train de faire. En général, Grbl bloque toute "
#~ "commande qui provient ou écrit sur l'EEPROM puisque le microcontrôleur "
#~ "AVR éteindra toutes les interruptions pendant quelques cycles d'horloge "
#~ "quand cela arrive. Il n'y a pas d'autre issue autre que le blocage. Ceci "
#~ "garantit à la fois que les interruptions du port série et du générateur "
#~ "de pas fonctionnent sans heurt tout au long de l'opération."

#~ msgid ""
#~ "Grbl enters an ALARM state when Grbl doesn't know where it is and will "
#~ "then block all G-code commands from being executed. This error occurs if "
#~ "G-code commands are sent while in the alarm state. Grbl has two alarm "
#~ "scenarios: When homing is enabled, Grbl automatically goes into an alarm "
#~ "state to remind the user to home before doing anything; When something "
#~ "has went critically wrong, usually when Grbl can't guarantee positioning. "
#~ "This typically happens when something causes Grbl to force an immediate "
#~ "stop while its moving from a hard limit being triggered or a user "
#~ "commands an ill-timed reset."
#~ msgstr ""
#~ "Grbl renvoie un état d'ALERTE lorsque Grbl ne sait pas où il est et qu'il "
#~ "bloquera alors l'exécution de toutes les commandes G-Code. Cette erreur "
#~ "survient si les commandes G-Code sont envoyés pendant un état d'alerte. "
#~ "Grbl a 2 types d'alertes : lorsque le homing a été fait, Grbl se met "
#~ "automatiquement en état d'alerte pour rappeler à l'utilisateur de revenir "
#~ "au point de départ (home) avant de faire quoi que ce soit ; lorsqu'un "
#~ "événement critique est apparu, en général quand Grbl ne peut garantir le "
#~ "positionnement. Ceci est typique quand quelque chose impose un arrêt "
#~ "immédiat (dépassement des limites physiques ou des commandes utilisateurs "
#~ "et un reset par inadvertance)."

#~ msgid ""
#~ "Soft limits cannot be enabled if homing is not enabled, because Grbl has "
#~ "no idea where it is when you startup your machine unless you perform a "
#~ "homing cycle."
#~ msgstr ""
#~ "Les limites logicielles ne peuvent être établies si le homing n'a pas été "
#~ "réalisé, car Grbl n'a aucune idée où il est quand vous démarrez votre "
#~ "machine à moins que vous réalisiez un homing."

#~ msgid ""
#~ "Grbl has to do everything it does within 2KB of RAM. Not much at all. So, "
#~ "we had to make some decisions on what's important. Grbl limits the number "
#~ "of characters in each line to less than 80 characters (70 in v0.8, 50 in "
#~ "v0.7 or earlier), excluding spaces or comments. The G-code standard "
#~ "mandates 256 characters, but Grbl simply doesn't have the RAM to spare. "
#~ "However, we don't think there will be any problems with this with all of "
#~ "the expected G-code commands sent to Grbl. This error almost always "
#~ "occurs when a user or CAM-generated G-code program sends position values "
#~ "that are in double precision (i.e. -2.003928578394852), which is not "
#~ "realistic or physically possible. Users and GUIs need to send Grbl "
#~ "floating point values in single precision (i.e. -2.003929) to avoid this "
#~ "error."
#~ msgstr ""
#~ "Grbl doit tout exécuter dans 2KB de RAM. Pas plus. Aussi, nous devons "
#~ "faire des choix sur l'essentiel. Grbl limite le nombre de caractères par "
#~ "ligne à 80 (70 dans la v0.8, 50 dans la v0.7 ou antérieure), sans compter "
#~ "les espaces ou les commentaires. Le standard G-Code fixe à 256 "
#~ "caractères, mais Grbl n'a tout simplement pas la RAM pour cela. "
#~ "Cependant, nous ne pensons pas qu'il puisse y avoir de problèmes avec "
#~ "cela avec toutes le commandes G-Code prévues envoyées à Grbl. Cette "
#~ "erreur survient presque toujours lorsque le G-code généré par un logiciel "
#~ "CAM envoie les valeurs des positions au format double (i.e. "
#~ "-2.003928578394852), ce qui n'est ni réaliste ni physiquement possible. "
#~ "Les utilisateurs et interfaces doivent envoyer les valeurs décimales avec "
#~ "une précision simple (i.e. - 2.003929) pour éviter cette erreur."

#~ msgid ""
#~ "The G-code parser has detected two G-code commands that belong to the "
#~ "same modal group in the block/line. Modal groups are sets of G-code "
#~ "commands that mutually exclusive. For example, you can't issue both a G0 "
#~ "rapids and G2 arc in the same line, since they both need to use the XYZ "
#~ "target position values in the line. LinuxCNC.org has some great "
#~ "documentation on modal groups."
#~ msgstr ""
#~ "Le parser de G-Code a détecté 2 commandes qui appartiennent au même "
#~ "groupe modal dans le bloc/ligne. Les groupes modaux sont des commandes "
#~ "qui s'excluent mutuellement. Par exemple, vous ne pouvez pas rélaiser à "
#~ "la fois un rapide G0 et un arc G2 sur la même ligne, puisqu'ils "
#~ "nécessitent tous deux les valeurs de la position cible. LinuxCNC.org "
#~ "fournit une excellente documentation sur les groupes modaux."

#~ msgid ""
#~ "The G-code parser doesn't recognize or support one of the G-code commands "
#~ "in the line. Check your G-code program for any unsupported commands and "
#~ "either remove them or update them to be compatible with Grbl."
#~ msgstr ""
#~ "Le parser G-Code ne reconnaît ou ne supporte pas une des commandes G-Code "
#~ "dans la ligne. Vérifiez votre programme G-Code en soit les supprimant ou "
#~ "en les actualisant afin qu'elles soient compatibles avec Grbl."

#~ msgid ""
#~ "There is no feed rate programmed, and a G-code command that requires one "
#~ "is in the block/line. The G-code standard mandates F feed rates to be "
#~ "undefined upon a reset or when switching from inverse time mode to units "
#~ "mode. Older Grbl versions had a default feed rate setting, which was "
#~ "illegal and was removed in Grbl v0.9."
#~ msgstr ""
#~ "Pas de taux de vitesse programmée, et une commande G-Code en demande un "
#~ "dans le bloc ou la ligne. Le standard G-Code définit le taux de vitesse F "
#~ "comme devant être indéfini après un reset ou lorsque l'on passe du mode "
#~ "vitesse inverse du temps (vitesse/distance) en mode unités par minute ou "
#~ "par tour. Les anciennes versions de Grbl avait un taux de vitesse par "
#~ "défaut, ce qui était illégal et a été supprimé dans Grbl v0.9."

#~ msgid ""
#~ "A G or M command value in the block is not an integer. For example, G4 "
#~ "can't be G4.13. Some G-code commands are floating point (G92.1), but "
#~ "these are ignored."
#~ msgstr ""
#~ "Une valeur d'une commande G ou M dans le bloc n'est pas un entier. Par "
#~ "exemple, G4 ne peut pas être G4.13. Certaines commandes ont une virgule "
#~ "flottante (G92.1) mais elles sont ignorées."

#~ msgid ""
#~ "The G-code protocol mandates N line numbers to be within the range of "
#~ "1-99,999. We think that's a bit silly and arbitrary. So, we increased the "
#~ "max number to 9,999,999. This error occurs when you send a number more "
#~ "than this."
#~ msgstr ""
#~ "Le protocole G-Code fixe le nombre de lignes N entre 1 et 99 999. Nous "
#~ "pensons que c'est un peu réducteur et arbitraire. Aussi, nous avons "
#~ "augmenter le nombre jusqu'à 9 999 999. Cette erreur survient lorsque vous "
#~ "envoyez un nombre plus grand que celui-ci."

#~ msgid ""
#~ "Grbl supports six work coordinate systems G54-G59. This error happens "
#~ "when trying to use or configure an unsupported work coordinate system, "
#~ "such as G59.1, G59.2, and G59.3."
#~ msgstr ""
#~ "Grbl supporte 6 systèmes de coordonnées de travail G54-G59. Cette erreur "
#~ "survient lorsqu'on essaie d'utiliser ou de configurer un système de "
#~ "coordonnées non supporté, tel que G59.1, G59.2, et G59.3."

#~ msgid ""
#~ "The motion command has an invalid target. G2, G3, and G38.2 generates "
#~ "this error. For both probing and arcs traced with the radius definition, "
#~ "the current position cannot be the same as the target. This also errors "
#~ "when the arc is mathematically impossible to trace, where the current "
#~ "position, the target position, and the radius of the arc doesn't define a "
#~ "valid arc."
#~ msgstr ""
#~ "La commande de mouvement a une cible invalide. G2, G3 et G38.2 génèrent "
#~ "cette erreur. Pour les deux, en sondant ou en traçant des arcs avec la "
#~ "définition du rayon, la position actuelle ne peut pas être la même que la "
#~ "cible. Ceci arrive aussi lorsqu'il est mathématiquement impossible de "
#~ "tracer l'arc, à cause de la position actuelle, de la position cible ou du "
#~ "rayon de l'arc."

#~ msgid ""
#~ "Hard and/or soft limits must be enabled for this error to occur. With "
#~ "hard limits, Grbl will enter alarm mode when a hard limit switch has been "
#~ "triggered and force kills all motion. Machine position will be lost and "
#~ "require re-homing. With soft limits, the alarm occurs when Grbl detects a "
#~ "programmed motion trying to move outside of the machine space, set by "
#~ "homing and the max travel settings. However, upon the alarm, a soft limit "
#~ "violation will instruct a feed hold and wait until the machine has "
#~ "stopped before issuing the alarm. Soft limits do not lose machine "
#~ "position because of this."
#~ msgstr ""
#~ "Des limites logicielles et/ou physiques doivent être activées pour que "
#~ "cette erreur arrive. Avec des limites physiques, Grbl entrera en mode "
#~ "alerte lorsqu'un switch sera déclenché et tuera tout mouvement. La "
#~ "position de la machine sera perdue et il faudra faire un homing. Avec des "
#~ "limites logicielles, l'alarme se déclenche quand Grbl détecte un "
#~ "mouvement prévue qui fera sortir de l'espace de travail fixé par le "
#~ "homing et les réglages des limites maximales. Cependant, au-dessus de "
#~ "cette alarme, une violation logicielle des limites entraînera une pause "
#~ "des mouvements et attendra jusqu'à ce que la machine s'arrête avant de "
#~ "déclencher l'alarme. Les limites logicielles ne font pas perdre la "
#~ "position de la machine à cause de ça."

#~ msgid ""
#~ "This alarm occurs when a user issues a soft-reset while the machine is in "
#~ "a cycle and moving. The soft-reset will kill all current motion, and, "
#~ "much like the hard limit alarm, the uncontrolled stop causes Grbl to lose "
#~ "position."
#~ msgstr ""
#~ "Cette alarme arrive lorsque l'utilisateur exécute un soft-reset alors que "
#~ "la machine réalise un cycle et se déplace. Le soft-reset tue tout "
#~ "déplacement en cours, et, comme une alarme matérielle de dépassement de "
#~ "limites, cet arrêt fait que Grbl perd le positionnement."

#~ msgid ""
#~ "The G38.2 straight probe command requires an alarm or error when the "
#~ "probe fails to trigger within the programmed probe distance. Grbl enters "
#~ "the alarm state to indicate to the user the probe has failed, but will "
#~ "not lose machine position, since the probe motion comes to a controlled "
#~ "stop before the error."
#~ msgstr ""
#~ "La commande G38.2 pour la sonde conduit à un avertissement ou une erreur "
#~ "lorsque la sonde ne s'active pas dans la zone prévue. Grbl renvoie un "
#~ "état d'alarme pour signaler à l'utilisateur que la sonde n'a pas "
#~ "fonctionné, mais elle ne perd pas la position de la machine, puisque le "
#~ "déplacement de la sonde mène à un arrêt contrôlé avant l'erreur."

#~ msgid "Machine"
#~ msgstr "Machine"

#~ msgid "Change color for block of g-code"
#~ msgstr "Changer la couleur pour le bloc de g-code"

#~ msgid "T-L"
#~ msgstr "H-G"

#~ msgid "Move origin of g-code to Top-Left corner"
#~ msgstr "Déplace l'origine du G-Code dans le coin Haut-Gauche"

#~ msgid "L"
#~ msgstr "G"

#~ msgid "Move origin of g-code to Left side"
#~ msgstr "Déplace l'origine du G-Code dans le côté Gauche"

#~ msgid "B-L"
#~ msgstr "B-G"

#~ msgid "Move origin of g-code to Bottom-Left corner"
#~ msgstr "Déplace l'origine du G-Code dans le coin Bas-Gauche"

#~ msgid "Move origin of g-code to Top side"
#~ msgstr "Déplace l'origine du G-Code dans le côté Haut"

#~ msgid "Move origin of g-code to center"
#~ msgstr "Déplace l'origine du G-Code au centre"

#~ msgid "Move origin of g-code to Bottom side"
#~ msgstr "Déplace l'origine du G-Code dans le côté Bas"

#~ msgid "T-R"
#~ msgstr "H-D"

#~ msgid "Move origin of g-code to Top-Right corner"
#~ msgstr "Déplace l'origine du G-Code dans le coin Haut-Droit"

#~ msgid "R"
#~ msgstr "D"

#~ msgid "Move origin of g-code to Right side"
#~ msgstr "Déplace l'origine du G-Code dans le côté Droit"

#~ msgid "B-R"
#~ msgstr "B-D"

#~ msgid "Move origin of g-code to Bottom-Right corner"
#~ msgstr "Déplace l'origine du G-Code dans le coin Bas-Droit"

#~ msgid ""
#~ "Feed\n"
#~ "Override:"
#~ msgstr ""
#~ "Vitesse\n"
#~ "Adaptation :"

#~ msgid "Set Feed Override"
#~ msgstr "Régler l'adaptation de la vitesse"

#~ msgid "Set workspace {} to X{} Y{} Z{}"
#~ msgstr "Réglez l'espace de travail {} à X{} Y{} Z{}."

#~ msgid "Preparing to run ..."
#~ msgstr "Préparation avant le travail ..."

#~ msgid "Square"
#~ msgstr "Carré"

#~ msgid "Probe X/Y axis by using a set square probe"
#~ msgstr "Sondage sur les axes X/Y en utilisant une sonde carrée"

#~ msgid "Perform a center probe cycle"
#~ msgstr "Lancer un cycle de sondage circulaire"

#~ msgid ""
#~ "ERROR: Please install the python pyserial module\n"
#~ "Windows: C:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Linux: sudo apt-get or yum install python-serial"
#~ msgstr ""
#~ "ERREUR : Veuillez installer le module python pyserial\n"
#~ "Windows: C:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Linux: sudo apt-get or yum install python-serial"

#~ msgid "Draw a square tab"
#~ msgstr "Dessiner une languette carré"

#~ msgid "Place origin with the mouse on canvas [O]"
#~ msgstr "Placer l'origine avec la souris sur le canevas [O]"

#~ msgid "Macros"
#~ msgstr "Macros"

#~ msgid "Probe is not zeroed"
#~ msgstr "Sonde non mise à Zéro"

#~ msgid "Please ZERO any location of the probe before starting a run"
#~ msgstr ""
#~ "Veuillez mettre à ZÉRO les positions de la sonde avant de lancer un "
#~ "travail"

#~ msgid "move {:g} {:g} {:g}"
#~ msgstr "déplacer {:g}, {:g}, {:g}"
