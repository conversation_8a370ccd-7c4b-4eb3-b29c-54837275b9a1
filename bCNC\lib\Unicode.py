# Copyright and User License
# ~~~~~~~~~~~~~~~~~~~~~~~~~~
# Copyright Vasilis.V<PERSON><PERSON><PERSON><PERSON>@cern.ch for the
# European Organization for Nuclear Research
#
# Please consult the documentation for the license
#
# DISCLAIMER
# ~~~~~~~~~~
# THIS SOFTWARE IS PROVIDED BY THE AUTHOR "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT
# NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, OF
# SATISFACTORY QUALITY, AND FITNESS FOR A PARTICULAR PURPOSE
# OR USE ARE DISCLAIMED. THE COPYRIGHT HOLDERS AND THE
# AUTHORS MAKE NO REPRESENTATION THAT THE SOFTWARE AND
# MODIFICATIONS THEREOF, WILL NOT INFRINGE ANY PATENT,
# COPYRIGHT, TRADE SECRET OR OTHER PROPRIETARY RIGHT.
#
# LIMITATION OF LIABILITY
# ~~~~~~~~~~~~~~~~~~~~~~~
# THE COPYRIGHT HOLDERS AND THE AUTHORS SHALL HAVE NO
# LIABILITY FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL,
# CONSEQUENTIAL, EXEMPLARY, OR PUNITIVE DAMAGES OF ANY
# CHARACTER INCLUDING, WITHOUT LIMITATION, PROCUREMENT OF
# SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA OR PROFITS,
# OR BUSINESS INTERRUPTION, HOWEVER CAUSED AND ON ANY THEORY
# OF CONTRACT, WARRANTY, TORT
# LIABILITY OR OTHERWISE, ARISING IN ANY WAY OUT OF THE USE OF
# THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
# DAMAGES.
#
# Author:    <EMAIL>
# Date:      04-Sep-2014

# Greek characters http://en.wikipedia.org/wiki/List_of_Unicode_characters
GREEK_NUMERAL_SIGN = "\u0374"
GREEK_LOWER_NUMERAL_SIGN = "\u0375"
GREEK_YPOGEGRAMMENI = "\u037A"
GREEK_SMALL_REVERSED_LUNATE_SIGMA_SYMBOL = "\u037B"
GREEK_SMALL_DOTTED_LUNATE_SIGMA_SYMBOL = "\u037C"
GREEK_SMALL_REVERSED_DOTTED_LUNATE_SIGMA_SYMBOL = "\u037D"
GREEK_QUESTION_MARK = "\u037E"
GREEK_ACUTE_ACCENT = "\u0384"
GREEK_DIAERESIS_WITH_ACUTE_ACCENT = "\u0385"
GREEK_CAPITAL_A_WITH_ACUTE_ACCENT = "\u0386"
GREEK_ANO_TELEIA = "\u0387"
GREEK_CAPITAL_EPSILON_WITH_ACUTE_ACCENT = "\u0388"
GREEK_CAPITAL_ETA_WITH_ACUTE_ACCENT = "\u0389"
GREEK_CAPITAL_IOTA_WITH_ACUTE_ACCENT = "\u038A"
GREEK_CAPITAL_OMICRON_WITH_ACUTE_ACCENT = "\u038C"
GREEK_CAPITAL_UPSILON_WITH_ACUTE_ACCENT = "\u038E"
GREEK_CAPITAL_OMEGA_WITH_ACUTE_ACCENT = "\u038F"
GREEK_SMALL_IOTA_WITH_DIAERESIS_AND_ACUTE_ACCENT = "\u0390"
GREEK_CAPITAL_ALPHA = "\u0391"
GREEK_CAPITAL_BETA = "\u0392"
GREEK_CAPITAL_GAMMA = "\u0393"
GREEK_CAPITAL_DELTA = "\u0394"
GREEK_CAPITAL_EPSILON = "\u0395"
GREEK_CAPITAL_ZETA = "\u0396"
GREEK_CAPITAL_ETA = "\u0397"
GREEK_CAPITAL_THETA = "\u0398"
GREEK_CAPITAL_IOTA = "\u0399"
GREEK_CAPITAL_KAPPA = "\u039A"
GREEK_CAPITAL_LAMBDA = "\u039B"
GREEK_CAPITAL_MU = "\u039C"
GREEK_CAPITAL_NU = "\u039D"
GREEK_CAPITAL_XI = "\u039E"
GREEK_CAPITAL_OMICRON = "\u039F"
GREEK_CAPITAL_PI = "\u03A0"
GREEK_CAPITAL_RHO = "\u03A1"
GREEK_CAPITAL_SIGMA = "\u03A3"
GREEK_CAPITAL_TAU = "\u03A4"
GREEK_CAPITAL_UPSILON = "\u03A5"
GREEK_CAPITAL_PHI = "\u03A6"
GREEK_CAPITAL_CHI = "\u03A7"
GREEK_CAPITAL_PSI = "\u03A8"
GREEK_CAPITAL_OMEGA = "\u03A9"
GREEK_CAPITAL_IOTA_WITH_DIAERESIS = "\u03AA"
GREEK_CAPITAL_UPSILON_WITH_DIAERESIS = "\u03AB"
GREEK_SMALL_ALPHA_WITH_ACUTE_ACCENT = "\u03AC"
GREEK_SMALL_EPSILON_WITH_ACUTE_ACCENT = "\u03AD"
GREEK_SMALL_ETA_WITH_ACUTE_ACCENT = "\u03AE"
GREEK_SMALL_IOTA_WITH_ACUTE_ACCENT = "\u03AF"
GREEK_SMALL_UPSILON_WITH_DIAERESIS_AND_ACUTE_ACCENT = "\u03B0"
GREEK_SMALL_ALPHA = "\u03B1"
GREEK_SMALL_BETA = "\u03B2"
GREEK_SMALL_GAMMA = "\u03B3"
GREEK_SMALL_DELTA = "\u03B4"
GREEK_SMALL_EPSILON = "\u03B5"
GREEK_SMALL_ZETA = "\u03B6"
GREEK_SMALL_ETA = "\u03B7"
GREEK_SMALL_THETA = "\u03B8"
GREEK_SMALL_IOTA = "\u03B9"
GREEK_SMALL_KAPPA = "\u03BA"
GREEK_SMALL_LAMBDA = "\u03BB"
GREEK_SMALL_MU = "\u03BC"
GREEK_SMALL_NU = "\u03BD"
GREEK_SMALL_XI = "\u03BE"
GREEK_SMALL_OMICRON = "\u03BF"
GREEK_SMALL_PI = "\u03C0"
GREEK_SMALL_RHO = "\u03C1"
GREEK_SMALL_FINAL_SIGMA = "\u03C2"
GREEK_SMALL_SIGMA = "\u03C3"
GREEK_SMALL_TAU = "\u03C4"
GREEK_SMALL_UPSILON = "\u03C5"
GREEK_SMALL_PHI = "\u03C6"
GREEK_SMALL_CHI = "\u03C7"
GREEK_SMALL_PSI = "\u03C8"
GREEK_SMALL_OMEGA = "\u03C9"
GREEK_SMALL_IOTA_WITH_DIAERESIS = "\u03CA"
GREEK_SMALL_UPSILON_WITH_DIAERESIS = "\u03CB"
GREEK_SMALL_OMICRON_WITH_ACUTE_ACCENT = "\u03CC"
GREEK_SMALL_UPSILON_WITH_ACUTE_ACCENT = "\u03CD"
GREEK_SMALL_OMEGA_WITH_ACUTE_ACCENT = "\u03CE"
GREEK_BETA_SYMBOL = "\u03D0"
GREEK_THETA_SYMBOL = "\u03D1"
GREEK_UPSILON_WITH_HOOK_SYMBOL = "\u03D2"
GREEK_UPSILON_WITH_ACUTE_AND_HOOK_SYMBOL = "\u03D3"
GREEK_UPSILON_WITH_DIAERESIS_AND_HOOK_SYMBOL = "\u03D4"
GREEK_PHI_SYMBOL = "\u03D5"
GREEK_PI_SYMBOL = "\u03D6"
GREEK_KAI_SYMBOL = "\u03D7"
GREEK_QOPPA = "\u03D8"
GREEK_SMALL_QOPPA = "\u03D9"
GREEK_STIGMA_LETTER = "\u03DA"
GREEK_SMALL_STIGMA = "\u03DB"
GREEK_DIGAMMA = "\u03DC"
GREEK_SMALL_DIGAMMA = "\u03DD"
GREEK_KOPPA = "\u03DE"
GREEK_SMALL_KOPPA = "\u03DF"
GREEK_SAMPI = "\u03E0"
GREEK_SMALL_SAMPI = "\u03E1"
COPTIC_CAPITAL_SHEI = "\u03E2"
COPTIC_SMALL_SHEI = "\u03E3"
COPTIC_CAPITAL_FEI = "\u03E4"
COPTIC_SMALL_FEI = "\u03E5"
COPTIC_CAPITAL_KHEI = "\u03E6"
COPTIC_SMALL_KHEI = "\u03E7"
COPTIC_CAPITAL_HORI = "\u03E8"
COPTIC_SMALL_HORI = "\u03E9"
COPTIC_CAPITAL_GANGIA = "\u03EA"
COPTIC_SMALL_GANGIA = "\u03EB"
COPTIC_CAPITAL_SHIMA = "\u03EC"
COPTIC_SMALL_SHIMA = "\u03ED"
COPTIC_CAPITAL_DEI = "\u03EE"
COPTIC_SMALL_DEI = "\u03EF"
GREEK_KAPPA_SYMBOL = "\u03F0"
GREEK_RHO_SYMBOL = "\u03F1"
GREEK_LUNATE_SIGMA_SYMBOL = "\u03F2"
GREEK_YOT = "\u03F3"
GREEK_CAPITAL_THETA_SYMBOL = "\u03F4"
GREEK_LUNATE_EPSILON_SYMBOL = "\u03F5"
GREEK_REVERSED_LUNATE_EPSILON_SYMBOL = "\u03F6"
GREEK_CAPITAL_SHO = "\u03F7"
GREEK_SMALL_SHO = "\u03F8"
GREEK_CAPITAL_LUNATE_SIGMA_SYMBOL = "\u03F9"
GREEK_CAPITAL_SAN = "\u03FA"
GREEK_SMALL_SAN = "\u03FB"
GREEK_RHO_WITH_STROKE_SYMBOL = "\u03FC"
GREEK_CAPITAL_REVERSED_LUNATE_SIGMA_SYMBOL = "\u03FD"
GREEK_CAPITAL_DOTTED_LUNATE_SIGMA_SYMBOL = "\u03FE"
GREEK_CAPITAL_REVERSED_DOTTED_LUNATE_SIGMA_SYMBOL = "\u03FF"

# Geometric Shapes http://en.wikipedia.org/wiki/Geometric_Shapes
BLACK_SQUARE = "\u25A0"
WHITE_SQUARE = "\u25A1"
WHITE_SQUARE_WITH_ROUNDED_CORNERS = "\u25A2"
WHITE_SQUARE_CONTAINING_BLACK_SMALL_SQUARE = "\u25A3"
SQUARE_WITH_HORIZONTAL_FILL = "\u25A4"
SQUARE_WITH_VERTICAL_FILL = "\u25A5"
SQUARE_WITH_ORTHOGONAL_CROSSHATCH_FILL = "\u25A6"
SQUARE_WITH_UPPER_LEFT_TO_LOWER_RIGHT_FILL = "\u25A7"
SQUARE_WITH_UPPER_RIGHT_TO_LOWER_LEFT_FILL = "\u25A8"
SQUARE_WITH_DIAGONAL_CROSSHATCH_FILL = "\u25A9"
BLACK_SMALL_SQUARE = "\u25AA"
WHITE_SMALL_SQUARE = "\u25AB"
BLACK_RECTANGLE = "\u25AC"
WHITE_RECTANGLE = "\u25AD"
BLACK_VERTICAL_RECTANGLE = "\u25AE"
WHITE_VERTICAL_RECTANGLE = "\u25AF"
BLACK_PARALLELOGRAM = "\u25B0"
WHITE_PARALLELOGRAM = "\u25B1"
BLACK_UP_POINTING_TRIANGLE = "\u25B2"
WHITE_UP_POINTING_TRIANGLE = "\u25B3"
BLACK_UP_POINTING_SMALL_TRIANGLE = "\u25B4"
WHITE_UP_POINTING_SMALL_TRIANGLE = "\u25B5"
BLACK_RIGHT_POINTING_TRIANGLE = "\u25B6"
WHITE_RIGHT_POINTING_TRIANGLE = "\u25B7"
BLACK_RIGHT_POINTING_SMALL_TRIANGLE = "\u25B8"
WHITE_RIGHT_POINTING_SMALL_TRIANGLE = "\u25B9"
BLACK_RIGHT_POINTING_POINTER = "\u25BA"
WHITE_RIGHT_POINTING_POINTER = "\u25BB"
BLACK_DOWN_POINTING_TRIANGLE = "\u25BC"
WHITE_DOWN_POINTING_TRIANGLE = "\u25BD"
BLACK_DOWN_POINTING_SMALL_TRIANGLE = "\u25BE"
WHITE_DOWN_POINTING_SMALL_TRIANGLE = "\u25BF"
BLACK_LEFT_POINTING_TRIANGLE = "\u25C0"
WHITE_LEFT_POINTING_TRIANGLE = "\u25C1"
BLACK_LEFT_POINTING_SMALL_TRIANGLE = "\u25C2"
WHITE_LEFT_POINTING_SMALL_TRIANGLE = "\u25C3"
BLACK_LEFT_POINTING_POINTER = "\u25C4"
WHITE_LEFT_POINTING_POINTER = "\u25C5"
BLACK_DIAMOND = "\u25C6"
WHITE_DIAMOND = "\u25C7"
WHITE_DIAMOND_CONTAINING_BLACK_SMALL_DIAMOND = "\u25C8"
FISHEYE = "\u25C9"
LOZENGE = "\u25CA"
WHITE_CIRCLE = "\u25CB"
DOTTED_CIRCLE = "\u25CC"
CIRCLE_WITH_VERTICAL_FILL = "\u25CD"
BULLSEYE = "\u25CE"
BLACK_CIRCLE = "\u25CF"
CIRCLE_WITH_LEFT_HALF_BLACK = "\u25D0"
CIRCLE_WITH_RIGHT_HALF_BLACK = "\u25D1"
CIRCLE_WITH_LOWER_HALF_BLACK = "\u25D2"
CIRCLE_WITH_UPPER_HALF_BLACK = "\u25D3"
CIRCLE_WITH_UPPER_RIGHT_QUADRANT_BLACK = "\u25D4"
CIRCLE_WITH_ALL_BUT_UPPER_LEFT_QUADRANT_BLACK = "\u25D5"
LEFT_HALF_BLACK_CIRCLE = "\u25D6"
RIGHT_HALF_BLACK_CIRCLE = "\u25D7"
INVERSE_BULLET = "\u25D8"
INVERSE_WHITE_CIRCLE = "\u25D9"
UPPER_HALF_INVERSE_WHITE_CIRCLE = "\u25DA"
LOWER_HALF_INVERSE_WHITE_CIRCLE = "\u25DB"
UPPER_LEFT_QUADRANT_CIRCULAR_ARC = "\u25DC"
UPPER_RIGHT_QUADRANT_CIRCULAR_ARC = "\u25DD"
LOWER_RIGHT_QUADRANT_CIRCULAR_ARC = "\u25DE"
LOWER_LEFT_QUADRANT_CIRCULAR_ARC = "\u25DF"
UPPER_HALF_CIRCLE = "\u25E0"
LOWER_HALF_CIRCLE = "\u25E1"
BLACK_LOWER_RIGHT_TRIANGLE = "\u25E2"
BLACK_LOWER_LEFT_TRIANGLE = "\u25E3"
BLACK_UPPER_LEFT_TRIANGLE = "\u25E4"
BLACK_UPPER_RIGHT_TRIANGLE = "\u25E5"
WHITE_BULLET = "\u25E6"
SQUARE_WITH_LEFT_HALF_BLACK = "\u25E7"
SQUARE_WITH_RIGHT_HALF_BLACK = "\u25E8"
SQUARE_WITH_UPPER_LEFT_DIAGONAL_HALF_BLACK = "\u25E9"
SQUARE_WITH_LOWER_RIGHT_DIAGONAL_HALF_BLACK = "\u25EA"
WHITE_SQUARE_WITH_VERTICAL_BISECTING_LINE = "\u25EB"
WHITE_UP_POINTING_TRIANGLE_WITH_DOT = "\u25EC"
UP_POINTING_TRIANGLE_WITH_LEFT_HALF_BLACK = "\u25ED"
UP_POINTING_TRIANGLE_WITH_RIGHT_HALF_BLACK = "\u25EE"
LARGE_CIRCLE = "\u25EF"
WHITE_SQUARE_WITH_UPPER_LEFT_QUADRANT = "\u25F0"
WHITE_SQUARE_WITH_LOWER_LEFT_QUADRANT = "\u25F1"
WHITE_SQUARE_WITH_LOWER_RIGHT_QUADRANT = "\u25F2"
WHITE_SQUARE_WITH_UPPER_RIGHT_QUADRANT = "\u25F3"
WHITE_CIRCLE_WITH_UPPER_LEFT_QUADRANT = "\u25F4"
WHITE_CIRCLE_WITH_LOWER_LEFT_QUADRANT = "\u25F5"
WHITE_CIRCLE_WITH_LOWER_RIGHT_QUADRANT = "\u25F6"
WHITE_CIRCLE_WITH_UPPER_RIGHT_QUADRANT = "\u25F7"
UPPER_LEFT_TRIANGLE = "\u25F8"
UPPER_RIGHT_TRIANGLE = "\u25F9"
LOWER_LEFT_TRIANGLE = "\u25FA"
WHITE_MEDIUM_SQUARE = "\u25FB"
BLACK_MEDIUM_SQUARE = "\u25FC"
WHITE_MEDIUM_SMALL_SQUARE = "\u25FD"
BLACK_MEDIUM_SMALL_SQUARE = "\u25FE"
LOWER_RIGHT_TRIANGLE = "\u25FF"

# Arrows (http://en.wikipedia.org/wiki/Arrow_%28symbol%29)
LEFTWARDS_ARROW = "\u2190"
UPWARDS_ARROW = "\u2191"
RIGHTWARDS_ARROW = "\u2192"
DOWNWARDS_ARROW = "\u2193"
LEFT_RIGHT_ARROW = "\u2194"
UP_DOWN_ARROW = "\u2195"
NORTH_WEST_ARROW = "\u2196"
NORTH_EAST_ARROW = "\u2197"
SOUTH_EAST_ARROW = "\u2198"
SOUTH_WEST_ARROW = "\u2199"
LEFTWARDS_ARROW_WITH_STROKE = "\u219A"
RIGHTWARDS_ARROW_WITH_STROKE = "\u219B"
LEFTWARDS_WAVE_ARROW = "\u219C"
RIGHTWARDS_WAVE_ARROW = "\u219D"
LEFTWARDS_TWO_HEADED_ARROW = "\u219E"
UPWARDS_TWO_HEADED_ARROW = "\u219F"
RIGHTWARDS_TWO_HEADED_ARROW = "\u21A0"
DOWNWARDS_TWO_HEADED_ARROW = "\u21A1"
LEFTWARDS_ARROW_WITH_TAIL = "\u21A2"
RIGHTWARDS_ARROW_WITH_TAIL = "\u21A3"
LEFTWARDS_ARROW_FROM_BAR = "\u21A4"
UPWARDS_ARROW_FROM_BAR = "\u21A5"
RIGHTWARDS_ARROW_FROM_BAR = "\u21A6"
DOWNWARDS_ARROW_FROM_BAR = "\u21A7"
UP_DOWN_ARROW_WITH_BASE = "\u21A8"
LEFTWARDS_ARROW_WITH_HOOK = "\u21A9"
RIGHTWARDS_ARROW_WITH_HOOK = "\u21AA"
LEFTWARDS_ARROW_WITH_LOOP = "\u21AB"
RIGHTWARDS_ARROW_WITH_LOOP = "\u21AC"
LEFT_RIGHT_WAVE_ARROW = "\u21AD"
LEFT_RIGHT_ARROW_WITH_STROKE = "\u21AE"
DOWNWARDS_ZIGZAG_ARROW = "\u21AF"
UPWARDS_ARROW_WITH_TIP_LEFTWARDS = "\u21B0"
UPWARDS_ARROW_WITH_TIP_RIGHTWARDS = "\u21B1"
DOWNWARDS_ARROW_WITH_TIP_LEFTWARDS = "\u21B2"
DOWNWARDS_ARROW_WITH_TIP_RIGHTWARDS = "\u21B3"
RIGHTWARDS_ARROW_WITH_CORNER_DOWNWARDS = "\u21B4"
DOWNWARDS_ARROW_WITH_CORNER_LEFTWARDS = "\u21B5"
ANTICLOCKWISE_TOP_SEMICIRCLE_ARROW = "\u21B6"
CLOCKWISE_TOP_SEMICIRCLE_ARROW = "\u21B7"
NORTH_WEST_ARROW_TO_LONG_BAR = "\u21B8"
LEFTWARDS_ARROW_TO_BAR_OVER_RIGHTWARDS_ARROW_TO_BAR = "\u21B9"
ANTICLOCKWISE_OPEN_CIRCLE_ARROW = "\u21BA"
CLOCKWISE_OPEN_CIRCLE_ARROW = "\u21BB"
LEFTWARDS_HARPOON_WITH_BARB_UPWARDS = "\u21BC"
LEFTWARDS_HARPOON_WITH_BARB_DOWNWARDS = "\u21BD"
UPWARDS_HARPOON_WITH_BARB_RIGHTWARDS = "\u21BE"
UPWARDS_HARPOON_WITH_BARB_LEFTWARDS = "\u21BF"
RIGHTWARDS_HARPOON_WITH_BARB_UPWARDS = "\u21C0"
RIGHTWARDS_HARPOON_WITH_BARB_DOWNWARDS = "\u21C1"
DOWNWARDS_HARPOON_WITH_BARB_RIGHTWARDS = "\u21C2"
DOWNWARDS_HARPOON_WITH_BARB_LEFTWARDS = "\u21C3"
RIGHTWARDS_ARROW_OVER_LEFTWARDS_ARROW = "\u21C4"
UPWARDS_ARROW_LEFTWARDS_OF_DOWNWARDS_ARROW = "\u21C5"
LEFTWARDS_ARROW_OVER_RIGHTWARDS_ARROW = "\u21C6"
LEFTWARDS_PAIRED_ARROWS = "\u21C7"
UPWARDS_PAIRED_ARROWS = "\u21C8"
RIGHTWARDS_PAIRED_ARROWS = "\u21C9"
DOWNWARDS_PAIRED_ARROWS = "\u21CA"
LEFTWARDS_HARPOON_OVER_RIGHTWARDS_HARPOON = "\u21CB"
RIGHTWARDS_HARPOON_OVER_LEFTWARDS_HARPOON = "\u21CC"
LEFTWARDS_DOUBLE_ARROW_WITH_STROKE = "\u21CD"
LEFT_RIGHT_DOUBLE_ARROW_WITH_STROKE = "\u21CE"
RIGHTWARDS_DOUBLE_ARROW_WITH_STROKE = "\u21CF"
LEFTWARDS_DOUBLE_ARROW = "\u21D0"
UPWARDS_DOUBLE_ARROW = "\u21D1"
RIGHTWARDS_DOUBLE_ARROW = "\u21D2"
DOWNWARDS_DOUBLE_ARROW = "\u21D3"
LEFT_RIGHT_DOUBLE_ARROW = "\u21D4"
UP_DOWN_DOUBLE_ARROW = "\u21D5"
NORTH_WEST_DOUBLE_ARROW = "\u21D6"
NORTH_EAST_DOUBLE_ARROW = "\u21D7"
SOUTH_EAST_DOUBLE_ARROW = "\u21D8"
SOUTH_WEST_DOUBLE_ARROW = "\u21D9"
LEFTWARDS_TRIPLE_ARROW = "\u21DA"
RIGHTWARDS_TRIPLE_ARROW = "\u21DB"
LEFTWARDS_SQUIGGLE_ARROW = "\u21DC"
RIGHTWARDS_SQUIGGLE_ARROW = "\u21DD"
UPWARDS_ARROW_WITH_DOUBLE_STROKE = "\u21DE"
DOWNWARDS_ARROW_WITH_DOUBLE_STROKE = "\u21DF"
LEFTWARDS_DASHED_ARROW = "\u21E0"
UPWARDS_DASHED_ARROW = "\u21E1"
RIGHTWARDS_DASHED_ARROW = "\u21E2"
DOWNWARDS_DASHED_ARROW = "\u21E3"
LEFTWARDS_ARROW_TO_BAR = "\u21E4"
RIGHTWARDS_ARROW_TO_BAR = "\u21E5"
LEFTWARDS_THICK_ARROW = "\u21E6"
UPWARDS_THICK_ARROW = "\u21E7"
RIGHTWARDS_THICK_ARROW = "\u21E8"
DOWNWARDS_THICK_ARROW = "\u21E9"
UPWARDS_THICK_ARROW_FROM_BAR = "\u21EA"
UPWARDS_THICK_ARROW_ON_PEDESTAL = "\u21EB"
UPWARDS_THICK_ARROW_ON_PEDESTAL_WITH_HORIZONTAL_BAR = "\u21EC"
UPWARDS_THICK_ARROW_ON_PEDESTAL_WITH_VERTICAL_BAR = "\u21ED"
UPWARDS_THICK_DOUBLE_ARROW = "\u21EE"
UPWARDS_THICK_DOUBLE_ARROW_ON_PEDESTAL = "\u21EF"
RIGHTWARDS_THICK_ARROW_FROM_WALL = "\u21F0"
NORTH_WEST_ARROW_TO_CORNER = "\u21F1"
SOUTH_EAST_ARROW_TO_CORNER = "\u21F2"
UP_DOWN_THICK_ARROW = "\u21F3"
RIGHT_ARROW_WITH_SMALL_CIRCLE = "\u21F4"
DOWNWARDS_ARROW_LEFTWARDS_OF_UPWARDS_ARROW = "\u21F5"
THREE_RIGHTWARDS_ARROWS = "\u21F6"
LEFTWARDS_ARROW_WITH_VERTICAL_STROKE = "\u21F7"
RIGHTWARDS_ARROW_WITH_VERTICAL_STROKE = "\u21F8"
LEFT_RIGHT_ARROW_WITH_VERTICAL_STROKE = "\u21F9"
LEFTWARDS_ARROW_WITH_DOUBLE_VERTICAL_STROKE = "\u21FA"
RIGHTWARDS_ARROW_WITH_DOUBLE_VERTICAL_STROKE = "\u21FB"
LEFT_RIGHT_ARROW_WITH_DOUBLE_VERTICAL_STROKE = "\u21FC"
LEFTWARDS_OPEN_HEADED_ARROW = "\u21FD"
RIGHTWARDS_OPEN_HEADED_ARROW = "\u21FE"
LEFT_RIGHT_OPEN_HEADED_ARROW = "\u21FF"

# Miscellaneous Symbols
BLACK_SUN_WITH_RAYS = "\u2600"
CLOUD = "\u2601"
UMBRELLA = "\u2602"
SNOWMAN = "\u2603"
COMET = "\u2604"
BLACK_STAR = "\u2605"
STAR = "\u2606"
LIGHTNING = "\u2607"
THUNDERSTORM = "\u2608"
SUN = "\u2609"
ASCENDING_NODE = "\u260A"
DESCENDING_NODE = "\u260B"
CONJUNCTION = "\u260C"
OPPOSITION = "\u260D"
BLACK_TELEPHONE = "\u260E"
WHITE_TELEPHONE = "\u260F"
BALLOT_BOX = "\u2610"
BALLOT_BOX_WITH_CHECK = "\u2611"
BALLOT_BOX_WITH_X = "\u2612"
SALTIRE = "\u2613"
UMBRELLA_WITH_RAINDROPS = "\u2614"
HOT_BEVERAGE = "\u2615"
WHITE_SHOGI_PIECE = "\u2616"
BLACK_SHOGI_PIECE = "\u2617"
SHAMROCK = "\u2618"
REVERSED_ROTATED_FLORAL_HEART_BULLET = "\u2619"
BLACK_LEFT_POINTING_INDEX = "\u261A"
BLACK_RIGHT_POINTING_INDEX = "\u261B"
WHITE_LEFT_POINTING_INDEX = "\u261C"
WHITE_UP_POINTING_INDEX = "\u261D"
WHITE_RIGHT_POINTING_INDEX = "\u261E"
WHITE_DOWN_POINTING_INDEX = "\u261F"
SKULL_AND_CROSSBONES = "\u2620"
CAUTION_SIGN = "\u2621"
RADIOACTIVE_SIGN = "\u2622"
BIOHAZARD_SIGN = "\u2623"
CADUCEUS = "\u2624"
ANKH = "\u2625"
RUSSIAN_CROSS = "\u2626"
CHI_RHO = "\u2627"
CROSS_OF_LORRAINE = "\u2628"
CROSS_OF_JERUSALEM = "\u2629"
STAR_AND_CRESCENT = "\u262A"
FARSI_SYMBOL = "\u262B"
ADI_SHAKTI = "\u262C"
HAMMER_AND_SICKLE = "\u262D"
PEACE_SYMBOL = "\u262E"
YIN_AND_YANG = "\u262F"
TRIGRAM_FOR_HEAVEN = "\u2630"
TRIGRAM_FOR_LAKE = "\u2631"
TRIGRAM_FOR_FIRE = "\u2632"
TRIGRAM_FOR_THUNDER = "\u2633"
TRIGRAM_FOR_WIND = "\u2634"
TRIGRAM_FOR_WATER = "\u2635"
TRIGRAM_FOR_MOUNTAIN = "\u2636"
TRIGRAM_FOR_EARTH = "\u2637"
WHEEL_OF_DHARMA = "\u2638"
WHITE_FROWNING_FACE = "\u2639"
WHITE_SMILING_FACE = "\u263A"
BLACK_SMILING_FACE = "\u263B"
WHITE_SUN_WITH_RAYS = "\u263C"
FIRST_QUARTER_MOON = "\u263D"
LAST_QUARTER_MOON = "\u263E"
MERCURY = "\u263F"
FEMALE_SIGN = "\u2640"
EARTH = "\u2641"
MALE_SIGN = "\u2642"
JUPITER = "\u2643"
SATURN = "\u2644"
URANUS = "\u2645"
NEPTUNE = "\u2646"
PLUTO = "\u2647"
ARIES = "\u2648"
TAURUS = "\u2649"
GEMINI = "\u264A"
CANCER = "\u264B"
LEO = "\u264C"
VIRGO = "\u264D"
LIBRA = "\u264E"
SCORPIUS = "\u264F"
SAGITTARIUS = "\u2650"
CAPRICORN = "\u2651"
AQUARIUS = "\u2652"
PISCES = "\u2653"
WHITE_CHESS_KING = "\u2654"
WHITE_CHESS_QUEEN = "\u2655"
WHITE_CHESS_ROOK = "\u2656"
WHITE_CHESS_BISHOP = "\u2657"
WHITE_CHESS_KNIGHT = "\u2658"
WHITE_CHESS_PAWN = "\u2659"
BLACK_CHESS_KING = "\u265A"
BLACK_CHESS_QUEEN = "\u265B"
BLACK_CHESS_ROOK = "\u265C"
BLACK_CHESS_BISHOP = "\u265D"
BLACK_CHESS_KNIGHT = "\u265E"
BLACK_CHESS_PAWN = "\u265F"
BLACK_SPADE_SUIT = "\u2660"
WHITE_HEART_SUIT = "\u2661"
WHITE_DIAMOND_SUIT = "\u2662"
BLACK_CLUB_SUIT = "\u2663"
WHITE_SPADE_SUIT = "\u2664"
BLACK_HEART_SUIT = "\u2665"
BLACK_DIAMOND_SUIT = "\u2666"
WHITE_CLUB_SUIT = "\u2667"
HOT_SPRING = "\u2668"
QUARTER_NOTE = "\u2669"
EIGHTH_NOTE = "\u266A"
BEAMED_EIGHTH_NOTES = "\u266B"
BEAMED_SIXTEENTH_NOTES = "\u266C"
MUSIC_FLAT_SIGN = "\u266D"
MUSIC_NATURAL_SIGN = "\u266E"
MUSIC_SHARP_SIGN = "\u266F"
WEST_SYRIAC_CROSS = "\u2670"
EAST_SYRIAC_CROSS = "\u2671"
UNIVERSAL_RECYCLING_SYMBOL = "\u2672"
RECYCLING_SYMBOL_FOR_TYPE_1_PLASTICS = "\u2673"
RECYCLING_SYMBOL_FOR_TYPE_2_PLASTICS = "\u2674"
RECYCLING_SYMBOL_FOR_TYPE_3_PLASTICS = "\u2675"
RECYCLING_SYMBOL_FOR_TYPE_4_PLASTICS = "\u2676"
RECYCLING_SYMBOL_FOR_TYPE_5_PLASTICS = "\u2677"
RECYCLING_SYMBOL_FOR_TYPE_6_PLASTICS = "\u2678"
RECYCLING_SYMBOL_FOR_TYPE_7_PLASTICS = "\u2679"
RECYCLING_SYMBOL_FOR_GENERIC_MATERIALS = "\u267A"
BLACK_UNIVERSAL_RECYCLING_SYMBOL = "\u267B"
RECYCLED_PAPER_SYMBOL = "\u267C"
PARTIALLY_RECYCLED_PAPER_SYMBOL = "\u267D"
PERMANENT_PAPER_SIGN = "\u267E"
WHEELCHAIR_SYMBOL = "\u267F"
DIE_FACE_1 = "\u2680"
DIE_FACE_2 = "\u2681"
DIE_FACE_3 = "\u2682"
DIE_FACE_4 = "\u2683"
DIE_FACE_5 = "\u2684"
DIE_FACE_6 = "\u2685"
WHITE_CIRCLE_WITH_DOT_RIGHT = "\u2686"
WHITE_CIRCLE_WITH_TWO_DOTS = "\u2687"
BLACK_CIRCLE_WITH_WHITE_DOT_RIGHT = "\u2688"
BLACK_CIRCLE_WITH_TWO_WHITE_DOTS = "\u2689"
MONOGRAM_FOR_YANG = "\u268A"
MONOGRAM_FOR_YIN = "\u268B"
DIGRAM_FOR_GREATER_YANG = "\u268C"
DIGRAM_FOR_LESSER_YIN = "\u268D"
DIGRAM_FOR_LESSER_YANG = "\u268E"
DIGRAM_FOR_GREATER_YIN = "\u268F"
WHITE_FLAG = "\u2690"
BLACK_FLAG = "\u2691"
HAMMER_AND_PICK = "\u2692"
ANCHOR = "\u2693"
CROSSED_SWORDS = "\u2694"
STAFF_OF_AESCULAPIUS = "\u2695"
WEIGHING_SCALES = "\u2696"
ALEMBIC = "\u2697"
FLOWER = "\u2698"
GEAR = "\u2699"
STAFF_OF_HERMES = "\u269A"
ATOM_SYMBOL = "\u269B"
FLEUR_DE_LIS = "\u269C"
OUTLINED_WHITE_STAR = "\u269D"
THREE_LINES_CONVERGING_RIGHT = "\u269E"
THREE_LINES_CONVERGING_LEFT = "\u269F"
WARNING_SIGN = "\u26A0"
HIGH_VOLTAGE_SIGN = "\u26A1"
DOUBLED_FEMALE_SIGN = "\u26A2"
DOUBLED_MALE_SIGN = "\u26A3"
INTERLOCKED_MALE_AND_FEMALE_SIGN = "\u26A4"
MALE_AND_FEMALE_SIGN = "\u26A5"
MALE_WITH_STROKE_SIGN = "\u26A6"
MALE_WITH_STROKE_AND_MALE_AND_FEMALE_SIGN = "\u26A7"
VERTICAL_MALE_WITH_STROKE_SIGN = "\u26A8"
HORIZONTAL_MALE_WITH_STROKE_SIGN = "\u26A9"
MEDIUM_WHITE_CIRCLE = "\u26AA"
MEDIUM_BLACK_CIRCLE = "\u26AB"
MEDIUM_SMALL_WHITE_CIRCLE = "\u26AC"
MARRIAGE_SYMBOL = "\u26AD"
DIVORCE_SYMBOL = "\u26AE"
UNMARRIED_PARTNERSHIP_SYMBOL = "\u26AF"
COFFIN = "\u26B0"
FUNERAL_URN = "\u26B1"
NEUTER = "\u26B2"
CERES = "\u26B3"
PALLAS = "\u26B4"
JUNO = "\u26B5"
VESTA = "\u26B6"
CHIRON = "\u26B7"
BLACK_MOON_LILITH = "\u26B8"
SEXTILE = "\u26B9"
SEMISEXTILE = "\u26BA"
QUINCUNX = "\u26BB"
SESQUIQUADRATE = "\u26BC"
SOCCER_BALL = "\u26BD"
BASEBALL = "\u26BE"
SQUARED_KEY = "\u26BF"
WHITE_DRAUGHTS_MAN = "\u26C0"
WHITE_DRAUGHTS_KING = "\u26C1"
BLACK_DRAUGHTS_MAN = "\u26C2"
BLACK_DRAUGHTS_KING = "\u26C3"
SNOWMAN_WITHOUT_SNOW = "\u26C4"
SUN_BEHIND_CLOUD = "\u26C5"
RAIN = "\u26C6"
BLACK_SNOWMAN = "\u26C7"
THUNDER_CLOUD_AND_RAIN = "\u26C8"
TURNED_WHITE_SHOGI_PIECE = "\u26C9"
TURNED_BLACK_SHOGI_PIECE = "\u26CA"
WHITE_DIAMOND_IN_SQUARE = "\u26CB"
CROSSING_LANES = "\u26CC"
DISABLED_CAR = "\u26CD"
OPHIUCHUS = "\u26CE"
PICK = "\u26CF"
CAR_SLIDING = "\u26D0"
HELMET_WITH_WHITE_CROSS = "\u26D1"
CIRCLED_CROSSING_LANES = "\u26D2"
CHAINS = "\u26D3"
NO_ENTRY = "\u26D4"
ALTERNATE_ONE_WAY_LEFT_WAY_TRAFFIC = "\u26D5"
BLACK_TWO_WAY_LEFT_WAY_TRAFFIC = "\u26D6"
WHITE_TWO_WAY_LEFT_WAY_TRAFFIC = "\u26D7"
BLACK_LEFT_LANE_MERGE = "\u26D8"
WHITE_LEFT_LANE_MERGE = "\u26D9"
DRIVE_SLOW_SIGN = "\u26DA"
HEAVY_WHITE_DOWN_POINTING_TRIANGLE = "\u26DB"
LEFT_CLOSED_ENTRY = "\u26DC"
SQUARED_SALTIRE = "\u26DD"
FALLING_DIAGONAL_IN_WHITE_CIRCLE_IN_BLACK_SQUARE = "\u26DE"
BLACK_TRUCK = "\u26DF"
RESTRICTED_LEFT_ENTRY_1 = "\u26E0"
RESTRICTED_LEFT_ENTRY_2 = "\u26E1"
ASTRONOMICAL_SYMBOL_FOR_URANUS = "\u26E2"
HEAVY_CIRCLE_WITH_STROKE_AND_TWO_DOTS_ABOVE = "\u26E3"
PENTAGRAM = "\u26E4"
RIGHT_HANDED_INTERLACED_PENTAGRAM = "\u26E5"
LEFT_HANDED_INTERLACED_PENTAGRAM = "\u26E6"
INVERTED_PENTAGRAM = "\u26E7"
BLACK_CROSS_ON_SHIELD = "\u26E8"
SHINTO_SHRINE = "\u26E9"
CHURCH = "\u26EA"
CASTLE = "\u26EB"
HISTORIC_SITE = "\u26EC"
GEAR_WITHOUT_HUB = "\u26ED"
GEAR_WITH_HANDLES = "\u26EE"
MAP_SYMBOL_FOR_LIGHTHOUSE = "\u26EF"
MOUNTAIN = "\u26F0"
UMBRELLA_ON_GROUND = "\u26F1"
FOUNTAIN = "\u26F2"
FLAG_IN_HOLE = "\u26F3"
FERRY = "\u26F4"
SAILBOAT = "\u26F5"
SQUARE_FOUR_CORNERS = "\u26F6"
SKIER = "\u26F7"
ICE_SKATE = "\u26F8"
PERSON_WITH_BALL = "\u26F9"
TENT = "\u26FA"
JAPANESE_BANK_SYMBOL = "\u26FB"
HEADSTONE_GRAVEYARD_SYMBOL = "\u26FC"
FUEL_PUMP = "\u26FD"
CUP_ON_BLACK_SQUARE = "\u26FE"
WHITE_FLAG_WITH_HORIZONTAL_MIDDLE_BLACK_STRIPE = "\u26FF"

# Mathematical symbols
MATH_TIMES = "\u00D7"
MATH_HEAVY_MULTIPLICATION = "\u2716"
MATH_OSLASH = "\u00D8"
MATH_UP_TACK = "\u22A5"
