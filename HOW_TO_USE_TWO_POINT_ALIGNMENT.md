# 🎯 كيفية استخدام ميزة المحاذاة بين النقطتين

## ✅ **الوضع الحالي**
- ✅ البرنامج bCNC يعمل
- ✅ الكاميرا تعمل (OpenCV 4.10.0)
- ✅ تم إضافة ميزة المحاذاة بين النقطتين
- ✅ زر المحاذاة متاح في واجهة الكاميرا

## 🚀 **خطوات الاستخدام**

### الخطوة 1: تفعيل الكاميرا في البرنامج

1. **في نافذة bCNC المفتوحة**:
   - اذهب إلى تبويب **"Probe"**
   - ابحث عن قسم **"Camera"**
   - اضغط زر **"Switch To"** لتفعيل الكاميرا
   - يجب أن ترى صورة الكاميرا في منطقة الرسم

### الخطوة 2: فتح أداة المحاذاة

1. **في قسم Camera**:
   - اضغط زر **"Two Point Alignment"**
   - ستفتح نافذة الإضافة الجديدة

### الخطوة 3: التقاط النقاط المرجعية

1. **النقطة الأولى**:
   - حرك الآلة إلى نقطة مرجعية على قطعة العمل (مثل الزاوية)
   - اضغط **"Capture Point 1"**
   - ستظهر الإحداثيات في الحقول

2. **النقطة الثانية**:
   - حرك الآلة إلى نقطة أخرى على قطعة العمل (مثل الحافة المقابلة)
   - اضغط **"Capture Point 2"**
   - ستظهر الإحداثيات في الحقول

### الخطوة 4: تحديد النقاط المستهدفة

1. **في حقول Target Points**:
   - أدخل الإحداثيات المطلوبة للنقطة الأولى (Target 1 X, Y)
   - أدخل الإحداثيات المطلوبة للنقطة الثانية (Target 2 X, Y)

### الخطوة 5: حساب وتطبيق المحاذاة

1. **احسب التحويل**:
   - اضغط **"Calculate"**
   - ستظهر معلومات التحويل في شريط الحالة

2. **طبق المحاذاة**:
   - اختر كتل G-code في المحرر (Editor)
   - اضغط **"Apply"**
   - ستظهر كتل جديدة محاذاة

## 🎥 **استخدام واجهة الويب (اختياري)**

### تشغيل خادم الويب:
1. في البرنامج: **Tools → Web Pendant**
2. اضغط **"Start"**
3. افتح المتصفح واذهب للعنوان المعروض
4. اختر **"Camera"**

### أزرار المحاذاة في الويب:
- **"Capture Point 1"** - التقاط النقطة الأولى
- **"Capture Point 2"** - التقاط النقطة الثانية
- **"Calculate"** - حساب المحاذاة
- **"Apply"** - تطبيق التحويل

## 💡 **مثال عملي**

### المشكلة:
قطعة عمل مائلة بزاوية 15° ومزاحة عن الموضع المطلوب

### الحل:
1. **التقط النقاط**:
   - النقطة 1: زاوية القطعة (10.0, 10.5)
   - النقطة 2: حافة القطعة (50.0, 15.0)

2. **حدد الأهداف**:
   - الهدف 1: (10.0, 10.0) - محاذاة أفقية
   - الهدف 2: (50.0, 10.0) - محاذاة أفقية

3. **النتيجة**:
   - دوران: -7.13°
   - تكبير: 0.998
   - إزاحة: X=0.0, Y=-0.5

## ⚠️ **نصائح مهمة**

### للحصول على أفضل النتائج:
1. **اختر نقاط متباعدة** للحصول على دقة أفضل
2. **استخدم معالم واضحة** على قطعة العمل
3. **اختبر مع G-code بسيط** أولاً
4. **احفظ النقاط المرجعية** للاستخدام المتكرر

### حل المشاكل:
- **"النقاط لا يمكن أن تكون متطابقة"**: التقط نقطتين مختلفتين
- **"يرجى حساب المحاذاة أولاً"**: اضغط Calculate قبل Apply
- **"يرجى اختيار كتل للمحاذاة"**: اختر G-code في المحرر

## 🎉 **استمتع بالميزة الجديدة!**

الآن يمكنك:
- ✅ محاذاة قطع العمل المائلة تلقائياً
- ✅ توفير الوقت في الإعداد
- ✅ تقليل الأخطاء والهدر
- ✅ العمل بدقة أكبر
- ✅ استخدام الكاميرا للتحكم الدقيق

---
**تم إنشاؤها بواسطة**: AI Assistant  
**التاريخ**: 2025-01-18  
**الإصدار**: 1.0  

**البرنامج جاهز للاستخدام مع الميزة الجديدة! 🚀**
