# دليل البدء السريع - محاذاة النقطتين

## 🎯 ما هي هذه الميزة؟
تسمح لك بمحاذاة G-code باستخدام نقطتين مرجعيتين من الكاميرا. مفيدة عندما تكون قطعة العمل غير موضوعة بشكل مثالي على الآلة.

## 🚀 كيفية الاستخدام

### الطريقة الأولى: من واجهة البرنامج الرئيسية

1. **افتح الأداة**:
   - اذهب إلى تبويب Probe
   - اضغط على زر "Two Point Alignment" في قسم Camera

2. **التقط النقاط المرجعية**:
   - حرك الآلة إلى النقطة الأولى على قطعة العمل
   - اضغط "Capture Point 1"
   - حرك الآلة إلى النقطة الثانية
   - اضغط "Capture Point 2"

3. **حدد النقاط المستهدفة**:
   - أدخل الإحداثيات المطلوبة للنقطة الأولى (Target 1 X, Y)
   - أدخل الإحداثيات المطلوبة للنقطة الثانية (Target 2 X, Y)

4. **احسب وطبق**:
   - اضغط "Calculate" لحساب التحويل
   - اختر كتل G-code التي تريد محاذاتها
   - اضغط "Apply" لإنشاء نسخ محاذاة

### الطريقة الثانية: من واجهة الكاميرا عبر الويب

1. **افتح واجهة الكاميرا**:
   - افتح المتصفح واذهب إلى عنوان pendant
   - انتقل إلى صفحة الكاميرا

2. **استخدم أزرار المحاذاة**:
   - حرك الآلة للنقطة الأولى واضغط "Capture Point 1"
   - حرك للنقطة الثانية واضغط "Capture Point 2"
   - اضغط "Calculate" لحساب المحاذاة
   - اضغط "Apply" لتطبيق التحويل

## 💡 نصائح للحصول على أفضل النتائج

1. **اختر نقاط مرجعية جيدة**:
   - استخدم نقاط متباعدة للحصول على دقة أفضل
   - اختر معالم واضحة وسهلة التحديد

2. **إعداد الكاميرا**:
   - تأكد من معايرة الكاميرا بشكل صحيح
   - استخدم إضاءة جيدة للرؤية الواضحة

3. **سير العمل**:
   - اختبر دائماً مع G-code بسيط أولاً
   - احفظ النقاط المرجعية إذا كنت تحتاج لتكرار المحاذاة

## ⚠️ حل المشاكل الشائعة

- **"النقاط لا يمكن أن تكون متطابقة"**: تأكد من التقاط نقطتين مختلفتين
- **"يرجى حساب المحاذاة أولاً"**: شغل خطوة الحساب قبل التطبيق
- **"يرجى اختيار كتل للمحاذاة"**: اختر كتل G-code في المحرر أولاً

## 🔧 الملفات المضافة/المعدلة

- `bCNC/plugins/twoPointAlignment.py` - ملف الإضافة الرئيسي
- `bCNC/ProbePage.py` - إضافة زر المحاذاة لواجهة الكاميرا
- `bCNC/pendant/camera.html` - إضافة أزرار التحكم لواجهة الويب

## 📊 مثال عملي

**المشكلة**: قطعة عمل مائلة بزاوية 15 درجة ومزاحة 5mm

**الحل**:
1. التقط نقطة في زاوية القطعة (مثلاً 10, 10)
2. التقط نقطة أخرى على حافة القطعة (مثلاً 50, 15)
3. حدد النقاط المستهدفة (10, 10) و (50, 10) للمحاذاة الأفقية
4. احسب وطبق التحويل

**النتيجة**: G-code محاذى تماماً مع قطعة العمل!

---
**تم إنشاؤها بواسطة**: AI Assistant  
**التاريخ**: 2025-01-18  
**الإصدار**: 1.0
