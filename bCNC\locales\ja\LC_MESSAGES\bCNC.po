# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: bCNC Japanese\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-19 19:55+0200\n"
"PO-Revision-Date: 2022-07-19 20:24+0200\n"
"Last-Translator: @SteveMoto [GitHub]\n"
"Language-Team: hoge\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-SourceCharset: UTF-8\n"

#: bCNC/controllers/_GenericGRBL.py:51
msgid ""
"(Compile Option) Grbl '$' setting value exceeds the maximum step rate "
"supported."
msgstr ""
"(コンパイル オプション) Grbl '$' の設定値は, サポートされたステップ速度を超え"
"ています."

#: bCNC/controllers/_GenericGRBL.py:55
msgid ""
"(Grbl-Mega Only) Build info or startup line exceeded EEPROM line length "
"limit."
msgstr ""
"(Grbl-Mega Only) ビルド情報やスタートアップラインが, EEPROMの容量制限を超えて"
"います."

#: bCNC/controllers/_GenericGRBL.py:69
msgid ""
"A G-code command implicitly or explicitly requires XYZ axis words in the "
"block, but none were detected."
msgstr ""
"Gコードのコマンドは, 陰に陽に, ブロック内でXYZ軸の命令を必要としますが, 見つ"
"かりませんでした."

#: bCNC/controllers/_GenericGRBL.py:75
msgid ""
"A G-code command was sent, but is missing some required P or L value words "
"in the line."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:87
msgid ""
"A G2 or G3 arc was commanded but there are no XYZ axis words in the selected "
"plane to trace the arc."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:96
msgid ""
"A G2 or G3 arc, traced with the offset definition, is missing the IJK offset "
"word in the selected plane to trace the arc."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:93
msgid ""
"A G2 or G3 arc, traced with the radius definition, had a mathematical error "
"when computing the arc geometry. Try either breaking up the arc into semi-"
"circles or quadrants, or redefine them with the arc offset definition."
msgstr ""

#: bCNC/ToolsPage.py:824
msgid ""
"Add additional margin/offset around islands to compensate for endmill "
"radius. This is automaticaly done for all islands if they are marked as tabs."
msgstr ""

#: bCNC/ProbePage.py:714
msgid ""
"Add an orientation marker. Jog first the machine to the marker position and "
"then click on canvas to add the marker."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:16
msgid ""
"Alarm is an emergency state. Something has gone terribly wrong when these "
"occur. Typically, they are caused by limit error when the machine has moved "
"or wants to move outside the machine space and crash into something. They "
"also report problems if Grbl is lost and can't guarantee positioning or a "
"probe command has failed. Once in alarm-mode, Grbl will lock out and shut "
"down everything until the user issues a reset. Even after a reset, Grbl will "
"remain in alarm-mode, block all G-code from being executed, but allows the "
"user to override the alarm manually. This is to ensure the user knows and "
"acknowledges the problem and has taken steps to fix or account for it."
msgstr ""
"アラームは非常時に発生し, とてもまずいことが起こっています... 一般的には, 可"
"動範囲を超えて, 何かに衝突したときに起こる, リミット エラーです. また, これら"
"は, Grblが切断され位置情報が保証されないときや, プローブコマンドが失敗したと"
"きにも起こりえます. 一度警報状態になると, ユーザーがリセットをするまで, Grbl"
"は動作禁止状態となり, 実行中の動作すべてを終了します. 例えリセットしても, "
"Grblは警報状態のままであり, 実行中のすべてのGコードをブロックしますが, 手動で"
"警報を解除することができます. ユーザーは, これらの機能によって, 問題を発見し,"
"認識 することができ, 問題点を修正したり, 原因を特定することができます."

#: bCNC/controllers/_GenericGRBL.py:33
msgid ""
"All is good! Everything in the last line was understood by Grbl and was "
"successfully processed and executed."
msgstr ""
"すばらしい!! Grblは, 最終行のすべてを理解しました. また, 確実に処理され, 実行"
"されました."

#: bCNC/plugins/linearize.py:50
msgid ""
"Also subdivide the lines. Otherwise only arcs and splines will be subdivided"
msgstr ""

#: bCNC/bmain.py:2418
msgid ""
"Autolevel/probe information already exists.\n"
"Delete it?"
msgstr ""

#: bCNC/plugins/slicemesh.py:276
msgid ""
"Calculating distance {} of {} (SciPy not installed => using SLOW fallback "
"method)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:505
msgid ""
"Currently there is some weird behaviour sometimes when trying to link "
"segments of pocket internally, so it can be disabled using this option. This "
"workaround should be fixed and removed in future."
msgstr ""

#: bCNC/ToolsPage.py:797
msgid ""
"Do the last cut once more in opposite direction. Helix bottom is disabled in "
"such case."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:168
msgid ""
"Door closed and resuming. Restoring from park, if applicable. Reset will "
"throw an alarm."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:165
msgid ""
"Door opened. Hold (or parking retract) in-progress. Reset will throw an "
"alarm."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:131
msgid ""
"G-code motion target exceeds machine travel. Machine position safely "
"retained. Alarm may be unlocked."
msgstr ""

#: bCNC/bmain.py:2526
msgid ""
"Gcode file {} was changed since editing started\n"
"Reload new version?"
msgstr ""

#: bCNC/ToolsPage.py:1027
msgid ""
"Generate pocket after profiling? Useful for making pockets with overcuts."
msgstr ""

#: bCNC/plugins/sketch.py:368
msgid ""
"Generated Sketch size W={} x H={} x distance={}, Total line:{}, Total length:"
"{}"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:43
msgid ""
"Grbl '$' command cannot be used unless Grbl is IDLE. Ensures smooth "
"operation during a job."
msgstr ""
"Grblが待機状態でないときは, Grbl '$' コマンドは使えません. 稼働中のスムースな"
"制御を確実にするためです."

#: bCNC/controllers/_GenericGRBL.py:19
msgid ""
"Grbl is in g-code check mode. If you send g-code to it, it will only check "
"it without actualy doing any motion. You can exit this by $C command (Or "
"equivalent button in terminal tab)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:26
msgid ""
"Grbl is in queue state. This also means you have relatively old GRBL "
"version, there are even 0.9 versions newer than this."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:23
msgid ""
"Grbl is in sleep mode. Motors are disabled, so you can move them manualy. "
"That also means that your machine might have lost the position (or "
"microsteps) and you may need to re-zero. Perform reset+unlock (or stop) to "
"wake Grbl again."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:78
msgid ""
"Grbl supports six work coordinate systems G54-G59. G59.1, G59.2, and G59.3 "
"are not supported."
msgstr ""
"Grblは,  G54-G59の6つの調整方法をサポートしていますが、 G59.1, G59.2, G59.3 "
"には対応していません."

#: bCNC/controllers/_GenericGRBL.py:128
msgid ""
"Hard limit triggered. Machine position is likely lost due to sudden and "
"immediate halt. Re-homing is highly recommended."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:148
msgid ""
"Homing fail. Could not find limit switch within search distance. Defined as "
"1.5 * max_travel on search and 5 * pulloff on locate phases."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:158
msgid ""
"Homing fail. Could not find second limit switch for auto squared axis within "
"search distances. Try increasing max travel, decreasing pull-off distance, "
"or check wiring. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:145
msgid ""
"Homing fail. Cycle failed to clear limit switch when pulling off. Try "
"increasing pull-off setting or check wiring."
msgstr ""

#: bCNC/bmain.py:231
msgid ""
"MDI Command line: Accept g-code commands or macro commands (RESET/HOME...) "
"or editor commands (move,inkscape, round...) [Space or Ctrl-Space]"
msgstr ""

#: bCNC/plugins/linearize.py:41
msgid ""
"Maximal length of resulting lines, smaller number means more precise output "
"and longer g-code. Length will be automaticaly truncated to be even across "
"whole subdivided segment."
msgstr ""

#: bCNC/ControlPage.py:1045
msgid ""
"Move to Origin.\n"
"User configurable button.\n"
"Right click to configure."
msgstr ""

#: bCNC/ControlPage.py:609 bCNC/ControlPage.py:916
msgid ""
"No info available.\n"
"Please contact the author."
msgstr ""

#: bCNC/plugins/endmilloffset.py:454
msgid ""
"Number of contours (Custom offset count) : indicates the number of contours "
"if custom selected. MAX:"
msgstr ""

#: bCNC/ProbePage.py:904
msgid ""
"Please verify that the probe is connected.\n"
"\n"
"Show this message again?"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:140
msgid ""
"Probe fail. Probe did not contact the workpiece within the programmed travel "
"for G38.2 and G38.4."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:137
msgid ""
"Probe fail. The probe is not in the expected initial state before starting "
"probe cycle, where G38.2 and G38.3 is not triggered and G38.4 and G38.5 is "
"triggered."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:134
msgid ""
"Reset while in motion. Grbl cannot guarantee position. Lost steps are "
"likely. Re-homing is highly recommended."
msgstr ""

#: bCNC/ProbePage.py:211
msgid ""
"Set current XY location as autoleveling Z-zero (recalculate probed data to "
"be relative to this XY origin point)"
msgstr ""

#: bCNC/ControlPage.py:251
msgid ""
"Show current state of the machine\n"
"Click to see details\n"
"Right-Click to clear alarm/errors"
msgstr ""

#: bCNC/plugins/dragknife.py:91
msgid ""
"Simulation is currently approximated by using lots of short lines. This is "
"the length of these lines."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:110
msgid ""
"Spindle not running when motion commanded in CSS or spindle sync mode. "
"(grblHAL)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:487 bCNC/plugins/endmilloffset.py:496
msgid ""
"Tabs are always ignored. You can select if all islands are active, none, or "
"only selected"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:102
msgid ""
"The G43.1 dynamic tool length offset command cannot apply an offset to an "
"axis other than its configured axis. The Grbl default axis is the Z-axis."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:81
msgid ""
"The G53 G-code command requires either a G0 seek or G1 feed motion mode to "
"be active. A different motion was active."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:90
msgid ""
"The motion command has an invalid target. G2, G3, and G38.2 generates this "
"error, if the arc is impossible to generate or if the probe target is the "
"current position."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:84
msgid ""
"There are unused axis words in the block and G80 motion mode cancel is "
"active."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:99
msgid ""
"There are unused, leftover G-code words that aren't used by any command in "
"the block."
msgstr ""

#: bCNC/Utils.py:709
#, fuzzy
#| msgid ""
#| "There was an error sending the report\n"
#| "Code={} {}"
msgid ""
"There was an error sending the report\n"
"Code={} {}"
msgstr ""
"報告の送信中にエラーが発生しました\n"
"エラーコード={} {}"

#: bCNC/plugins/endmilloffset.py:471
msgid ""
"This can be used to switch between Conventional and Climb milling. If unsure "
"use Convetional (default)."
msgstr ""

#: bCNC/plugins/drillmark.py:23
msgid ""
"This plugin is for creating drilling marks with a laser engraver\n"
"        for manual drilling"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:105
msgid ""
"Tool number greater than max supported value or undefined tool selected. "
"(grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:65
msgid ""
"Two G-code commands that both require the use of the XYZ axis words were "
"detected in the block."
msgstr ""

#: bCNC/plugins/dragknife.py:82
msgid ""
"Use this option to simulate cuting of dragknife path. Resulting shape will "
"reflect what shape will actuall be cut. This should reverse the dragknife "
"procedure and give you back the original shape from g-code that was "
"previously processed for dragknife."
msgstr ""

#: bCNC/bmain.py:2722
msgid ""
"\n"
"Would you like open it locally?"
msgstr ""

#: bCNC/bmain.py:888
msgid ""
"bCNC/\tAn advanced fully featured\n"
"\tg-code sender for GRBL."
msgstr ""

#: bCNC/plugins/dragknife.py:72
msgid ""
"direction that knife blade is facing before and after cut. Eg.: if you set "
"this to X+, then the knifes rotation axis should be on the right side of the "
"tip. Meaning that the knife is ready to cut towards right immediately "
"without pivoting. If you cut multiple shapes in single operation, it's "
"important to have this set consistently across all of them."
msgstr ""

#: bCNC/plugins/arcfit.py:47
msgid ""
"how precisely must line fit. set to 0 to disable line fitting, but at least "
"some line fitting (0.001 to 0.01) might be needed to fix arcs, so they can "
"be fit"
msgstr ""

#: bCNC/plugins/endmilloffset.py:445
msgid ""
"indicates the number of profile passes (single,custom number,full pocket)"
msgstr ""

#: bCNC/ToolsPage.py:783
msgid ""
"positive value = relative to tool diameter (5 to 10 probably makes sense), "
"negative = absolute ramp distance (you probably don't need this). Also note "
"that ramp can't currently be shorter than affected g-code segment."
msgstr ""

#: bCNC/plugins/dragknife.py:63
msgid ""
"retract to this height for pivots (useful for thick materials, you should "
"enter number slightly lower than material thickness)"
msgstr ""

#: bCNC/CNCCanvas.py:2003
msgid "     line: {}\n"
msgstr ""

#: bCNC/bmain.py:1195 bCNC/bmain.py:1276
msgid "# Blocks:"
msgstr ""

#: bCNC/TerminalPage.py:182
msgid "$ Display build information of Grbl"
msgstr ""

#: bCNC/TerminalPage.py:103
msgid "$# Display parameters of Grbl"
msgstr ""

#: bCNC/TerminalPage.py:88
msgid "$$ Display settings of Grbl"
msgstr ""

#: bCNC/ToolsPage.py:1173
msgid "$0 Step pulse time [us]"
msgstr ""

#: bCNC/ToolsPage.py:1174
msgid "$1 Step idle delay [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1180
msgid "$10 Status report [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1195
msgid "$100 X steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1196
msgid "$101 Y steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1197
msgid "$102 Z steps/mm"
msgstr ""

#: bCNC/ToolsPage.py:1181
msgid "$11 Junction deviation [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1198
msgid "$110 X max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1199
msgid "$111 Y max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1200
msgid "$112 Z max rate [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1182
msgid "$12 Arc tolerance [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1201
msgid "$120 X acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1202
msgid "$121 Y acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1203
msgid "$122 Z acceleration [mm/sec^2]"
msgstr ""

#: bCNC/ToolsPage.py:1183
msgid "$13 Report inches"
msgstr ""

#: bCNC/ToolsPage.py:1204
msgid "$130 X max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1205
msgid "$131 Y max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1206
msgid "$132 Z max travel [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1207
msgid "$140 X homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1208
msgid "$141 Y homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1209
msgid "$142 Z homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1175
msgid "$2 Step port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1184
msgid "$20 Soft limits"
msgstr ""

#: bCNC/ToolsPage.py:1185
msgid "$21 Hard limits"
msgstr ""

#: bCNC/ToolsPage.py:1186
msgid "$22 Homing cycle"
msgstr ""

#: bCNC/ToolsPage.py:1187
msgid "$23 Homing direction invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1188
msgid "$24 Homing feed [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1189
msgid "$25 Homing seek [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1190
msgid "$26 Homing debounce [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1191
msgid "$27 Homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1176
msgid "$3 Direction port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1192
msgid "$30 Max spindle speed [RPM]"
msgstr ""

#: bCNC/ToolsPage.py:1193
msgid "$31 Min spindle speed [RPM]"
msgstr ""

#: bCNC/ToolsPage.py:1194
msgid "$32 Laser mode enable"
msgstr ""

#: bCNC/ToolsPage.py:1177
msgid "$4 Step enable invert"
msgstr ""

#: bCNC/ToolsPage.py:1178
msgid "$5 Limit pins invert"
msgstr ""

#: bCNC/ToolsPage.py:1179
msgid "$6 Probe pin invert"
msgstr ""

#: bCNC/TerminalPage.py:165
msgid "$C Enable/Disable checking of gcode"
msgstr ""

#: bCNC/TerminalPage.py:117
msgid "$G Display state of Grbl"
msgstr ""

#: bCNC/TerminalPage.py:133
msgid "$I Display build information of Grbl"
msgstr ""

#: bCNC/TerminalPage.py:148
msgid "$N Display startup configuration of Grbl"
msgstr ""

#: bCNC/bmain.py:2450
msgid "'{}' loaded"
msgstr ""

#: bCNC/bmain.py:2446
msgid "'{}' reloaded at '{}'"
msgstr ""

#: bCNC/bmain.py:2459
msgid "'{}' saved"
msgstr ""

#: bCNC/EditorPage.py:376
msgid "(Un)Comment selected lines"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:120
msgid "(grblHAL internal)"
msgstr ""

#: bCNC/ControlPage.py:1005 bCNC/ControlPage.py:1482
msgid "+"
msgstr ""

#: bCNC/ControlPage.py:1174 bCNC/ControlPage.py:1646
msgid "-"
msgstr ""

#: bCNC/plugins/slicemesh.py:58
msgid ".STL/.PLY file to slice"
msgstr ""

#: bCNC/ProbePage.py:1742
msgid "1. Spindle"
msgstr ""

#: bCNC/ProbePage.py:1748
msgid "2. Camera"
msgstr ""

#: bCNC/plugins/slicemesh.py:67
msgid "3D slice (devel)"
msgstr ""

#: bCNC/CNCCanvas.py:2001
msgid ">>> ERROR: {}\n"
msgstr ""

#: bCNC/ControlPage.py:1407
msgid "A"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:67
msgid "A G-code word was repeated in the block."
msgstr "ブロック内で, Gコードの命令が繰り返されています."

#: bCNC/ControlPage.py:651
msgid "A work position (click to set)"
msgstr ""

#: bCNC/ControlPage.py:727
msgid "A=0"
msgstr ""

#: bCNC/ControlPage.py:793
msgid "ABC=0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:117
msgid "ATC: current tool is not set. Set current tool with M61. (grblHAL)"
msgstr ""

#: bCNC/FilePage.py:216
msgid "About"
msgstr ""

#: bCNC/FilePage.py:224
msgid "About the program"
msgstr ""

#: bCNC/bmain.py:847
msgid "About {} v{}"
msgstr ""

#: bCNC/ToolsPage.py:641
msgid "Acceleration x"
msgstr "x軸 加速度"

#: bCNC/ToolsPage.py:642
msgid "Acceleration y"
msgstr "y軸 加速度"

#: bCNC/ToolsPage.py:643
msgid "Acceleration z"
msgstr "z軸 加速度"

#: bCNC/EditorPage.py:335
msgid "Active"
msgstr ""

#: bCNC/plugins/trochoidPath.py:48 bCNC/plugins/trochoidal_3D.py:72
msgid "Adaptative"
msgstr ""

#: bCNC/EditorPage.py:258 bCNC/ProbePage.py:702 bCNC/ToolsPage.py:1472
msgid "Add"
msgstr "追加"

#: bCNC/ToolsPage.py:1478
msgid "Add a new operation/object"
msgstr "新しい 操作/オブジェクト を追加"

#: bCNC/CNCCanvas.py:468
msgid "Add an orientation marker"
msgstr ""

#: bCNC/plugins/zigzag.py:108
msgid "Additional length at start/end"
msgstr "開始,終了時に追加する長さ"

#: bCNC/plugins/endmilloffset.py:478
msgid "Additional offset (mm)"
msgstr ""

#: bCNC/plugins/trochoidPath.py:41 bCNC/plugins/trochoidal_3D.py:65
#: bCNC/ToolsPage.py:1013
msgid "Additional offset distance"
msgstr ""

#: bCNC/ProbePage.py:2111
msgid "After a tool change distance to scan starting from ProbeZ"
msgstr ""

#: bCNC/ToolsPage.py:617
#, fuzzy
msgid "Align Camera"
msgstr "カメラ"

#: bCNC/ToolsPage.py:620
msgid "Align Camera Angle"
msgstr ""

#: bCNC/ToolsPage.py:619
msgid "Align Camera Height"
msgstr ""

#: bCNC/ToolsPage.py:618
msgid "Align Camera Width"
msgstr ""

#: bCNC/ProbePage.py:831
msgid "Align GCode with the machine markers"
msgstr ""

#: bCNC/EditorPage.py:106 bCNC/bmain.py:140
msgid "All"
msgstr "すべて"

#: bCNC/bmain.py:1235
msgid "All GCode"
msgstr ""

#: bCNC/bmain.py:119
msgid "All accepted"
msgstr ""

#: bCNC/bmain.py:2609
msgid "Already running"
msgstr ""

#: bCNC/ToolsPage.py:717
msgid "Angle"
msgstr ""

#: bCNC/ProbePage.py:808
msgid "Angle:"
msgstr ""

#: bCNC/plugins/spiral.py:481
msgid "Approach Height (Safe Z)"
msgstr ""

#: bCNC/plugins/arcfit.py:11
msgid "ArcFit"
msgstr ""

#: bCNC/ProbePage.py:956
msgid "At least one probe direction should be specified"
msgstr ""

#: bCNC/EditorPage.py:198 bCNC/ProbePage.py:128 bCNC/ProbePage.py:254
#: bCNC/ProbePage.py:1224
msgid "Autolevel"
msgstr ""

#: bCNC/ProbePage.py:135
msgid "Autolevel Z surface"
msgstr ""

#: bCNC/ProbePage.py:573
msgid "Automatic GOTO after probing"
msgstr ""

#: bCNC/Utils.py:602
msgid "Automatic error reporting"
msgstr "自動エラー報告送信"

#: bCNC/plugins/midi2cnc.py:70
msgid "Axis to be used"
msgstr ""

#: bCNC/ControlPage.py:1491
msgid "B"
msgstr ""

#: bCNC/ControlPage.py:668
msgid "B work position (click to set)"
msgstr ""

#: bCNC/ControlPage.py:743
msgid "B=0"
msgstr ""

#: bCNC/ControlPage.py:777
msgid "BC=0"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "BEFORE & AFTER probing"
msgstr ""

#: bCNC/FilePage.py:316
msgid "Baud:"
msgstr "ボーレート:"

#: bCNC/EditorPage.py:250
msgid "Block"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:126
msgid "Bluetooth initalisation failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/jigsaw.py:362
msgid "Board height"
msgstr ""

#: bCNC/plugins/jigsaw.py:361
msgid "Board width"
msgstr ""

#: bCNC/EditorPage.py:464
msgid "Bottom"
msgstr ""

#: bCNC/EditorPage.py:461
msgid "Bottom-Left"
msgstr ""

#: bCNC/EditorPage.py:467
msgid "Bottom-Right"
msgstr ""

#: bCNC/plugins/bowl.py:14
msgid "Bowl"
msgstr "ボール"

#: bCNC/plugins/box.py:19
msgid "Box"
msgstr "箱"

#: bCNC/TerminalPage.py:215
msgid "Buffered commands"
msgstr ""

#: bCNC/TerminalPage.py:126
msgid "Build"
msgstr ""

#: bCNC/plugins/drillmark.py:49
msgid "Burn power for drillmark"
msgstr ""

#: bCNC/plugins/drillmark.py:48
msgid "Burn time for drillmark"
msgstr ""

#: bCNC/ControlPage.py:1410
msgid "C"
msgstr ""

#: bCNC/ControlPage.py:685
msgid "C work position (click to set)"
msgstr ""

#: bCNC/ControlPage.py:759
msgid "C=0"
msgstr ""

#: bCNC/ToolsPage.py:1522 bCNC/ToolsPage.py:2090
msgid "CAM"
msgstr ""

#: bCNC/EditorPage.py:596
msgid "CCW"
msgstr ""

#: bCNC/ProbePage.py:507
msgid "CIRCLE"
msgstr ""

#: bCNC/ControlPage.py:2363
msgid "CNC communication and control"
msgstr ""

#: bCNC/bmain.py:636
msgid "CNC is currently running, please stop it before."
msgstr ""

#: bCNC/ToolsPage.py:926
msgid "CUT selected paths"
msgstr ""

#: bCNC/EditorPage.py:566
msgid "CW"
msgstr ""

#: bCNC/ProbePage.py:1926 bCNC/ProbePage.py:2132
msgid "Calibrate"
msgstr ""

#: bCNC/ProbePage.py:2121
msgid "Calibration:"
msgstr ""

#: bCNC/ProbePage.py:142 bCNC/ProbePage.py:1630 bCNC/ToolsPage.py:1870
msgid "Camera"
msgstr "カメラ"

#: bCNC/ToolsPage.py:1878
msgid "Camera Configuration"
msgstr "カメラ設定"

#: bCNC/ProbePage.py:1701
msgid "Camera cross hair diameter [units]"
msgstr ""

#: bCNC/ProbePage.py:1643
msgid "Camera location inside canvas"
msgstr ""

#: bCNC/ProbePage.py:1718 bCNC/ProbePage.py:1727
msgid "Camera offset from gantry"
msgstr ""

#: bCNC/ProbePage.py:1862
msgid "Camera offset is updated"
msgstr ""

#: bCNC/ProbePage.py:1655
#, fuzzy
msgid "Camera rotation [degrees]"
msgstr "解像度 (角度)"

#: bCNC/ProbePage.py:1688
msgid "Camera scale [pixels / unit]"
msgstr ""

#: bCNC/lib/bFileDialog.py:361 bCNC/Utils.py:876
msgid "Cancel"
msgstr "キャンセル"

#: bCNC/lib/bFileDialog.py:582
msgid "Cannot access path \"{}\""
msgstr ""

#: bCNC/plugins/sketch.py:50
msgid "Casual first point"
msgstr ""

#: bCNC/plugins/halftone.py:47
msgid "Cell size"
msgstr ""

#: bCNC/plugins/center.py:12 bCNC/EditorPage.py:463 bCNC/ProbePage.py:646
#: bCNC/ProbePage.py:662
msgid "Center"
msgstr ""

#: bCNC/plugins/simpleArc.py:65
msgid "Center X"
msgstr ""

#: bCNC/plugins/function_plot.py:28
msgid "Center X coordinate"
msgstr ""

#: bCNC/plugins/simpleArc.py:66
msgid "Center Y"
msgstr ""

#: bCNC/plugins/function_plot.py:29
msgid "Center Y coordinate"
msgstr ""

#: bCNC/ProbePage.py:671
msgid "Center probing using a ring"
msgstr ""

#: bCNC/ProbePage.py:1944
msgid "Change"
msgstr ""

#: bCNC/EditorPage.py:732
msgid "Change cut direction to CCW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:715
msgid "Change cut direction to CW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:682
msgid "Change cut direction to climb for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:665
msgid "Change cut direction to conventional for selected gcode blocks"
msgstr ""

#: bCNC/ToolsPage.py:1810
msgid "Change program language restart is required"
msgstr "言語を変更 再起動が必要です"

#: bCNC/CNCCanvas.py:2250
msgid "Change viewing angle"
msgstr ""

#: bCNC/ProbePage.py:2011
msgid "Change:"
msgstr ""

#: bCNC/plugins/halftone.py:43 bCNC/plugins/sketch.py:57
msgid "Channel to analyze"
msgstr ""

#: bCNC/Updates.py:86
msgid "Check Interval"
msgstr "更新頻度"

#: bCNC/Updates.py:135
msgid "Check Now"
msgstr "今すぐ確認"

#: bCNC/FilePage.py:211
msgid "Check Updates"
msgstr "更新を確認"

#: bCNC/TerminalPage.py:157
msgid "Check gcode"
msgstr ""

#: bCNC/Updates.py:142
msgid "Check the web site for new versions of bCNC"
msgstr "WebでbCNCの新バージョンの確認"

#: bCNC/lib/bFileDialog.py:998
msgid "Choose Directory"
msgstr ""

#: bCNC/ProbePage.py:530
msgid "Circle radius"
msgstr ""

#: bCNC/plugins/trochoidal.py:46
msgid "Circular"
msgstr ""

#: bCNC/plugins/gear.py:163
msgid "Circular Pitch"
msgstr ""

#: bCNC/ProbePage.py:224 bCNC/ProbePage.py:794 bCNC/TerminalPage.py:45
msgid "Clear"
msgstr ""

#: bCNC/ControlPage.py:479
msgid "Clear Message"
msgstr ""

#: bCNC/ProbePage.py:230
msgid "Clear probe data"
msgstr ""

#: bCNC/TerminalPage.py:50
msgid "Clear terminal"
msgstr ""

#: bCNC/CNCCanvas.py:440
msgid "Click to set the origin (zero)"
msgstr ""

#: bCNC/EditorPage.py:674
msgid "Climb"
msgstr ""

#: bCNC/plugins/endmilloffset.py:469
msgid "Climb milling"
msgstr ""

#: bCNC/EditorPage.py:40
msgid "Clipboard"
msgstr "クリップボード"

#: bCNC/plugins/trochoidal.py:45 bCNC/plugins/trochoidal_3D.py:92
msgid "Clockwise"
msgstr ""

#: bCNC/EditorPage.py:276 bCNC/ToolsPage.py:1489
msgid "Clone"
msgstr "複製"

#: bCNC/EditorPage.py:282
msgid "Clone selected lines or blocks [Ctrl-D]"
msgstr "選択された直線"

#: bCNC/ToolsPage.py:1495
msgid "Clone selected operation/object"
msgstr "選択された 操作/オブジェクト を複製"

#: bCNC/FilePage.py:267 bCNC/Updates.py:126 bCNC/Utils.py:613
#: bCNC/bmain.py:1085 bCNC/bmain.py:1320 bCNC/bmain.py:2554
msgid "Close"
msgstr "閉じる"

#: bCNC/plugins/text.py:42
msgid "Close Contours"
msgstr ""

#: bCNC/FilePage.py:280
msgid "Close program [Ctrl-Q]"
msgstr "プログラムを終了 [Ctrl-Q]"

#: bCNC/plugins/closepath.py:17
msgid "Close the path"
msgstr ""

#: bCNC/plugins/closepath.py:11
msgid "ClosePath"
msgstr ""

#: bCNC/ToolsPage.py:712
msgid "Coating"
msgstr ""

#: bCNC/CNCList.py:749 bCNC/EditorPage.py:203
msgid "Color"
msgstr "色"

#: bCNC/ToolsPage.py:1934
msgid "Colors"
msgstr "色"

#: bCNC/Utils.py:861 bCNC/bmain.py:215
msgid "Command:"
msgstr "コマンド:"

#: bCNC/TerminalPage.py:61
msgid "Commands"
msgstr ""

#: bCNC/EditorPage.py:370 bCNC/ToolsPage.py:681 bCNC/ToolsPage.py:708
#: bCNC/ToolsPage.py:738
msgid "Comment"
msgstr ""

#: bCNC/ProbePage.py:295
msgid "Common"
msgstr ""

#: bCNC/ToolsPage.py:822
msgid "Compensate islands for cutter radius"
msgstr ""

#: bCNC/ToolsPage.py:1795 bCNC/ToolsPage.py:1821
msgid "Config"
msgstr "設定"

#: bCNC/FilePage.py:348
msgid "Connect on startup"
msgstr "起動時に接続"

#: bCNC/FilePage.py:352
msgid "Connect to serial on startup of the program"
msgstr "プログラムの起動時にシリアルに接続する"

#: bCNC/ControlPage.py:82 bCNC/ControlPage.py:122
msgid "Connection"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:31
msgid "Connection is established with Grbl"
msgstr "Grblとの接続に成功しました"

#: bCNC/ControlPage.py:927 bCNC/ControlPage.py:2364
msgid "Control"
msgstr ""

#: bCNC/ToolsPage.py:591 bCNC/ToolsPage.py:592 bCNC/ToolsPage.py:593
#: bCNC/ToolsPage.py:594 bCNC/ToolsPage.py:595 bCNC/ToolsPage.py:596
#: bCNC/ToolsPage.py:597 bCNC/ToolsPage.py:598 bCNC/ToolsPage.py:599
#: bCNC/ToolsPage.py:600 bCNC/ToolsPage.py:601 bCNC/ToolsPage.py:602
#, fuzzy
msgid "Control-"
msgstr "コントローラー"

#: bCNC/ToolsPage.py:1854
msgid "Controller"
msgstr "コントローラー"

#: bCNC/ToolsPage.py:1862
msgid "Controller (GRBL) configuration"
msgstr "コントローラー (GRBL) 設定"

#: bCNC/bmain.py:202
msgid "Controller buffer fill"
msgstr ""

#: bCNC/FilePage.py:330
msgid "Controller:"
msgstr "コントローラー:"

#: bCNC/EditorPage.py:655
msgid "Conventional"
msgstr ""

#: bCNC/ControlPage.py:2149
msgid "Coolant:"
msgstr ""

#: bCNC/EditorPage.py:80
msgid "Copy"
msgstr "コピー"

#: bCNC/EditorPage.py:86
msgid "Copy [Ctrl-C]"
msgstr "コピー [Ctrl-C]"

#: bCNC/plugins/simpleRectangle.py:96
#, fuzzy
#| msgid "Internal Radius"
msgid "Corner Radius"
msgstr "内径"

#: bCNC/plugins/zigzag.py:110
msgid "Corner resolution"
msgstr "コーナーの解像度(細かさ)"

#: bCNC/plugins/simpleArc.py:89
msgid "Create Simple Arc"
msgstr ""

#: bCNC/plugins/simpleDrill.py:104
msgid "Create Simple Drill"
msgstr ""

#: bCNC/plugins/simpleLine.py:73
msgid "Create Simple Line"
msgstr ""

#: bCNC/plugins/simpleRectangle.py:118
msgid "Create Simple Rectangle"
msgstr ""

#: bCNC/plugins/gear.py:178
msgid "Create Spur GEAR"
msgstr ""

#: bCNC/plugins/hilbert.py:104
msgid "Create a Hilbert path"
msgstr ""

#: bCNC/plugins/zigzag.py:98
msgid "Create a Zig-Zag path"
msgstr "ジグザグ加工を作成"

#: bCNC/plugins/spirograph.py:111
msgid "Create a spirograph path"
msgstr "スピログラフ加工の作成"

#: bCNC/plugins/trochoidPath.py:29
msgid "Create a trochoid rute along selected blocks"
msgstr ""

#: bCNC/plugins/pyrograph.py:29
msgid "Create a variable feed path based upon image brightness"
msgstr ""

#: bCNC/plugins/box.py:393
msgid "Create finger BOX"
msgstr "組みつぎの作成"

#: bCNC/plugins/halftone.py:29
msgid "Create halftone pattern from a picture"
msgstr "写真からモノクロのパターンを作成"

#: bCNC/plugins/driller.py:36
msgid "Create holes along selected blocks"
msgstr ""

#: bCNC/plugins/sketch.py:28
msgid "Create sketch based on picture brightness"
msgstr ""

#: bCNC/ToolsPage.py:1162
msgid "Create tabs on blocks"
msgstr ""

#: bCNC/plugins/text.py:29
msgid "Create text using a ttf font"
msgstr "ttfフォントを使って, テキストを生成する"

#: bCNC/plugins/trochoidal_3D.py:38
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Create trochoids along selected blocks"
msgstr "選択範囲にタイルを貼る"

#: bCNC/ProbePage.py:1692
msgid "Crosshair:"
msgstr ""

#: bCNC/plugins/endmilloffset.py:452
msgid "Custom offset count"
msgstr ""

#: bCNC/plugins/box.py:356 bCNC/EditorPage.py:64 bCNC/ToolsPage.py:1530
msgid "Cut"
msgstr "カット"

#: bCNC/plugins/heightmap.py:75
msgid "Cut Border"
msgstr ""

#: bCNC/EditorPage.py:724
msgid "Cut CCW"
msgstr ""

#: bCNC/EditorPage.py:707
msgid "Cut CW"
msgstr ""

#: bCNC/plugins/trochoidPath.py:38
msgid "Cut Diameter"
msgstr ""

#: bCNC/plugins/flatten.py:298
msgid "Cut Direction"
msgstr ""

#: bCNC/plugins/spiral.py:482
msgid "Cut Pattern"
msgstr ""

#: bCNC/plugins/heightmap.py:74
msgid "Cut Top"
msgstr ""

#: bCNC/EditorPage.py:70
msgid "Cut [Ctrl-X]"
msgstr "切り取り [Ctrl-X]"

#: bCNC/ToolsPage.py:829
msgid "Cut contours of selected islands"
msgstr ""

#: bCNC/ToolsPage.py:1539
msgid "Cut for the full stock thickness selected code"
msgstr "選択された部分を切り取る"

#: bCNC/plugins/spiral.py:483
msgid "Cut in Both Directions"
msgstr ""

#: bCNC/ToolsPage.py:775
msgid "Cutting strategy"
msgstr ""

#: bCNC/ToolsPage.py:987
msgid "DRILL selected points"
msgstr ""

#: bCNC/ToolsPage.py:655
msgid "DRO Zero padding"
msgstr "DRO"

#: bCNC/ToolsPage.py:1397
msgid "Database"
msgstr "データベース"

#: bCNC/lib/bFileDialog.py:307
msgid "Date"
msgstr ""

#: bCNC/Updates.py:101
#, fuzzy
#| msgid "Date of last checking"
msgid "Date last checked"
msgstr "前回の更新日"

#: bCNC/Updates.py:119
msgid "Days-interval to remind again for checking"
msgstr "更新通知の間隔 (日)"

#: bCNC/ToolsPage.py:650
msgid "Decimal digits"
msgstr ""

#: bCNC/ControlPage.py:1176 bCNC/ControlPage.py:1648
msgid "Decrease step by 1 unit"
msgstr ""

#: bCNC/EditorPage.py:292 bCNC/ProbePage.py:750 bCNC/ToolsPage.py:1506
msgid "Delete"
msgstr "削除"

#: bCNC/ProbePage.py:803 bCNC/ProbePage.py:1061
msgid "Delete all markers"
msgstr ""

#: bCNC/ProbePage.py:1497
msgid "Delete autolevel information"
msgstr ""

#: bCNC/ProbePage.py:759
msgid "Delete current marker"
msgstr ""

#: bCNC/EditorPage.py:298
msgid "Delete selected lines or blocks [Del]"
msgstr ""

#: bCNC/ToolsPage.py:1512
msgid "Delete selected operation/object"
msgstr "選択された 操作/オブジェクト を削除"

#: bCNC/plugins/hilbert.py:114 bCNC/plugins/zigzag.py:111
msgid "Depth"
msgstr ""

#: bCNC/plugins/box.py:349
msgid "Depth Dy"
msgstr "奥行き Dy"

#: bCNC/plugins/trochoidPath.py:55 bCNC/plugins/trochoidal_3D.py:89
#: bCNC/ToolsPage.py:684 bCNC/ToolsPage.py:768
msgid "Depth Increment"
msgstr ""

#: bCNC/plugins/spiral.py:478
msgid "Depth to Reduce"
msgstr ""

#: bCNC/plugins/flatten.py:293
msgid "Depth to flatten"
msgstr ""

#: bCNC/plugins/bowl.py:113 bCNC/ToolsPage.py:713
msgid "Diameter"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:83
msgid "Diameter Cut"
msgstr ""

#: bCNC/ProbePage.py:649
msgid "Diameter:"
msgstr ""

#: bCNC/plugins/difference.py:18
msgid "Difference"
msgstr ""

#: bCNC/plugins/jigsaw.py:365
msgid "Difference between pieces"
msgstr ""

#: bCNC/plugins/difference.py:24
msgid "Difference of two shapes"
msgstr ""

#: bCNC/plugins/trochoidPath.py:40 bCNC/plugins/trochoidal_3D.py:60
#: bCNC/plugins/pyrograph.py:46 bCNC/ToolsPage.py:1010
msgid "Direction"
msgstr ""

#: bCNC/bmain.py:1584
msgid "Direction command error"
msgstr ""

#: bCNC/lib/bFileDialog.py:275
msgid "Directory:"
msgstr ""

#: bCNC/EditorPage.py:327
msgid "Disable"
msgstr ""

#: bCNC/ToolsPage.py:942
msgid "Distance (mm)"
msgstr ""

#: bCNC/ControlPage.py:1921
msgid "Distance Mode [G90,G91]"
msgstr ""

#: bCNC/plugins/driller.py:45
msgid "Distance between holes"
msgstr ""

#: bCNC/ControlPage.py:1910 bCNC/ProbePage.py:2103
msgid "Distance:"
msgstr ""

#: bCNC/ControlPage.py:1170 bCNC/ControlPage.py:1642
msgid "Divide step by 10"
msgstr ""

#: bCNC/ProbePage.py:1498
msgid "Do you want to delete all autolevel in formation?"
msgstr ""

#: bCNC/ProbePage.py:1062
msgid "Do you want to delete all orientation markers?"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:162
msgid "Door closed. Ready to resume."
msgstr ""

#: bCNC/ToolsPage.py:639
msgid "Double Size Icon"
msgstr ""

#: bCNC/EditorPage.py:524
msgid "Down"
msgstr ""

#: bCNC/Updates.py:182
msgid "Download"
msgstr "ダウンロード"

#: bCNC/CNCCanvas.py:463
msgid "Drag a ruler to measure distances"
msgstr ""

#: bCNC/plugins/dragknife.py:25
msgid "Drag knife postprocessor"
msgstr ""

#: bCNC/plugins/dragknife.py:19
msgid "DragKnife"
msgstr ""

#: bCNC/plugins/halftone.py:51 bCNC/plugins/sketch.py:49
#: bCNC/plugins/pyrograph.py:47
msgid "Draw border"
msgstr ""

#: bCNC/plugins/function_plot.py:35
msgid "Draw coordinate system?"
msgstr ""

#: bCNC/CNCCanvas.py:2432
msgid "Draw timeout in seconds"
msgstr ""

#: bCNC/CNCCanvas.py:2335
msgid "Draw:"
msgstr ""

#: bCNC/ToolsPage.py:1621
msgid "Drill"
msgstr "ドリル"

#: bCNC/ToolsPage.py:938
msgid "Drill in center only"
msgstr ""

#: bCNC/plugins/drillmark.py:44
msgid "Drill mark size"
msgstr ""

#: bCNC/plugins/driller.py:20
msgid "Driller"
msgstr ""

#: bCNC/plugins/driller.py:276
msgid "Driller abort: Distance must be > 0"
msgstr ""

#: bCNC/plugins/driller.py:285
msgid "Driller abort: Dwell time >= 0, here time runs only forward!"
msgstr ""

#: bCNC/plugins/driller.py:293
msgid "Driller abort: Excellon-File not a file"
msgstr ""

#: bCNC/plugins/driller.py:280
msgid "Driller abort: Peck must be >= 0"
msgstr ""

#: bCNC/plugins/driller.py:303
msgid "Driller abort: Please select some path"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:86
msgid "Drop by lap"
msgstr ""

#: bCNC/ToolsPage.py:941
msgid "Dwell (s)"
msgstr ""

#: bCNC/plugins/simpleDrill.py:71
msgid "Dwell time (s)"
msgstr ""

#: bCNC/plugins/driller.py:48
msgid "Dwell time, 0 means None"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:41
msgid "EEPROM read failed. Reset and restored to default values."
msgstr ""
"EEPROMの読み込みに失敗たので, リセットされ, デフォルトの値が復元されました."

#: bCNC/CNCCanvas.py:525
msgid "ERROR: Cannot set X-Y marker  with the current view"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:150
msgid "EStop asserted. Clear and reset (grblHAL)"
msgstr ""

#: bCNC/ProbePage.py:1560
msgid "Edge Detection"
msgstr ""

#: bCNC/EditorPage.py:194
msgid "Edit"
msgstr "編集"

#: bCNC/ToolsPage.py:1461
msgid "Edit name of current operation/object"
msgstr "現在の 操作/オブジェクト の名称を変更"

#: bCNC/ToolsPage.py:1445
msgid "Editable database of EndMills properties"
msgstr "エンドミルの設定"

#: bCNC/ToolsPage.py:1429
msgid "Editable database of material properties"
msgstr "材料の設定"

#: bCNC/EditorPage.py:804
msgid "Editor"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:119
msgid "Emergency stop active. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2657
msgid "Empty gcode"
msgstr ""

#: bCNC/EditorPage.py:322
msgid "Enable"
msgstr ""

#: bCNC/ToolsPage.py:640
msgid "Enable 6 Axis Displays"
msgstr ""

#: bCNC/EditorPage.py:342
msgid "Enable or disable blocks of gcode"
msgstr ""

#: bCNC/bmain.py:1154
msgid "Enabled GCode"
msgstr ""

#: bCNC/plugins/simpleArc.py:69
msgid "End Angle in Degrees "
msgstr ""

#: bCNC/plugins/Helical_Descent.py:84 bCNC/plugins/endmilloffset.py:438
#: bCNC/plugins/trochoidPath.py:43 bCNC/plugins/trochoidal_3D.py:48
#: bCNC/ToolsPage.py:1003 bCNC/ToolsPage.py:1074 bCNC/ToolsPage.py:1437
msgid "End Mill"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:100
msgid "End in the Deep"
msgstr ""

#: bCNC/bmain.py:1953
msgid "EndMill: {} {}"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:96
msgid "Entry and Exit"
msgstr ""

#: bCNC/lib/bFileDialog.py:582 bCNC/lib/bFileDialog.py:655
#: bCNC/lib/bFileDialog.py:879 bCNC/lib/bFileDialog.py:907
#: bCNC/lib/bFileDialog.py:931
msgid "Error"
msgstr ""

#: bCNC/Utils.py:565
msgid "Error Reporting"
msgstr "エラー報告"

#: bCNC/lib/bFileDialog.py:880
msgid "Error creating folder \"{}\""
msgstr ""

#: bCNC/lib/bFileDialog.py:931
msgid "Error deleting file \"{}\""
msgstr ""

#: bCNC/lib/bFileDialog.py:655
msgid "Error listing folder \"{}\""
msgstr ""

#: bCNC/bmain.py:2568
msgid "Error opening serial"
msgstr ""

#: bCNC/lib/bFileDialog.py:908
msgid "Error renaming \"{}\" to \"{}\""
msgstr ""

#: bCNC/Utils.py:694 bCNC/Utils.py:708
msgid "Error sending report"
msgstr "送信に失敗"

#: bCNC/Updates.py:193
#, fuzzy
#| msgid "Error {} in connection"
msgid "Error {} in connection"
msgstr "接続中に {} エラーが発生しました"

#: bCNC/ProbePage.py:853
msgid "Error:"
msgstr ""

#: bCNC/plugins/bowl.py:136
msgid "Error: Check the Bowl and End Mill parameters"
msgstr "エラー: ボール,エンドミルのパラメーターを確認してください."

#: bCNC/plugins/jigsaw.py:406
msgid "Error: Check the parameters and your endmill config"
msgstr ""

#: bCNC/plugins/midi2cnc.py:155
msgid "Error: Sorry can't parse the Midi file."
msgstr ""

#: bCNC/plugins/midi2cnc.py:116
msgid "Error: This plugin requires midiparser.py"
msgstr ""

#: bCNC/bmain.py:1487
msgid "Evaluation error"
msgstr ""

#: bCNC/plugins/trochoidal.py:47
msgid "Even spacing across segment"
msgstr ""

#: bCNC/ToolsPage.py:1927
msgid "Events"
msgstr "イベント"

#: bCNC/plugins/driller.py:50
msgid "Excellon-File"
msgstr ""

#: bCNC/ToolsPage.py:1967
msgid "Execute"
msgstr "実行"

#: bCNC/bmain.py:2417
msgid "Existing Autolevel"
msgstr ""

#: bCNC/FilePage.py:272
msgid "Exit"
msgstr "終了"

#: bCNC/ToolsPage.py:805
msgid "Exit strategy (usefull for threads)"
msgstr ""

#: bCNC/EditorPage.py:351
msgid "Expand"
msgstr ""

#: bCNC/plugins/spirograph.py:119
msgid "External Radius"
msgstr "外径"

#: bCNC/ToolsPage.py:567 bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:591
msgid "F1"
msgstr ""

#: bCNC/ToolsPage.py:576 bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:600
msgid "F10"
msgstr ""

#: bCNC/ToolsPage.py:577 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:601
msgid "F11"
msgstr ""

#: bCNC/ToolsPage.py:578 bCNC/ToolsPage.py:590 bCNC/ToolsPage.py:602
msgid "F12"
msgstr ""

#: bCNC/ToolsPage.py:568 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:592
msgid "F2"
msgstr ""

#: bCNC/ToolsPage.py:569 bCNC/ToolsPage.py:581 bCNC/ToolsPage.py:593
msgid "F3"
msgstr ""

#: bCNC/ToolsPage.py:570 bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:594
msgid "F4"
msgstr ""

#: bCNC/ToolsPage.py:571 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:595
msgid "F5"
msgstr ""

#: bCNC/ToolsPage.py:572 bCNC/ToolsPage.py:584 bCNC/ToolsPage.py:596
msgid "F6"
msgstr ""

#: bCNC/ToolsPage.py:573 bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:597
msgid "F7"
msgstr ""

#: bCNC/ToolsPage.py:574 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:598
msgid "F8"
msgstr ""

#: bCNC/ToolsPage.py:575 bCNC/ToolsPage.py:587 bCNC/ToolsPage.py:599
msgid "F9"
msgstr ""

#: bCNC/ProbePage.py:485
msgid "FEED"
msgstr ""

#: bCNC/ProbePage.py:518
msgid "FINISH"
msgstr ""

#: bCNC/plugins/sketch.py:47
msgid "Fading force"
msgstr ""

#: bCNC/ProbePage.py:306
msgid "Fast Probe Feed:"
msgstr ""

#: bCNC/plugins/scaling.py:43 bCNC/plugins/trochoidal_3D.py:95
#: bCNC/ToolsPage.py:682 bCNC/ToolsPage.py:769
msgid "Feed"
msgstr ""

#: bCNC/ControlPage.py:2021
msgid "Feed Mode [G93, G94, G95]"
msgstr ""

#: bCNC/ControlPage.py:1999
msgid "Feed Rate [F#]"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:411
msgid "Feed has to be greater than 0"
msgstr ""

#: bCNC/ControlPage.py:487
msgid "Feed hold"
msgstr ""

#: bCNC/ToolsPage.py:644
msgid "Feed max x"
msgstr "x軸 最大送り速度"

#: bCNC/ToolsPage.py:645
msgid "Feed max y"
msgstr "y軸 最大送り速度"

#: bCNC/ToolsPage.py:646
msgid "Feed max z"
msgstr "z軸 最大送り速度"

#: bCNC/plugins/driller.py:56
#, fuzzy
#| msgid "Feed max x"
msgid "Feed rapid G0"
msgstr "x軸 最大送り速度"

#: bCNC/controllers/_GenericGRBL.py:62
msgid "Feed rate has not yet been set or is undefined."
msgstr "送る(移動)速度が定義されていません."

#: bCNC/ControlPage.py:1987
msgid "Feed:"
msgstr ""

#: bCNC/plugins/trochoidal.py:53
msgid "Feedrate"
msgstr ""

#: bCNC/FilePage.py:75 bCNC/FilePage.py:464
msgid "File"
msgstr "ファイル"

#: bCNC/FilePage.py:463
msgid "File I/O and configuration"
msgstr ""

#: bCNC/lib/bFileDialog.py:951 bCNC/lib/bFileDialog.py:962
msgid "File \"{}\" does not exist"
msgstr ""

#: bCNC/lib/bFileDialog.py:984
msgid "File already exists"
msgstr ""

#: bCNC/lib/bFileDialog.py:950 bCNC/lib/bFileDialog.py:961
msgid "File does not exist"
msgstr ""

#: bCNC/bmain.py:2378
msgid "File modified"
msgstr ""

#: bCNC/lib/bFileDialog.py:332
msgid "File name:"
msgstr ""

#: bCNC/lib/bFileDialog.py:340
msgid "Files of type:"
msgstr ""

#: bCNC/EditorPage.py:167
msgid "Filter"
msgstr ""

#: bCNC/EditorPage.py:175
msgid "Filter blocks"
msgstr "ブロックをフィルター"

#: bCNC/plugins/Helical_Descent.py:87
msgid "Final Depth"
msgstr ""

#: bCNC/plugins/center.py:18
msgid "Find center of bounding box"
msgstr ""

#: bCNC/plugins/box.py:351
msgid "Fingers Nx"
msgstr "組みつぎ Nx"

#: bCNC/plugins/box.py:352
msgid "Fingers Ny"
msgstr "組みつぎ Ny"

#: bCNC/plugins/box.py:353
msgid "Fingers Nz"
msgstr "組みつぎ Nz"

#: bCNC/ProbePage.py:1214
msgid "Finished recording"
msgstr ""

#: bCNC/ToolsPage.py:790
msgid "First cut at surface height"
msgstr ""

#: bCNC/CNCCanvas.py:2270
msgid "Fit to screen [F]"
msgstr ""

#: bCNC/plugins/flatpath.py:12
msgid "FlatPath"
msgstr ""

#: bCNC/plugins/flatten.py:13
msgid "Flatten"
msgstr ""

#: bCNC/plugins/flatten.py:43
msgid "Flatten abort: Cut Direction is undefined"
msgstr ""

#: bCNC/plugins/flatten.py:52
msgid "Flatten abort: Flatten Area dimensions must be > 0"
msgstr ""

#: bCNC/plugins/flatten.py:98
msgid "Flatten abort: Flatten area is too small for this End Mill."
msgstr ""

#: bCNC/plugins/flatten.py:58
msgid "Flatten abort: Hey this is only for subtractive machine! Check depth!"
msgstr ""

#: bCNC/plugins/flatten.py:47
msgid "Flatten abort: Pocket Type is undefined"
msgstr ""

#: bCNC/plugins/flatten.py:281
msgid "Flatten an area in different ways"
msgstr ""

#: bCNC/plugins/flatpath.py:18
msgid "Flatten the path"
msgstr ""

#: bCNC/plugins/flatten.py:328
msgid "Flatten: Generated flatten surface"
msgstr ""

#: bCNC/EditorPage.py:581
msgid "Flip"
msgstr ""

#: bCNC/plugins/slicemesh.py:68
msgid "Flip upwards"
msgstr ""

#: bCNC/ControlPage.py:2168
msgid "Flood"
msgstr ""

#: bCNC/ToolsPage.py:715
msgid "Flutes"
msgstr ""

#: bCNC/plugins/text.py:41
msgid "Font file"
msgstr "フォントファイル"

#: bCNC/plugins/text.py:40
msgid "Font size"
msgstr "フォントサイズ"

#: bCNC/ToolsPage.py:1941
msgid "Fonts"
msgstr "フォント"

#: bCNC/ToolsPage.py:657
msgid "Footer gcode"
msgstr ""

#: bCNC/plugins/function_plot.py:24
msgid "Formula"
msgstr ""

#: bCNC/ProbePage.py:1575
msgid "Freeze"
msgstr ""

#: bCNC/plugins/function_plot.py:8
msgid "Function"
msgstr ""

#: bCNC/bmain.py:133 bCNC/bmain.py:2487
msgid "G-Code"
msgstr ""

#: bCNC/plugins/arcfit.py:17
msgid "G-Code arc-fit"
msgstr ""

#: bCNC/bmain.py:134
msgid "G-Code clean"
msgstr ""

#: bCNC/plugins/linearize.py:17
msgid "G-Code linearizer"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:63
msgid "G-code command in block requires an integer value."
msgstr "ブロック内のGコードは十分な値が必要です."

#: bCNC/controllers/_GenericGRBL.py:108
msgid "G-code command not allowed when tool change is pending. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:45
msgid "G-code locked out during alarm or jog state"
msgstr "警報状態や手動操作している間(jog state)は, Gコードはロックされます."

#: bCNC/controllers/_GenericGRBL.py:35
msgid "G-code words consist of a letter and a value. Letter was not found."
msgstr ""
"Gコードは, アルファベットと数字からなるものですが, アルファベットがありませ"
"ん."

#: bCNC/ProbePage.py:59
msgid "G38.2 stop on contact else error"
msgstr ""

#: bCNC/ProbePage.py:60
msgid "G38.3 stop on contact"
msgstr ""

#: bCNC/ProbePage.py:61
msgid "G38.4 stop on loss contact else error"
msgstr ""

#: bCNC/ProbePage.py:62
msgid "G38.5 stop on loss contact"
msgstr ""

#: bCNC/ControlPage.py:2051
msgid "G92:"
msgstr ""

#: bCNC/ProbePage.py:733
msgid "GCode X coordinate of orientation point"
msgstr ""

#: bCNC/ProbePage.py:744
msgid "GCode Y coordinate of orientation point"
msgstr ""

#: bCNC/EditorPage.py:803
msgid "GCode editor"
msgstr ""

#: bCNC/ToolsPage.py:2089
msgid "GCode manipulation tools and user plugins"
msgstr "Gコードの操作ツール ユーザープラグイン"

#: bCNC/bmain.py:2379
msgid "Gcode was modified do you want to save it first?"
msgstr ""

#: bCNC/ProbePage.py:723
msgid "Gcode:"
msgstr ""

#: bCNC/plugins/gear.py:16
msgid "Gear"
msgstr ""

#: bCNC/plugins/bowl.py:105
msgid "Generate a bowl cavity"
msgstr "ボールのくり抜きを作成"

#: bCNC/plugins/box.py:339
msgid "Generate a finger box"
msgstr "組みつぎ箱の作成"

#: bCNC/plugins/endmilloffset.py:430
msgid "Generate a pocket or profile for selected shape (regarding islands)"
msgstr ""

#: bCNC/plugins/simpleArc.py:58
#, fuzzy
#| msgid "Generate a finger box"
msgid "Generate a simple Arc"
msgstr "組みつぎ箱の作成"

#: bCNC/plugins/simpleDrill.py:60
#, fuzzy
#| msgid "Generate a finger box"
msgid "Generate a simple Drill"
msgstr "組みつぎ箱の作成"

#: bCNC/plugins/simpleLine.py:44
#, fuzzy
#| msgid "Generate a finger box"
msgid "Generate a simple line"
msgstr "組みつぎ箱の作成"

#: bCNC/plugins/simpleRectangle.py:85
#, fuzzy
#| msgid "Generate a finger box"
msgid "Generate a simple rectangle"
msgstr "組みつぎ箱の作成"

#: bCNC/plugins/gear.py:153
msgid "Generate a spur gear"
msgstr ""

#: bCNC/plugins/halftone.py:53
msgid "Generate for conical end mill"
msgstr ""

#: bCNC/plugins/endmilloffset.py:175 bCNC/plugins/endmilloffset.py:183
#: bCNC/plugins/endmilloffset.py:191 bCNC/plugins/endmilloffset.py:199
#: bCNC/plugins/endmilloffset.py:207 bCNC/plugins/endmilloffset.py:215
#: bCNC/plugins/endmilloffset.py:223 bCNC/plugins/endmilloffset.py:573
#: bCNC/ToolsPage.py:1088
msgid "Generate pocket path"
msgstr ""

#: bCNC/ToolsPage.py:1062
msgid "Generate profile path"
msgstr ""

#: bCNC/plugins/Random.py:22 bCNC/plugins/tile.py:20
msgid "Generate replicas of selected code"
msgstr ""

#: bCNC/plugins/driller.py:459
msgid "Generated Driller: {} holes"
msgstr ""

#: bCNC/plugins/halftone.py:285
msgid "Generated Halftone size W={} x H={} x D={}, Total points: {}"
msgstr ""

#: bCNC/plugins/heightmap.py:408
msgid "Generated Heightmap {} x {} x {}"
msgstr ""

#: bCNC/plugins/midi2cnc.py:353
msgid "Generated Midi2CNC, ready to play?"
msgstr ""

#: bCNC/plugins/pyrograph.py:213
msgid "Generated Pyrograph W={:g} x H={:g} x D={:g}"
msgstr ""

#: bCNC/plugins/function_plot.py:273
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated function graph"
msgstr "生成済み: スピログラフ"

#: bCNC/plugins/trochoidPath.py:110
msgid "Generated path for trochoidal cutting"
msgstr ""

#: bCNC/plugins/arcfit.py:102
msgid "Generated: Arc fit"
msgstr ""

#: bCNC/plugins/bowl.py:134
msgid "Generated: BOWL"
msgstr "生成済み: ボール"

#: bCNC/plugins/box.py:395
msgid "Generated: BOX with fingers"
msgstr "生成済み: 組みつぎ"

#: bCNC/plugins/center.py:70
msgid "Generated: Center"
msgstr ""

#: bCNC/plugins/closepath.py:52
msgid "Generated: Closepath"
msgstr ""

#: bCNC/plugins/difference.py:102
msgid "Generated: Diff"
msgstr ""

#: bCNC/plugins/dragknife.py:229
msgid "Generated: Dragknife"
msgstr ""

#: bCNC/plugins/flatpath.py:60
msgid "Generated: Flat"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:545
msgid "Generated: Helical_Descent Result"
msgstr ""

#: bCNC/plugins/hilbert.py:146
msgid "Generated: Hilbert"
msgstr ""

#: bCNC/plugins/intersection.py:86
msgid "Generated: Intersect"
msgstr ""

#: bCNC/plugins/linearize.py:97
msgid "Generated: Linearize"
msgstr ""

#: bCNC/plugins/drillmark.py:319
msgid "Generated: Manual drillmark"
msgstr ""

#: bCNC/plugins/simpleArc.py:91
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated: Simple Arc"
msgstr "生成済み: スピログラフ"

#: bCNC/plugins/simpleDrill.py:106
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated: Simple Drill"
msgstr "生成済み: スピログラフ"

#: bCNC/plugins/simpleLine.py:75
#, fuzzy
#| msgid "Generated: BOX with fingers"
msgid "Generated: Simple Line"
msgstr "生成済み: 組みつぎ"

#: bCNC/plugins/simpleRectangle.py:120
#, fuzzy
#| msgid "Generated: Zig-Zag"
msgid "Generated: Simple Rectangle"
msgstr "生成済み: ジグザグ"

#: bCNC/plugins/spirograph.py:145
msgid "Generated: Spirograph"
msgstr "生成済み: スピログラフ"

#: bCNC/plugins/gear.py:180
msgid "Generated: Spur GEAR"
msgstr ""

#: bCNC/plugins/trochoidal.py:177
msgid "Generated: Trochoidal"
msgstr ""

#: bCNC/plugins/zigzag.py:158
msgid "Generated: Zig-Zag"
msgstr "生成済み: ジグザグ"

#: bCNC/plugins/function_plot.py:14
msgid "Generates gcode from a formula"
msgstr ""

#: bCNC/plugins/jigsaw.py:387
msgid "Generating puzzle..."
msgstr ""

#: bCNC/ProbePage.py:1704
msgid "Get"
msgstr ""

#: bCNC/ProbePage.py:2050
msgid "Get current gantry position as machine tool change location"
msgstr ""

#: bCNC/ProbePage.py:2096
msgid "Get current gantry position as machine tool probe location"
msgstr ""

#: bCNC/ProbePage.py:1706
msgid "Get diameter from active endmill"
msgstr ""

#: bCNC/plugins/slicemesh.py:63
msgid "Get flat slice"
msgstr ""

#: bCNC/ProbePage.py:192
msgid "Get margins from gcode file"
msgstr ""

#: bCNC/ProbePage.py:583
msgid "Goto"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:37
msgid "Grbl '$' system command was not recognized or supported."
msgstr ""
"Grblの システムコマンド '$' が, 認識されていないか, サポートされていません."

#: bCNC/controllers/_GenericGRBL.py:21
msgid "Grbl executes jogging motion"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:13
msgid "Grbl is in idle state and waiting for user commands"
msgstr "待機中... ユーザーのコマンドを待っています"

#: bCNC/controllers/_GenericGRBL.py:29
msgid "Grbl is not connected. Please specify the correct port and click Open."
msgstr ""
"Grblが接続されていません. 正しいポートを確認して, \"オープン\" をクリックして"
"ください."

#: bCNC/controllers/_GenericGRBL.py:14
msgid "Grbl is on hold state. Click on resume (pause) to continue"
msgstr "ホールド中... 再開 (一時停止) を押して継続"

#: bCNC/plugins/sketch.py:41
msgid "Grundgy, search radius"
msgstr ""

#: bCNC/ProbePage.py:1659
msgid "Haircross Offset:"
msgstr ""

#: bCNC/ProbePage.py:1667
msgid "Haircross X offset [unit]"
msgstr ""

#: bCNC/ProbePage.py:1676
msgid "Haircross Y offset [unit]"
msgstr ""

#: bCNC/plugins/halftone.py:15
msgid "Halftone"
msgstr "モノクロ"

#: bCNC/plugins/halftone.py:159
msgid "Halftone abort: Angle in V-Cutting end mill is missing"
msgstr ""

#: bCNC/plugins/halftone.py:173
msgid "Halftone abort: Can't read image file"
msgstr ""

#: bCNC/plugins/halftone.py:148
msgid "Halftone abort: Cell size too small"
msgstr ""

#: bCNC/plugins/halftone.py:165
msgid "Halftone abort: Conical path need V-Cutting end mill"
msgstr ""

#: bCNC/plugins/halftone.py:144
msgid "Halftone abort: Maximum diameter too small"
msgstr ""

#: bCNC/plugins/halftone.py:138
msgid "Halftone abort: Minimum diameter must be minor then Maximum"
msgstr ""

#: bCNC/plugins/halftone.py:133
msgid "Halftone abort: Size too small to draw anything!"
msgstr ""

#: bCNC/plugins/halftone.py:109
msgid "Halftone abort: This plugin requires PIL/Pillow to read image data"
msgstr ""

#: bCNC/ControlPage.py:84
msgid "Hard Reset"
msgstr ""

#: bCNC/ToolsPage.py:656
msgid "Header gcode"
msgstr ""

#: bCNC/ToolsPage.py:1105
msgid "Height"
msgstr ""

#: bCNC/plugins/box.py:350
msgid "Height Dz"
msgstr "高さ Dz"

#: bCNC/plugins/flatten.py:292
msgid "Height to flatten"
msgstr ""

#: bCNC/plugins/heightmap.py:40
msgid "Heightmap"
msgstr ""

#: bCNC/plugins/heightmap.py:98
msgid "Heightmap abort: Can't read image file"
msgstr ""

#: bCNC/plugins/heightmap.py:89
msgid "Heightmap abort: This plugin requires PIL/Pillow"
msgstr ""

#: bCNC/plugins/heightmap.py:171
msgid "Heightmap abort: angle not defined for selected End Mill"
msgstr ""

#: bCNC/plugins/heightmap.py:102
msgid "Heightmap abort: depth must be < 0"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:271
msgid "Helical Abort: Drop must be greater than 0"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:286
msgid "Helical Abort: Entry Edge Clearence may be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:261
msgid "Helical Abort: Helix diameter must be greater than the end mill"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:267
msgid "Helical Abort: Helix diameter must be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:257
#, fuzzy
msgid "Helical Abort: Please select helical type"
msgstr "テキストは無視:　フォントファイルを選択してください."

#: bCNC/plugins/Helical_Descent.py:281
#, fuzzy
msgid "Helical Abort: Please selecte Entry and Exit type"
msgstr "テキストは無視:　フォントファイルを選択してください."

#: bCNC/plugins/Helical_Descent.py:275
#, fuzzy
msgid "Helical Abort: Z Feed Multiplier must be greater than 0"
msgstr "ステップパルスは3usecより長い必要があります."

#: bCNC/plugins/trochoidal_3D.py:407
#, fuzzy
msgid "Helical Descent Diameter has to be greater than End mill"
msgstr "ステップパルスは3usecより長い必要があります."

#: bCNC/plugins/Helical_Descent.py:94
msgid "Helical Type"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:320
#, fuzzy
msgid "Helical abort: Please select some path"
msgstr "テキストは無視:　フォントファイルを選択してください."

#: bCNC/TerminalPage.py:175
msgid "Help"
msgstr ""

#: bCNC/Ribbon.py:641
msgid "Help [F1]"
msgstr "ヘルプ [F1]"

#: bCNC/plugins/hilbert.py:15
msgid "Hilbert"
msgstr ""

#: bCNC/plugins/hilbert.py:136
msgid "Hilbert abort: depth must be minor or equal to zero"
msgstr ""

#: bCNC/plugins/hilbert.py:131
msgid "Hilbert abort: verify the size"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:160
msgid "Hold complete. Ready to resume."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:161
msgid "Hold in-progress. Reset will throw an alarm."
msgstr ""

#: bCNC/ControlPage.py:93
msgid "Home"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:116
msgid "Home machine to continue. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:39
msgid "Homing cycle is not enabled via settings."
msgstr "初期位置補正の設定が有効化されていません."

#: bCNC/controllers/_GenericGRBL.py:142
msgid "Homing fail. Reset during active homing cycle."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:143
msgid "Homing fail. Safety door was opened during active homing cycle."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:152
msgid "Homing required. Execute homing command ($H) to continue. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:612
msgid "Horizontal"
msgstr ""

#: bCNC/Utils.py:848
msgid "Icon to appear on button"
msgstr "ボタンのアイコン"

#: bCNC/Utils.py:837
msgid "Icon:"
msgstr "アイコン:"

#: bCNC/plugins/Helical_Descent.py:97
msgid "If Eddge, Edge Clearance"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:253
msgid "If block selected false, please make a value of x"
msgstr ""

#: bCNC/ProbePage.py:67
msgid "Ignore M6 commands"
msgstr ""

#: bCNC/plugins/text.py:44
msgid "Image chars width"
msgstr "画像フォントの幅"

#: bCNC/plugins/halftone.py:50
msgid "Image rotation angle"
msgstr ""

#: bCNC/plugins/text.py:43
msgid "Image to Ascii"
msgstr "画像からASCII"

#: bCNC/plugins/halftone.py:38 bCNC/plugins/sketch.py:52
#: bCNC/plugins/heightmap.py:78 bCNC/plugins/pyrograph.py:48
msgid "Image to process"
msgstr ""

#: bCNC/EditorPage.py:207 bCNC/FilePage.py:126
msgid "Import"
msgstr "インポート"

#: bCNC/bmain.py:2482
msgid "Import Gcode/DXF file"
msgstr ""

#: bCNC/FilePage.py:131
msgid "Import gcode/dxf file"
msgstr ""

#: bCNC/ControlPage.py:1008 bCNC/ControlPage.py:1484
msgid "Increase step by 1 unit"
msgstr ""

#: bCNC/EditorPage.py:742 bCNC/EditorPage.py:765
msgid "Info"
msgstr ""

#: bCNC/ToolsPage.py:1630
msgid "Insert a drill cycle on current objects/location"
msgstr ""

#: bCNC/EditorPage.py:239 bCNC/EditorPage.py:266
msgid "Insert a new block or line of code [Ins or Ctrl-Enter]"
msgstr "新規にブロック, 制御コードを挿入 [Ins or Ctrl-Enter]"

#: bCNC/ToolsPage.py:1646
msgid "Insert holding tabs"
msgstr "固定タブの挿入"

#: bCNC/Updates.py:60
msgid "Installed Version:"
msgstr "インストール済みバージョン:"

#: bCNC/plugins/box.py:347
msgid "Internal Dimensions"
msgstr "内部の寸法"

#: bCNC/plugins/spirograph.py:120
msgid "Internal Radius"
msgstr "内径"

#: bCNC/plugins/intersection.py:18
msgid "Intersection"
msgstr ""

#: bCNC/plugins/intersection.py:24
msgid "Intersection of two shapes"
msgstr ""

#: bCNC/Updates.py:103
msgid "Interval (days):"
msgstr "更新の間隔 (日)"

#: bCNC/ProbePage.py:1412
msgid "Invalid X probing region"
msgstr ""

#: bCNC/ProbePage.py:1421
msgid "Invalid X range [xmin>=xmax]"
msgstr ""

#: bCNC/ProbePage.py:1436
msgid "Invalid Y probing region"
msgstr ""

#: bCNC/ProbePage.py:1445
msgid "Invalid Y range [ymin>=ymax]"
msgstr ""

#: bCNC/ProbePage.py:1457
msgid "Invalid Z probing region"
msgstr ""

#: bCNC/ProbePage.py:1466
msgid "Invalid Z range [zmin>=zmax]"
msgstr ""

#: bCNC/Sender.py:355
msgid "Invalid command {}"
msgstr ""

#: bCNC/ProbePage.py:988
msgid "Invalid diameter entered"
msgstr ""

#: bCNC/bmain.py:1585
msgid "Invalid direction {} specified"
msgstr ""

#: bCNC/ProbePage.py:923 bCNC/ProbePage.py:1475
msgid "Invalid probe feed rate"
msgstr ""

#: bCNC/ProbePage.py:2199
msgid "Invalid tool change position"
msgstr ""

#: bCNC/ProbePage.py:2231
msgid "Invalid tool height or not calibrated"
msgstr ""

#: bCNC/ProbePage.py:2211
msgid "Invalid tool probe location"
msgstr ""

#: bCNC/ProbePage.py:2221 bCNC/ProbePage.py:2241
msgid "Invalid tool scanning distance entered"
msgstr ""

#: bCNC/bmain.py:1978
msgid "Invalid user command {}"
msgstr ""

#: bCNC/plugins/heightmap.py:76 bCNC/EditorPage.py:138 bCNC/EditorPage.py:543
msgid "Invert"
msgstr "反転"

#: bCNC/plugins/halftone.py:45
msgid "Invert Colors"
msgstr "色を反転"

#: bCNC/EditorPage.py:549
msgid "Invert cutting order of selected blocks"
msgstr ""

#: bCNC/EditorPage.py:144
msgid "Invert selection [Ctrl-I]"
msgstr "選択を反転 [Ctrl-I]"

#: bCNC/ToolsPage.py:1655
msgid "Island"
msgstr ""

#: bCNC/plugins/endmilloffset.py:494
msgid "Island behaviour"
msgstr ""

#: bCNC/plugins/jigsaw.py:403
msgid "Jigsaw puzzle generated in {}s"
msgstr ""

#: bCNC/plugins/jigsaw.py:353
msgid "Jigsaw puzzle generator"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:58
msgid "Jog command with no '=' or contains prohibited g-code."
msgstr "'=' のない手動操作コマンドであるか, 禁止されたGコードです"

#: bCNC/controllers/_GenericGRBL.py:57
msgid "Jog target exceeds machine travel. Command ignored."
msgstr "手動操作は, 装置の可動範囲を超えたため, コマンドは無視されました."

#: bCNC/EditorPage.py:386
msgid "Join"
msgstr ""

#: bCNC/EditorPage.py:392
msgid "Join selected blocks"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:29
msgid "Keep original Yes/No"
msgstr ""

#: bCNC/plugins/simpleRotate.py:30
msgid "Keep original yes/no"
msgstr ""

#: bCNC/ToolsPage.py:1911
msgid "Language change"
msgstr "言語を変更"

#: bCNC/ToolsPage.py:638
msgid "Laser Adaptive Power"
msgstr ""

#: bCNC/ToolsPage.py:637
#, fuzzy
msgid "Laser Cutter"
msgstr "レーザーカッター"

#: bCNC/controllers/_GenericGRBL.py:59
msgid "Laser mode requires PWM output."
msgstr ""

#: bCNC/plugins/driller.py:70
msgid "Laser power maximum"
msgstr ""

#: bCNC/plugins/driller.py:63
msgid "Laser power minimum"
msgstr ""

#: bCNC/Updates.py:89
msgid "Last Check:"
msgstr "前回の更新"

#: bCNC/ControlPage.py:603 bCNC/ControlPage.py:910
msgid "Last error: {}\n"
msgstr ""

#: bCNC/Updates.py:67
msgid "Latest Github Version:"
msgstr "Githubの最新バージョン"

#: bCNC/Updates.py:73
#, fuzzy
#| msgid "Latest release version on on github"
msgid "Latest release version on github"
msgstr "Githubの最新のリリースバージョン"

#: bCNC/EditorPage.py:154
msgid "Layer"
msgstr "レイヤー"

#: bCNC/ToolsPage.py:811
msgid "Leave islands uncut"
msgstr ""

#: bCNC/EditorPage.py:460
msgid "Left"
msgstr ""

#: bCNC/ToolsPage.py:716
msgid "Length"
msgstr ""

#: bCNC/plugins/spiral.py:477
msgid "Length of Stock to Reduce"
msgstr ""

#: bCNC/bmain.py:1204 bCNC/bmain.py:1290
msgid "Length:"
msgstr ""

#: bCNC/plugins/spiral.py:484
msgid "Lift before rotate"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:154
msgid "Limit switch engaged. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:245
msgid "Line"
msgstr ""

#: bCNC/plugins/zigzag.py:107
msgid "Line length"
msgstr "直線の長さ"

#: bCNC/plugins/linearize.py:11
msgid "Linearize"
msgstr ""

#: bCNC/plugins/endmilloffset.py:503
msgid "Link segments"
msgstr ""

#: bCNC/plugins/slicemesh.py:136
msgid "Loading mesh: {}"
msgstr ""

#: bCNC/bmain.py:2424
msgid "Loading: {} ..."
msgstr ""

#: bCNC/ProbePage.py:1635
msgid "Location:"
msgstr ""

#: bCNC/plugins/driller.py:51
msgid "M3 for laser (settings below)"
msgstr ""

#: bCNC/ControlPage.py:314 bCNC/ControlPage.py:693
msgid "MPos:"
msgstr ""

#: bCNC/ProbePage.py:2002
msgid "MX"
msgstr ""

#: bCNC/ProbePage.py:2004
msgid "MY"
msgstr ""

#: bCNC/ProbePage.py:2006
msgid "MZ"
msgstr ""

#: bCNC/ProbePage.py:775
msgid "Machine X coordinate of orientation point"
msgstr ""

#: bCNC/ProbePage.py:787
msgid "Machine Y coordinate of orientation point"
msgstr ""

#: bCNC/ToolsPage.py:1829
msgid "Machine configuration for bCNC"
msgstr "bCNC用の装置設定"

#: bCNC/controllers/_GenericGRBL.py:163
msgid "Machine stopped. Door still ajar. Can't resume until closed."
msgstr ""

#: bCNC/ProbePage.py:1962
msgid "Manual Tool Change"
msgstr ""

#: bCNC/ProbePage.py:70
msgid "Manual Tool Change (NoProbe)"
msgstr ""

#: bCNC/ProbePage.py:69
msgid "Manual Tool Change (TLO)"
msgstr ""

#: bCNC/ProbePage.py:68
msgid "Manual Tool Change (WCS)"
msgstr ""

#: bCNC/plugins/drillmark.py:317
msgid "Manual drill mark"
msgstr ""

#: bCNC/ProbePage.py:2018
msgid "Manual tool change Machine X location"
msgstr ""

#: bCNC/ProbePage.py:2029
msgid "Manual tool change Machine Y location"
msgstr ""

#: bCNC/ProbePage.py:2040
msgid "Manual tool change Machine Z location"
msgstr ""

#: bCNC/ProbePage.py:2064
msgid "Manual tool change Probing MX location"
msgstr ""

#: bCNC/ProbePage.py:2075
msgid "Manual tool change Probing MY location"
msgstr ""

#: bCNC/ProbePage.py:2086
msgid "Manual tool change Probing MZ location"
msgstr ""

#: bCNC/ProbePage.py:186
msgid "Margins"
msgstr ""

#: bCNC/bmain.py:1159 bCNC/bmain.py:1240
msgid "Margins X:"
msgstr ""

#: bCNC/plugins/drillmark.py:46
msgid "Mark X center"
msgstr ""

#: bCNC/plugins/drillmark.py:47
msgid "Mark Y center"
msgstr ""

#: bCNC/ProbePage.py:1752
msgid "Mark camera position for calculating offset"
msgstr ""

#: bCNC/plugins/drillmark.py:50
msgid "Mark drawing power"
msgstr ""

#: bCNC/ProbePage.py:1745
msgid "Mark spindle position for calculating offset"
msgstr ""

#: bCNC/ProbePage.py:683
msgid "Markers:"
msgstr ""

#: bCNC/ToolsPage.py:711 bCNC/ToolsPage.py:739 bCNC/ToolsPage.py:1421
msgid "Material"
msgstr ""

#: bCNC/ProbePage.py:1232
msgid "Max"
msgstr ""

#: bCNC/plugins/spiral.py:479
msgid "Max Depth per Pass"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:48
msgid "Max characters per line exceeded. Line was not processed and executed."
msgstr ""
"一行あたりの文字数制限を超えています. この行は処理されず, 実行されません."

#: bCNC/plugins/halftone.py:48
msgid "Max diameter, cap limit"
msgstr ""

#: bCNC/plugins/halftone.py:46
msgid "Max draw size (Width or Height)"
msgstr "最大サイズ(幅 または 高さ)"

#: bCNC/controllers/_GenericGRBL.py:113
msgid "Max. feed rate exceeded. (grblHAL)"
msgstr ""

#: bCNC/plugins/midi2cnc.py:64
msgid "Maximum X travel"
msgstr ""

#: bCNC/plugins/midi2cnc.py:65
msgid "Maximum Y travel"
msgstr ""

#: bCNC/plugins/midi2cnc.py:66
msgid "Maximum Z travel"
msgstr ""

#: bCNC/plugins/pyrograph.py:42
msgid "Maximum feed"
msgstr ""

#: bCNC/plugins/sketch.py:48
msgid "Maximum light"
msgstr ""

#: bCNC/plugins/sketch.py:44 bCNC/plugins/heightmap.py:66
#: bCNC/plugins/pyrograph.py:40
msgid "Maximum size"
msgstr ""

#: bCNC/plugins/slicemesh.py:194
msgid "Mesh sliced"
msgstr ""

#: bCNC/plugins/midi2cnc.py:71
msgid "Midi to process"
msgstr ""

#: bCNC/ProbePage.py:1230
msgid "Min"
msgstr ""

#: bCNC/plugins/halftone.py:49
msgid "Min diameter, cut off"
msgstr ""

#: bCNC/ToolsPage.py:1102
msgid "Min. Distance of tabs"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:420
#, fuzzy
#| msgid "Minimum step pulse time must be greater than 3usec"
msgid "Minimum Adaptative Feed has to be greater than 0"
msgstr "ステップパルスは3usecより長い必要があります."

#: bCNC/plugins/pyrograph.py:41
msgid "Minimum feed"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:40
msgid "Minimum step pulse time must be greater than 3usec"
msgstr "ステップパルスは3usecより長い必要があります."

#: bCNC/EditorPage.py:619
msgid "Mirror horizontally X=-X selected gcode"
msgstr ""

#: bCNC/EditorPage.py:634
msgid "Mirror vertically Y=-Y selected gcode"
msgstr ""

#: bCNC/ControlPage.py:2182
msgid "Mist"
msgstr ""

#: bCNC/ControlPage.py:2009
msgid "Mode:"
msgstr ""

#: bCNC/ProbePage.py:261
msgid "Modify selected G-Code to match autolevel"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:61
msgid "More than one g-code command from same modal group found in block."
msgstr "一つの文法ブロックの中に, 2つ以上のGコードがあります."

#: bCNC/ToolsPage.py:714
msgid "Mount Axis"
msgstr ""

#: bCNC/EditorPage.py:416 bCNC/EditorPage.py:424
msgid "Move"
msgstr ""

#: bCNC/ControlPage.py:1428
msgid "Move +A"
msgstr ""

#: bCNC/ControlPage.py:1530
msgid "Move +B"
msgstr ""

#: bCNC/ControlPage.py:1468
msgid "Move +B +C"
msgstr ""

#: bCNC/ControlPage.py:1635
msgid "Move +B -C"
msgstr ""

#: bCNC/ControlPage.py:1455
msgid "Move +C"
msgstr ""

#: bCNC/ControlPage.py:1060
msgid "Move +X"
msgstr ""

#: bCNC/ControlPage.py:992
msgid "Move +X +Y"
msgstr ""

#: bCNC/ControlPage.py:1163
msgid "Move +X -Y"
msgstr ""

#: bCNC/ControlPage.py:980
msgid "Move +Y"
msgstr ""

#: bCNC/ControlPage.py:954
msgid "Move +Z"
msgstr ""

#: bCNC/ControlPage.py:1596
msgid "Move -A"
msgstr ""

#: bCNC/ControlPage.py:1504
msgid "Move -B"
msgstr ""

#: bCNC/ControlPage.py:1442
msgid "Move -B +C"
msgstr ""

#: bCNC/ControlPage.py:1609
msgid "Move -B -C"
msgstr ""

#: bCNC/ControlPage.py:1622
msgid "Move -C"
msgstr ""

#: bCNC/ControlPage.py:1028
msgid "Move -X"
msgstr ""

#: bCNC/ControlPage.py:967
msgid "Move -X +Y"
msgstr ""

#: bCNC/ControlPage.py:1137
msgid "Move -X -Y"
msgstr ""

#: bCNC/ControlPage.py:1150
msgid "Move -Y"
msgstr ""

#: bCNC/ControlPage.py:1124
msgid "Move -Z"
msgstr ""

#: bCNC/CNCCanvas.py:451
msgid "Move CNC gantry to mouse location"
msgstr ""

#: bCNC/ControlPage.py:451
msgid "Move Gantry"
msgstr ""

#: bCNC/EditorPage.py:451
msgid "Move all gcode such as origin is on mouse location [O]"
msgstr ""

#: bCNC/CNCCanvas.py:760
msgid "Move by {:g}, {:g}, {:g}"
msgstr ""

#: bCNC/ControlPage.py:461
msgid "Move gantry to mouse location [g]"
msgstr ""

#: bCNC/CNCCanvas.py:445
msgid "Move graphically objects"
msgstr ""

#: bCNC/EditorPage.py:433
msgid "Move objects [M]"
msgstr ""

#: bCNC/EditorPage.py:532
msgid "Move selected g-code down [Ctrl-Down, Ctrl-PgDn]"
msgstr ""

#: bCNC/EditorPage.py:513
msgid "Move selected g-code up [Ctrl-Up, Ctrl-PgUp]"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Moved selected blocks"
msgstr "選択範囲にタイルを貼る"

#: bCNC/ControlPage.py:1001 bCNC/ControlPage.py:1477
msgid "Multiply step by 10"
msgstr ""

#: bCNC/ProbePage.py:1236
msgid "N"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:72
msgid "N line number value is not within the valid range of 1 - 9,999,999."
msgstr "N lineの値が有効な範囲1 - 9,999,999に収まっていません."

#: bCNC/lib/bFileDialog.py:304 bCNC/plugins/Helical_Descent.py:77
#: bCNC/plugins/Random.py:29 bCNC/plugins/arcfit.py:32 bCNC/plugins/bowl.py:112
#: bCNC/plugins/box.py:346 bCNC/plugins/center.py:35
#: bCNC/plugins/difference.py:40 bCNC/plugins/dragknife.py:41
#: bCNC/plugins/driller.py:44 bCNC/plugins/drillmark.py:38
#: bCNC/plugins/endmilloffset.py:437 bCNC/plugins/flatten.py:288
#: bCNC/plugins/function_plot.py:23 bCNC/plugins/gear.py:160
#: bCNC/plugins/halftone.py:37 bCNC/plugins/hilbert.py:111
#: bCNC/plugins/intersection.py:41 bCNC/plugins/jigsaw.py:360
#: bCNC/plugins/linearize.py:33 bCNC/plugins/midi2cnc.py:60
#: bCNC/plugins/scaling.py:38 bCNC/plugins/sketch.py:36
#: bCNC/plugins/spiral.py:471 bCNC/plugins/spirograph.py:118
#: bCNC/plugins/text.py:37 bCNC/plugins/tile.py:27
#: bCNC/plugins/trochoidPath.py:37 bCNC/plugins/trochoidal.py:43
#: bCNC/plugins/trochoidal_3D.py:46 bCNC/plugins/zigzag.py:105
#: bCNC/plugins/heightmap.py:64 bCNC/plugins/pyrograph.py:37
#: bCNC/plugins/slicemesh.py:55 bCNC/ToolsPage.py:680 bCNC/ToolsPage.py:707
#: bCNC/ToolsPage.py:737 bCNC/ToolsPage.py:765 bCNC/ToolsPage.py:937
#: bCNC/ToolsPage.py:998 bCNC/ToolsPage.py:1073 bCNC/ToolsPage.py:1099
#: bCNC/ToolsPage.py:1981
msgid "Name"
msgstr ""

#: bCNC/Utils.py:833
msgid "Name to appear on button"
msgstr "ボタンの名称"

#: bCNC/Utils.py:829
msgid "Name:"
msgstr "名称: "

#: bCNC/controllers/_GenericGRBL.py:38
msgid "Negative value received for an expected positive value."
msgstr "正の数であるべきところに, 負の数が入っています."

#: bCNC/FilePage.py:85
msgid "New"
msgstr "新規作成"

#: bCNC/Ribbon.py:574
msgid "New file"
msgstr "新規ファイル"

#: bCNC/FilePage.py:90
msgid "New gcode/dxf file"
msgstr "新規作成 gcode/dxf"

#: bCNC/lib/bFileDialog.py:863
msgid "NewFolder"
msgstr ""

#: bCNC/plugins/Random.py:45 bCNC/plugins/simpleRotate.py:49
#: bCNC/plugins/simpleTranslate.py:47 bCNC/plugins/tile.py:44
msgid "No g-code blocks selected"
msgstr "Gコードのブロックが選択されていません"

#: bCNC/plugins/gear.py:161
msgid "No of teeth"
msgstr ""

#: bCNC/EditorPage.py:122
msgid "None"
msgstr ""

#: bCNC/bmain.py:2658
msgid "Not gcode file was loaded"
msgstr ""

#: bCNC/bmain.py:2035
msgid "Nothing to do"
msgstr ""

#: bCNC/ToolsPage.py:943
msgid "Number"
msgstr ""

#: bCNC/plugins/trochoidPath.py:60
msgid "Number of Tabs 0 = Not Tabs"
msgstr ""

#: bCNC/plugins/zigzag.py:106
msgid "Number of lines"
msgstr "直線の数"

#: bCNC/ToolsPage.py:1101
msgid "Number of tabs"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:36
msgid "Numeric value format is not valid or missing an expected value."
msgstr "数値のフォーマットが正しくないか, 想定される数値がありません."

#: bCNC/ControlPage.py:2154
msgid "OFF"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "ONLY before probing"
msgstr ""

#: bCNC/plugins/endmilloffset.py:14
#, fuzzy
#| msgid "Offset radius"
msgid "Offset"
msgstr "半径のオフセット"

#: bCNC/plugins/spirograph.py:121
msgid "Offset radius"
msgstr "半径のオフセット"

#: bCNC/plugins/endmilloffset.py:462
#, fuzzy
#| msgid "Offset radius"
msgid "Offset side"
msgstr "半径のオフセット"

#: bCNC/ProbePage.py:836 bCNC/ProbePage.py:1710
msgid "Offset:"
msgstr ""

#: bCNC/Utils.py:877
msgid "Ok"
msgstr "決定"

#: bCNC/controllers/_GenericGRBL.py:115
msgid "Only homing is allowed when a limit switch is engaged. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:816
msgid "Only leave selected islands uncut"
msgstr ""

#: bCNC/lib/bFileDialog.py:337 bCNC/lib/bFileDialog.py:938
#: bCNC/lib/bFileDialog.py:1063 bCNC/FilePage.py:110 bCNC/FilePage.py:376
#: bCNC/bmain.py:2546
msgid "Open"
msgstr "開く"

#: bCNC/FilePage.py:103
msgid "Open existing gcode/dxf file [Ctrl-O]"
msgstr "既存の gcode/dxfファイルを開く [Ctrl-O]"

#: bCNC/bmain.py:2345
msgid "Open file"
msgstr ""

#: bCNC/Ribbon.py:584
msgid "Open file [Ctrl-O]"
msgstr "ファイルを開く [Ctrl-O]"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
msgid "Open paths"
msgstr ""

#: bCNC/FilePage.py:116
msgid "Open recent file"
msgstr "履歴から開く"

#: bCNC/Updates.py:186
msgid "Open web browser to download bCNC"
msgstr "ブラウザを開いてbCNCをダウンロードする"

#: bCNC/ControlPage.py:129
msgid "Open/Close connection"
msgstr ""

#: bCNC/FilePage.py:384
msgid "Open/Close serial port"
msgstr ""

#: bCNC/plugins/endmilloffset.py:443
#, fuzzy
#| msgid "Options"
msgid "Operation"
msgstr "設定"

#: bCNC/bmain.py:2083
msgid "Operation error"
msgstr ""

#: bCNC/bmain.py:2036
msgid "Operation {} requires some gcode to be selected"
msgstr ""

#: bCNC/EditorPage.py:490 bCNC/bmain.py:1780
msgid "Optimize"
msgstr ""

#: bCNC/FilePage.py:168
msgid "Options"
msgstr "設定"

#: bCNC/plugins/hilbert.py:113 bCNC/EditorPage.py:486
msgid "Order"
msgstr ""

#: bCNC/ProbePage.py:677 bCNC/ProbePage.py:822 bCNC/bmain.py:138
msgid "Orient"
msgstr ""

#: bCNC/EditorPage.py:441
msgid "Origin"
msgstr ""

#: bCNC/plugins/box.py:355 bCNC/plugins/trochoidPath.py:52
#: bCNC/plugins/trochoidal_3D.py:76 bCNC/ToolsPage.py:1018
msgid "Overcut"
msgstr ""

#: bCNC/plugins/endmilloffset.py:485
msgid "Overcut corners"
msgstr ""

#: bCNC/lib/bFileDialog.py:985
msgid "Overwrite existing file {}?"
msgstr ""

#: bCNC/ProbePage.py:496
msgid "POINT"
msgstr ""

#: bCNC/CNCCanvas.py:435
msgid "Pan viewport"
msgstr ""

#: bCNC/CNCCanvas.py:2300
msgid "Pan viewport [X]"
msgstr ""

#: bCNC/TerminalPage.py:96
msgid "Parameters"
msgstr ""

#: bCNC/EditorPage.py:49
msgid "Paste"
msgstr "貼り付け"

#: bCNC/EditorPage.py:55
msgid "Paste [Ctrl-V]"
msgstr "貼り付け [ctrl-V]"

#: bCNC/ControlPage.py:192
msgid "Pause"
msgstr ""

#: bCNC/ControlPage.py:214
msgid "Pause running program and soft reset controller to empty the buffer."
msgstr ""

#: bCNC/ControlPage.py:199
msgid "Pause running program. Sends either FEED_HOLD ! or CYCLE_START ~"
msgstr ""

#: bCNC/ProbePage.py:1985
msgid "Pause:"
msgstr ""

#: bCNC/ToolsPage.py:940
msgid "Peck depth"
msgstr ""

#: bCNC/plugins/simpleDrill.py:70
msgid "Peck depth (positive)"
msgstr ""

#: bCNC/plugins/driller.py:47
msgid "Peck, 0 means None"
msgstr ""

#: bCNC/FilePage.py:232 bCNC/bmain.py:2713 bCNC/bmain.py:2719
#: bCNC/bmain.py:2733
msgid "Pendant"
msgstr ""

#: bCNC/bmain.py:2720
msgid "Pendant already started:\n"
msgstr ""

#: bCNC/bmain.py:2714
msgid "Pendant started:\n"
msgstr ""

#: bCNC/bmain.py:2734
msgid "Pendant stopped"
msgstr ""

#: bCNC/ProbePage.py:2135
msgid "Perform a calibration probing to determine the height"
msgstr ""

#: bCNC/ControlPage.py:100
msgid "Perform a homing cycle [$H] now"
msgstr ""

#: bCNC/ToolsPage.py:1613
msgid "Perform a pocket operation on selected code"
msgstr "選択された部分にポケット加工"

#: bCNC/ToolsPage.py:1595
msgid "Perform a profile operation on selected code"
msgstr "選択された部分にポケット加工"

#: bCNC/ProbePage.py:1935
msgid "Perform a single a tool change cycle to set the calibration field"
msgstr ""

#: bCNC/ProbePage.py:636
msgid "Perform a single probe cycle"
msgstr ""

#: bCNC/ProbePage.py:1951
msgid "Perform a tool change cycle"
msgstr ""

#: bCNC/plugins/jigsaw.py:363
msgid "Piece count"
msgstr ""

#: bCNC/ControlPage.py:1978
msgid "Plane [G17,G18,G19]"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:112
msgid "Plane must be ZX for threading. (grblHAL)"
msgstr ""

#: bCNC/ControlPage.py:1967
msgid "Plane:"
msgstr ""

#: bCNC/ToolsPage.py:1912
msgid "Please restart the program."
msgstr "ソフトを再起動してください"

#: bCNC/bmain.py:1781
msgid "Please select the blocks of gcode you want to optimize."
msgstr ""

#: bCNC/bmain.py:2609
msgid "Please stop before"
msgstr ""

#: bCNC/ToolsPage.py:651
msgid "Plotting Arc accuracy"
msgstr ""

#: bCNC/plugins/scaling.py:44 bCNC/plugins/trochoidal_3D.py:96
#: bCNC/ToolsPage.py:683 bCNC/ToolsPage.py:770
msgid "Plunge Feed"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:415
msgid "Plunge Feed has to be greater than 0"
msgstr ""

#: bCNC/ToolsPage.py:1025 bCNC/ToolsPage.py:1604
msgid "Pocket"
msgstr "ポケット"

#: bCNC/plugins/flatten.py:299
msgid "Pocket type"
msgstr ""

#: bCNC/ProbePage.py:1164
msgid "Pointrec"
msgstr ""

#: bCNC/ProbePage.py:1967
msgid "Policy:"
msgstr ""

#: bCNC/FilePage.py:294
msgid "Port:"
msgstr "ポート:"

#: bCNC/ProbePage.py:596
msgid "Pos:"
msgstr ""

#: bCNC/EditorPage.py:209
msgid "Postprocess Inkscape g-code"
msgstr ""

#: bCNC/plugins/gear.py:162
msgid "Pressure angle"
msgstr ""

#: bCNC/ProbePage.py:106 bCNC/ProbePage.py:114 bCNC/ProbePage.py:539
#: bCNC/ProbePage.py:627 bCNC/bmain.py:137
msgid "Probe"
msgstr ""

#: bCNC/ProbePage.py:987
msgid "Probe Center Error"
msgstr ""

#: bCNC/ProbePage.py:369
msgid "Probe Command"
msgstr ""

#: bCNC/ProbePage.py:922 bCNC/ProbePage.py:955 bCNC/ProbePage.py:1411
#: bCNC/ProbePage.py:1420 bCNC/ProbePage.py:1435 bCNC/ProbePage.py:1444
#: bCNC/ProbePage.py:1456 bCNC/ProbePage.py:1465 bCNC/ProbePage.py:1474
msgid "Probe Error"
msgstr ""

#: bCNC/ProbePage.py:328
msgid "Probe Feed:"
msgstr ""

#: bCNC/bmain.py:2390
msgid "Probe File modified"
msgstr ""

#: bCNC/ProbePage.py:2198 bCNC/ProbePage.py:2210 bCNC/ProbePage.py:2220
#: bCNC/ProbePage.py:2230 bCNC/ProbePage.py:2240
msgid "Probe Tool Change Error"
msgstr ""

#: bCNC/ProbePage.py:603
msgid "Probe along X direction"
msgstr ""

#: bCNC/ProbePage.py:611
msgid "Probe along Y direction"
msgstr ""

#: bCNC/ProbePage.py:619
msgid "Probe along Z direction"
msgstr ""

#: bCNC/ProbePage.py:2366
msgid "Probe configuration and probing"
msgstr ""

#: bCNC/ProbePage.py:902
msgid "Probe connected?"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:155
msgid "Probe protection triggered. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2391
msgid "Probe was modified do you want to save it first?"
msgstr ""

#: bCNC/ProbePage.py:543 bCNC/ProbePage.py:2057
msgid "Probe:"
msgstr ""

#: bCNC/ProbePage.py:655
msgid "Probing ring internal diameter"
msgstr ""

#: bCNC/plugins/box.py:354 bCNC/ToolsPage.py:1586
msgid "Profile"
msgstr "プロファイル"

#: bCNC/bmain.py:2129 bCNC/bmain.py:2231
msgid "Profile block distance={:g}"
msgstr ""

#: bCNC/plugins/bowl.py:115
msgid "Progressive"
msgstr ""

#: bCNC/Updates.py:74
msgid "Published at:"
msgstr "公開場所:"

#: bCNC/Updates.py:80
#, fuzzy
msgid "Published date of the latest github release"
msgstr "最新のリリース公開日"

#: bCNC/plugins/midi2cnc.py:61
msgid "Pulse per unit for X"
msgstr ""

#: bCNC/plugins/midi2cnc.py:62
msgid "Pulse per unit for Y"
msgstr ""

#: bCNC/plugins/midi2cnc.py:63
msgid "Pulse per unit for Z"
msgstr ""

#: bCNC/plugins/pyrograph.py:13
msgid "Pyrograph"
msgstr ""

#: bCNC/plugins/pyrograph.py:97
msgid "Pyrograph abort: Can't read image file"
msgstr ""

#: bCNC/plugins/pyrograph.py:85
msgid "Pyrograph abort: Please check feed rate parameters"
msgstr ""

#: bCNC/plugins/pyrograph.py:58
msgid "Pyrograph abort: This plugin requires PIL/Pillow"
msgstr ""

#: bCNC/plugins/pyrograph.py:80
msgid "Pyrograph abort: Tool Size must be > 0"
msgstr ""

#: bCNC/plugins/pyrograph.py:76
msgid "Pyrograph abort: please define a scan Direction"
msgstr ""

#: bCNC/plugins/pyrograph.py:38
msgid "Pyrograph tip size"
msgstr ""

#: bCNC/ProbePage.py:474
msgid "RAPID"
msgstr ""

#: bCNC/plugins/scaling.py:45 bCNC/plugins/trochoidal_3D.py:94
msgid "RPM"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:114
msgid "RPM out of range. (grblHAL)"
msgstr ""

#: bCNC/plugins/trochoidal.py:51
msgid "Radial depth of cut (<= cutter D * 0.4)"
msgstr ""

#: bCNC/plugins/simpleArc.py:67
msgid "Radius"
msgstr ""

#: bCNC/ToolsPage.py:781
msgid "Ramp length"
msgstr ""

#: bCNC/plugins/Random.py:15
msgid "Random"
msgstr ""

#: bCNC/plugins/jigsaw.py:364
msgid "Random seed"
msgstr ""

#: bCNC/plugins/function_plot.py:26
msgid "Range of X"
msgstr ""

#: bCNC/plugins/function_plot.py:27
msgid "Range of Y"
msgstr ""

#: bCNC/ProbePage.py:592
msgid "Rapid goto to last probe location"
msgstr ""

#: bCNC/bmain.py:1213
msgid "Rapid:"
msgstr ""

#: bCNC/plugins/flatten.py:294
msgid "Raster border"
msgstr ""

#: bCNC/ProbePage.py:452
msgid "Record"
msgstr ""

#: bCNC/ProbePage.py:468
msgid "Record Z coordinate?"
msgstr ""

#: bCNC/Ribbon.py:624
msgid "Redo [Ctrl-Y]"
msgstr "進む [Ctrl-Y]"

#: bCNC/CNCCanvas.py:2425
msgid "Redraw display [Ctrl-R]"
msgstr ""

#: bCNC/plugins/spiral.py:464
msgid "Reduce Diameter of 4th Axis Stock"
msgstr ""

#: bCNC/FilePage.py:361
msgid "Refresh"
msgstr ""

#: bCNC/FilePage.py:367
msgid "Refresh list of serial ports"
msgstr ""

#: bCNC/ProbePage.py:1740
msgid "Register:"
msgstr ""

#: bCNC/ToolsPage.py:1455
msgid "Rename"
msgstr "リネーム"

#: bCNC/plugins/sketch.py:51
msgid "Repetition of a point"
msgstr ""

#: bCNC/FilePage.py:189 bCNC/Utils.py:568
msgid "Report"
msgstr "報告"

#: bCNC/Utils.py:701
msgid "Report successfully send"
msgstr "エラー報告の送信に成功しました"

#: bCNC/Utils.py:702
msgid "Report was successfully uploaded to web site"
msgstr "報告はウェブサイトに送信されました"

#: bCNC/ControlPage.py:136 bCNC/ControlPage.py:2080
msgid "Reset"
msgstr ""

#: bCNC/ControlPage.py:2082
msgid "Reset override to 100%"
msgstr ""

#: bCNC/plugins/function_plot.py:25
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "Resolution"
msgstr "解像度 (角度)"

#: bCNC/plugins/bowl.py:114
msgid "Resolution (degrees)"
msgstr "解像度 (角度)"

#: bCNC/TerminalPage.py:67
msgid "Restore All"
msgstr ""

#: bCNC/TerminalPage.py:64
msgid "Restore Settings"
msgstr ""

#: bCNC/TerminalPage.py:66
msgid "Restore Workspace"
msgstr ""

#: bCNC/ControlPage.py:493
msgid "Resume"
msgstr ""

#: bCNC/ControlPage.py:1517
msgid "Return ABC to 0."
msgstr ""

#: bCNC/EditorPage.py:691
msgid "Reverse"
msgstr ""

#: bCNC/EditorPage.py:699
msgid "Reverse cut direction for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:466
msgid "Right"
msgstr ""

#: bCNC/plugins/drillmark.py:58
msgid "Ring mark (d/2)"
msgstr ""

#: bCNC/plugins/spiral.py:94
msgid "Risk Accepted"
msgstr ""

#: bCNC/plugins/spiral.py:475
msgid "Rotary Alignment Axis"
msgstr ""

#: bCNC/plugins/spiral.py:476
msgid "Rotary Axis"
msgstr ""

#: bCNC/EditorPage.py:588
msgid "Rotate selected gcode by 180deg"
msgstr ""

#: bCNC/EditorPage.py:573
msgid "Rotate selected gcode clock-wise (-90deg)"
msgstr ""

#: bCNC/EditorPage.py:604
msgid "Rotate selected gcode counter-clock-wise (90deg)"
msgstr ""

#: bCNC/plugins/simpleRotate.py:68
#, fuzzy
#| msgid "Tiled selected blocks"
msgid "Rotated selected blocks"
msgstr "選択範囲にタイルを貼る"

#: bCNC/plugins/simpleRotate.py:19
msgid "Rotates a block to a new position"
msgstr ""

#: bCNC/ProbePage.py:1647
msgid "Rotation:"
msgstr ""

#: bCNC/EditorPage.py:213
msgid "Round"
msgstr "ラウンド加工"

#: bCNC/EditorPage.py:647
msgid "Route"
msgstr ""

#: bCNC/CNCCanvas.py:2329
msgid "Ruler [R]"
msgstr ""

#: bCNC/Sender.py:685
msgid "Run ended"
msgstr "実行終了"

#: bCNC/ControlPage.py:184
msgid "Run g-code commands from editor to controller"
msgstr ""

#: bCNC/bmain.py:635
msgid "Running"
msgstr ""

#: bCNC/Updates.py:65
msgid "Running version of bCNC"
msgstr "bCNCの現在のバージョン"

#: bCNC/bmain.py:2695
msgid "Running..."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:123
msgid "SD Card directory listing failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:124
msgid "SD Card directory not found. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:125
msgid "SD Card file empty. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:122
msgid "SD Card file open/read failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:121
msgid "SD Card mount failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/slicemesh.py:40
msgid "STL/PLY Slicer"
msgstr ""

#: bCNC/ToolsPage.py:740
msgid "Safe Z"
msgstr ""

#: bCNC/plugins/simpleDrill.py:73
msgid "Safe z secu for G0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:53
msgid "Safety door detected as opened and door state initiated."
msgstr "安全扉が開かれたことを検出したため, 保護モードに入ります."

#: bCNC/lib/bFileDialog.py:977 bCNC/FilePage.py:153
msgid "Save"
msgstr "保存"

#: bCNC/lib/bFileDialog.py:972
msgid "Save As"
msgstr ""

#: bCNC/Ribbon.py:594
msgid "Save all [Ctrl-S]"
msgstr "ずべて保存 [Ctrl-S]"

#: bCNC/bmain.py:2366
msgid "Save file"
msgstr ""

#: bCNC/FilePage.py:159
msgid "Save gcode/dxf AS"
msgstr ""

#: bCNC/FilePage.py:145
msgid "Save gcode/dxf file [Ctrl-S]"
msgstr ""

#: bCNC/ProbePage.py:1680
msgid "Scale:"
msgstr ""

#: bCNC/plugins/scaling.py:22
msgid "Scaling"
msgstr ""

#: bCNC/plugins/scaling.py:291
msgid "Scaling Generated"
msgstr ""

#: bCNC/plugins/scaling.py:175
msgid "Scaling abort: Please select some path"
msgstr ""

#: bCNC/plugins/scaling.py:30
msgid "Scaling the selected block"
msgstr ""

#: bCNC/plugins/heightmap.py:67 bCNC/ProbePage.py:241 bCNC/ProbePage.py:271
msgid "Scan"
msgstr ""

#: bCNC/ProbePage.py:247
msgid "Scan Autolevel Margins"
msgstr ""

#: bCNC/ProbePage.py:280
msgid "Scan probed area for level information on Z plane"
msgstr ""

#: bCNC/plugins/heightmap.py:72
msgid "ScanDir"
msgstr ""

#: bCNC/EditorPage.py:96
msgid "Select"
msgstr "選択"

#: bCNC/FilePage.py:307
msgid "Select (or manual enter) port to connect"
msgstr "接続するポートを選択 (手動で入力) "

#: bCNC/EditorPage.py:112
msgid "Select all blocks [Ctrl-A]"
msgstr "すべてのブロックを選択 [Ctrl-A]"

#: bCNC/EditorPage.py:160
msgid "Select all blocks from current layer"
msgstr "現在のレイヤーのすべてのブロックを選択"

#: bCNC/FilePage.py:323
msgid "Select connection baud rate"
msgstr "ボーレートを選択"

#: bCNC/FilePage.py:340
msgid "Select controller board"
msgstr "コントローラーの種類を選択"

#: bCNC/CNCCanvas.py:430
msgid "Select objects with mouse"
msgstr ""

#: bCNC/ProbePage.py:696
msgid "Select orientation marker"
msgstr ""

#: bCNC/ControlPage.py:2078
msgid "Select override type."
msgstr ""

#: bCNC/CNCCanvas.py:2288
msgid "Select tool [S]"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:79
#, fuzzy
msgid "Selected Block"
msgstr "選択範囲にタイルを貼る"

#: bCNC/FilePage.py:197
msgid "Send Error Report"
msgstr "エラーを報告"

#: bCNC/ProbePage.py:66
msgid "Send M6 commands"
msgstr ""

#: bCNC/Utils.py:617
msgid "Send report"
msgstr "報告する"

#: bCNC/FilePage.py:289
msgid "Serial"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial Error"
msgstr ""

#: bCNC/TerminalPage.py:241
msgid "Serial Terminal"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial is not connected"
msgstr ""

#: bCNC/ControlPage.py:735
msgid "Set A coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:801
msgid "Set ABC coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:751
msgid "Set B coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:785
msgid "Set BC coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:767
msgid "Set C coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:2101
msgid "Set Feed/Rapid/Spindle Override. Right or Double click to reset."
msgstr ""

#: bCNC/ControlPage.py:436
msgid "Set WPOS"
msgstr ""

#: bCNC/ControlPage.py:445
msgid "Set WPOS to mouse location"
msgstr ""

#: bCNC/ControlPage.py:357
msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:407
msgid "Set XY coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:424
msgid "Set XYZ coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:373
msgid "Set Y coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:389
msgid "Set Z coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ProbePage.py:320
msgid "Set initial probe feed rate for tool change and calibration"
msgstr ""

#: bCNC/CNCCanvas.py:458
msgid "Set mouse location as current machine position (X/Y only)"
msgstr ""

#: bCNC/ControlPage.py:2056
msgid "Set position [G92 X# Y# Z#]"
msgstr ""

#: bCNC/ProbePage.py:340
msgid "Set probe feed rate"
msgstr ""

#: bCNC/ControlPage.py:2135
msgid "Set spindle RPM"
msgstr ""

#: bCNC/ProbePage.py:354
msgid "Set tool offset for probing"
msgstr ""

#: bCNC/controllers/_GenericController.py:163
#, fuzzy
#| msgid "Set workspace {} to {}"
msgid "Set workspace {} to {}"
msgstr "ワークスペースを {} から {} に切り替える"

#: bCNC/ToolsPage.py:1019
msgid "Sets if we want to overcut or not."
msgstr ""

#: bCNC/TerminalPage.py:80
msgid "Settings"
msgstr ""

#: bCNC/ProbePage.py:165
msgid "Setup probing for manual tool change"
msgstr ""

#: bCNC/ToolsPage.py:710
msgid "Shape"
msgstr ""

#: bCNC/plugins/jigsaw.py:369
msgid "Shape of the tap"
msgstr ""

#: bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:581
#: bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:584
#: bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:587
#: bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:590
msgid "Shift-"
msgstr ""

#: bCNC/ToolsPage.py:1837
msgid "Shortcuts"
msgstr "ショートカット"

#: bCNC/ToolsPage.py:1845
msgid "Shortcuts configuration"
msgstr "ショートカット設定"

#: bCNC/ToolsPage.py:1011
msgid "Should we machine on inside or outside of the shape?"
msgstr ""

#: bCNC/ControlPage.py:473
msgid "Show Info"
msgstr ""

#: bCNC/EditorPage.py:773
msgid "Show cutting information on selected blocks [Ctrl-n]"
msgstr ""

#: bCNC/EditorPage.py:757
msgid "Show statistics for enabled gcode"
msgstr ""

#: bCNC/ProbePage.py:121
msgid "Simple probing along a direction"
msgstr ""

#: bCNC/plugins/heightmap.py:77
msgid "Single pass"
msgstr ""

#: bCNC/lib/bFileDialog.py:306 bCNC/plugins/hilbert.py:112
msgid "Size"
msgstr ""

#: bCNC/ToolsPage.py:1004
msgid "Size of this endmill will be used as offset distance"
msgstr ""

#: bCNC/plugins/sketch.py:20
msgid "Sketch"
msgstr ""

#: bCNC/plugins/sketch.py:255
msgid "Sketch abort: Can't read image file"
msgstr ""

#: bCNC/plugins/sketch.py:244
msgid "Sketch abort: Please let me draw at least 1 squiggle"
msgstr ""

#: bCNC/plugins/sketch.py:248
msgid "Sketch abort: Squiggle Length must be > 0"
msgstr ""

#: bCNC/plugins/sketch.py:200
msgid "Sketch abort: This plugin requires PIL/Pillow to read image data"
msgstr ""

#: bCNC/plugins/sketch.py:234
msgid "Sketch abort: Too small to draw anything!"
msgstr ""

#: bCNC/plugins/slicemesh.py:179
msgid "Slicing {} {:f} in {:f} -> {:f} of {}"
msgstr ""

#: bCNC/plugins/function_plot.py:34
#, fuzzy
#| msgid "Line length"
msgid "Small line length"
msgstr "直線の長さ"

#: bCNC/controllers/_GenericGRBL.py:46
msgid "Soft limits cannot be enabled without homing also enabled."
msgstr ""
"ソフトリミットを利用するには, 初期位置補正が有効化されている必要があります."

#: bCNC/ControlPage.py:143
msgid "Software reset of controller [ctrl-x]"
msgstr ""

#: bCNC/plugins/midi2cnc.py:32
msgid "Sound your machine from a midi file"
msgstr ""

#: bCNC/ControlPage.py:2112
msgid "Spindle"
msgstr ""

#: bCNC/ProbePage.py:1737
msgid "Spindle Z position when camera was registered"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:156
msgid "Spindle at speed timeout. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:654
msgid "Spindle max (RPM)"
msgstr "スピンドル 最高回転数 (RPM)"

#: bCNC/ToolsPage.py:653
msgid "Spindle min (RPM)"
msgstr "スピンドル 最低回転数 (RPM)"

#: bCNC/ProbePage.py:1854
msgid "Spindle position is not registered"
msgstr ""

#: bCNC/ProbePage.py:1846
msgid "Spindle position is registered"
msgstr ""

#: bCNC/ProbePage.py:1855
msgid "Spindle position must be registered before camera"
msgstr ""

#: bCNC/plugins/spiral.py:18
#, fuzzy
#| msgid "Spirograph"
msgid "Spiral"
msgstr "スピログラフ"

#: bCNC/plugins/spiral.py:69
#, fuzzy
msgid "Spiral abort: Approach height must be greater than Z Start"
msgstr "ステップパルスは3usecより長い必要があります."

#: bCNC/plugins/spiral.py:74
msgid "Spiral abort: Depth Reduction must be negative"
msgstr ""

#: bCNC/plugins/spiral.py:156 bCNC/plugins/spiral.py:179
#: bCNC/plugins/spiral.py:192 bCNC/plugins/spiral.py:220
#: bCNC/plugins/spiral.py:284 bCNC/plugins/spiral.py:318
#: bCNC/plugins/spiral.py:357 bCNC/plugins/spiral.py:379
#: bCNC/plugins/spiral.py:430
msgid "Spiral abort: Rotary Axis Not Assigned."
msgstr ""

#: bCNC/plugins/spiral.py:60
msgid "Spiral abort: Rotary Axis is undefined"
msgstr ""

#: bCNC/plugins/spiral.py:64
msgid "Spiral abort: Spiral Type is undefined"
msgstr ""

#: bCNC/plugins/spiral.py:80
msgid "Spiral abort: Step Over exceeds tool limits"
msgstr ""

#: bCNC/plugins/spiral.py:124
msgid "Spiral abort: Stock Length is too small for this End Mill."
msgstr ""

#: bCNC/plugins/spiral.py:100
msgid "Spiral abort: Stock Length to cut must be positive"
msgstr ""

#: bCNC/plugins/spiral.py:518
msgid "Spiral: Reduced 4th Axis Stock"
msgstr ""

#: bCNC/plugins/spirograph.py:17
msgid "Spirograph"
msgstr "スピログラフ"

#: bCNC/EditorPage.py:401
msgid "Split"
msgstr ""

#: bCNC/EditorPage.py:407
msgid "Split selected blocks"
msgstr ""

#: bCNC/ToolsPage.py:795
msgid "Spring pass"
msgstr ""

#: bCNC/plugins/sketch.py:46
msgid "Squiggle Length"
msgstr ""

#: bCNC/plugins/sketch.py:45
msgid "Squiggle total count"
msgstr ""

#: bCNC/ControlPage.py:178 bCNC/FilePage.py:238
msgid "Start"
msgstr ""

#: bCNC/plugins/simpleArc.py:68
msgid "Start Angle in Degrees"
msgstr ""

#: bCNC/ControlPage.py:2175
msgid "Start flood (M8)"
msgstr ""

#: bCNC/ControlPage.py:2189
msgid "Start mist (M7)"
msgstr ""

#: bCNC/FilePage.py:246
msgid "Start pendant"
msgstr ""

#: bCNC/ToolsPage.py:652
msgid "Start up"
msgstr "開始"

#: bCNC/ControlPage.py:2121
msgid "Start/Stop spindle (M3/M5)"
msgstr ""

#: bCNC/TerminalPage.py:140
msgid "Startup"
msgstr ""

#: bCNC/ControlPage.py:1877 bCNC/TerminalPage.py:110
msgid "State"
msgstr ""

#: bCNC/ControlPage.py:611 bCNC/ControlPage.py:918
msgid "State: {}"
msgstr ""

#: bCNC/EditorPage.py:750 bCNC/bmain.py:1133
msgid "Statistics"
msgstr ""

#: bCNC/ControlPage.py:236
msgid "Status:"
msgstr ""

#: bCNC/ProbePage.py:1234
msgid "Step"
msgstr ""

#: bCNC/plugins/zigzag.py:109
msgid "Step distance"
msgstr "1ステップの長さ"

#: bCNC/ControlPage.py:1562
msgid "Step for A move operation"
msgstr ""

#: bCNC/ControlPage.py:1090
msgid "Step for Z move operation"
msgstr ""

#: bCNC/ControlPage.py:1075 bCNC/ControlPage.py:1546
msgid "Step for every move operation"
msgstr ""

#: bCNC/ControlPage.py:1265 bCNC/ControlPage.py:1739
msgid "Step: {:g}"
msgstr ""

#: bCNC/ControlPage.py:1744
msgid "Step: {:g}   Astep:{:g} "
msgstr ""

#: bCNC/ControlPage.py:1270
msgid "Step: {:g}  Zstep: {:g} "
msgstr ""

#: bCNC/ToolsPage.py:718
msgid "Stepover %"
msgstr ""

#: bCNC/plugins/spiral.py:480
msgid "Stepover (spiral=mm, lines=deg)"
msgstr ""

#: bCNC/ToolsPage.py:1405
msgid "Stock"
msgstr "ストック"

#: bCNC/ToolsPage.py:1413
msgid "Stock material currently on machine"
msgstr "現在の材料設定を保存する"

#: bCNC/ControlPage.py:208 bCNC/FilePage.py:251
msgid "Stop"
msgstr ""

#: bCNC/ControlPage.py:2161
msgid "Stop cooling (M9)"
msgstr ""

#: bCNC/FilePage.py:259
msgid "Stop pendant"
msgstr ""

#: bCNC/ToolsPage.py:741 bCNC/ToolsPage.py:766
msgid "Surface Z"
msgstr ""

#: bCNC/ProbePage.py:1543
msgid "Switch To"
msgstr ""

#: bCNC/ProbePage.py:1553
msgid "Switch between camera and spindle"
msgstr ""

#: bCNC/ControlPage.py:1904
msgid "Switch to workspace {}"
msgstr ""

#: bCNC/ProbePage.py:347
msgid "TLO"
msgstr ""

#: bCNC/ControlPage.py:2029
msgid "TLO:"
msgstr ""

#: bCNC/ToolsPage.py:1638
msgid "Tabs"
msgstr "タブ"

#: bCNC/plugins/trochoidPath.py:63
msgid "Tabs Diameter"
msgstr ""

#: bCNC/plugins/trochoidPath.py:64
#, fuzzy
#| msgid "Height Dz"
msgid "Tabs Height"
msgstr "高さ Dz"

#: bCNC/ToolsPage.py:1153
msgid "Tabs error"
msgstr ""

#: bCNC/plugins/driller.py:46 bCNC/plugins/spirograph.py:122
#: bCNC/plugins/trochoidPath.py:53 bCNC/plugins/trochoidal_3D.py:82
#: bCNC/ToolsPage.py:767 bCNC/ToolsPage.py:939
msgid "Target Depth"
msgstr ""

#: bCNC/plugins/simpleDrill.py:69
msgid "Target z (negative under surface)"
msgstr ""

#: bCNC/TerminalPage.py:38 bCNC/TerminalPage.py:189
msgid "Terminal"
msgstr ""

#: bCNC/TerminalPage.py:205
msgid "Terminal communication with controller"
msgstr ""

#: bCNC/plugins/text.py:94
#, fuzzy
#| msgid "Text abort: That embarrassing, I can't read this font file!"
msgid "Text abort: That's embarrassing, I can't read this font file!"
msgstr "テキストは無視: 困りましたねえ... このフォントファイル読めないわ..."

#: bCNC/plugins/text.py:62
msgid "Text abort: please input a Font size > 0"
msgstr "テキストは無視: 0より大きいフォントサイズを指定してください."

#: bCNC/plugins/text.py:65
msgid "Text abort: please select a font file"
msgstr "テキストは無視:　フォントファイルを選択してください."

#: bCNC/plugins/text.py:38
msgid "Text to generate"
msgstr "生成するテキスト"

#: bCNC/Utils.py:573
#, fuzzy
#| msgid "The following report is about to be send to the author of {}"
msgid "The following report is about to be send to the author of {}"
msgstr "以下のレポートは作者 {} に送られます"

#: bCNC/plugins/sketch.py:239
msgid "The maximum illumination shouldn't be more than 250!"
msgstr ""

#: bCNC/Utils.py:695
msgid "There was a problem connecting to the web site"
msgstr "webサイトへの接続に問題があります"

#: bCNC/ToolsPage.py:742
msgid "Thickness"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:57
msgid "This is my Helical Descent"
msgstr ""

#: bCNC/plugins/tile.py:13
msgid "Tile"
msgstr ""

#: bCNC/plugins/Random.py:44 bCNC/plugins/simpleRotate.py:48
#: bCNC/plugins/simpleTranslate.py:46 bCNC/plugins/tile.py:44
msgid "Tile error"
msgstr "タイルエラー"

#: bCNC/plugins/Random.py:80 bCNC/plugins/tile.py:85
msgid "Tiled selected blocks"
msgstr "選択範囲にタイルを貼る"

#: bCNC/bmain.py:1222 bCNC/bmain.py:1301
msgid "Time:"
msgstr ""

#: bCNC/CNCCanvas.py:2437
msgid "Timeout:"
msgstr ""

#: bCNC/CNCCanvas.py:2347
msgid "Toggle display of axes"
msgstr ""

#: bCNC/CNCCanvas.py:2418
msgid "Toggle display of camera"
msgstr ""

#: bCNC/CNCCanvas.py:2357
msgid "Toggle display of grid lines"
msgstr ""

#: bCNC/CNCCanvas.py:2367
msgid "Toggle display of margins"
msgstr ""

#: bCNC/CNCCanvas.py:2388
msgid "Toggle display of paths (G1,G2,G3)"
msgstr ""

#: bCNC/CNCCanvas.py:2378
msgid "Toggle display of probe"
msgstr ""

#: bCNC/CNCCanvas.py:2398
msgid "Toggle display of rapid motion (G0)"
msgstr ""

#: bCNC/CNCCanvas.py:2408
msgid "Toggle display of workarea"
msgstr ""

#: bCNC/EditorPage.py:316
msgid "Toggle enable/disable block of g-code [Ctrl-L]"
msgstr ""

#: bCNC/EditorPage.py:359
msgid "Toggle expand/collapse blocks of gcode [Ctrl-E]"
msgstr ""

#: bCNC/ToolsPage.py:1662
msgid "Toggle island"
msgstr ""

#: bCNC/ProbePage.py:158
msgid "Tool"
msgstr ""

#: bCNC/Utils.py:852
msgid "Tool Tip:"
msgstr ""

#: bCNC/ProbePage.py:1979
msgid "Tool change policy"
msgstr ""

#: bCNC/ControlPage.py:2041
msgid "Tool length offset [G43.1#]"
msgstr ""

#: bCNC/ControlPage.py:1957
msgid "Tool number [T#]"
msgstr ""

#: bCNC/ProbePage.py:2127
msgid "Tool probe height"
msgstr ""

#: bCNC/CNCCanvas.py:2273 bCNC/ControlPage.py:1950
msgid "Tool:"
msgstr ""

#: bCNC/Utils.py:857
msgid "Tooltip for button"
msgstr ""

#: bCNC/EditorPage.py:462
msgid "Top"
msgstr ""

#: bCNC/EditorPage.py:459
msgid "Top-Left"
msgstr ""

#: bCNC/EditorPage.py:465
msgid "Top-Right"
msgstr ""

#: bCNC/EditorPage.py:558
msgid "Transform"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:19
msgid "Translates a block to a new position"
msgstr ""

#: bCNC/ToolsPage.py:647
msgid "Travel x"
msgstr "x軸 可動域"

#: bCNC/ToolsPage.py:648
msgid "Travel y"
msgstr "y軸 可動域"

#: bCNC/ToolsPage.py:649
msgid "Travel z"
msgstr "z軸 可動域"

#: bCNC/plugins/trochoidal_3D.py:47
msgid "Trochoid Cut Diameter"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:402
msgid "Trochoid Cut Diameter has to be greater than End mill"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:1672
msgid "Trochoid Generated"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:397
#, fuzzy
msgid "Trochoid abort: Please select some path"
msgstr "テキストは無視:　フォントファイルを選択してください."

#: bCNC/plugins/trochoidal.py:52
msgid "Trochoid diameter (<= cutter D)"
msgstr ""

#: bCNC/plugins/trochoidal.py:49
msgid "Trochoid entry (prepare for helicut)"
msgstr ""

#: bCNC/plugins/trochoidal.py:18
msgid "Trochoidal"
msgstr ""

#: bCNC/plugins/trochoidPath.py:20
msgid "Trochoidal Path"
msgstr ""

#: bCNC/plugins/trochoidal.py:24
msgid "Trochoidal g-code postprocessor"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:30
msgid "Trochoidcut"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:50
msgid "Trochoids Advance"
msgstr ""

#: bCNC/ProbePage.py:1568
msgid "Turn on/off edge detection"
msgstr ""

#: bCNC/ProbePage.py:1583
msgid "Turn on/off freeze image"
msgstr ""

#: bCNC/lib/bFileDialog.py:305 bCNC/ToolsPage.py:709
msgid "Type"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:56
msgid "Type of Splice"
msgstr ""

#: bCNC/plugins/drillmark.py:56
msgid "Type of the mark"
msgstr ""

#: bCNC/Ribbon.py:604
msgid "Undo [Ctrl-Z]"
msgstr "戻す [Ctrl-Z]"

#: bCNC/ToolsPage.py:636
msgid "Units (inches)"
msgstr ""

#: bCNC/ControlPage.py:1942
msgid "Units [G20, G21]"
msgstr ""

#: bCNC/ControlPage.py:1931
msgid "Units:"
msgstr ""

#: bCNC/ControlPage.py:108
msgid "Unlock"
msgstr ""

#: bCNC/ControlPage.py:115
msgid "Unlock controller [$X]"
msgstr ""

#: bCNC/EditorPage.py:128
msgid "Unselect all blocks [Ctrl-Shift-A]"
msgstr "すべてのブロックの選択を解除"

#: bCNC/controllers/_GenericGRBL.py:60
msgid "Unsupported or invalid g-code command found in block."
msgstr ""
"ブロック内で,サポートされていないか, 無効なGコード コマンドが見つかりました."

#: bCNC/EditorPage.py:505
msgid "Up"
msgstr ""

#: bCNC/FilePage.py:203
msgid "Updates"
msgstr "更新"

#: bCNC/plugins/heightmap.py:56
msgid "Use a brightness map to create a variable Z path"
msgstr ""

#: bCNC/plugins/driller.py:49
msgid "Use anchor"
msgstr ""

#: bCNC/ToolsPage.py:1921
msgid "User File"
msgstr "ユーザーファイル"

#: bCNC/Utils.py:823
msgid "User configurable button"
msgstr "ユーザーカスタムボタン"

#: bCNC/ToolsPage.py:1981
msgid "Value"
msgstr "値"

#: bCNC/controllers/_GenericGRBL.py:107
msgid "Value out of range. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:118
msgid "Value word conflict. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:627
msgid "Vertical"
msgstr ""

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
msgid "WARNING: {}"
msgstr ""

#: bCNC/ControlPage.py:261 bCNC/ProbePage.py:765
msgid "WPos:"
msgstr ""

#: bCNC/bmain.py:2524
msgid "Warning"
msgstr ""

#: bCNC/plugins/function_plot.py:110
msgid "Warning: "
msgstr ""

#: bCNC/ToolsPage.py:621
#, fuzzy
msgid "Web Camera"
msgstr "カメラ"

#: bCNC/ToolsPage.py:624
msgid "Web Camera Angle"
msgstr ""

#: bCNC/ToolsPage.py:623
msgid "Web Camera Height"
msgstr ""

#: bCNC/ToolsPage.py:622
msgid "Web Camera Width"
msgstr ""

#: bCNC/plugins/box.py:348
msgid "Width Dx"
msgstr "幅 Dx"

#: bCNC/plugins/flatten.py:291
msgid "Width to flatten"
msgstr ""

#: bCNC/ProbePage.py:149
msgid "Work surface camera view and alignment"
msgstr ""

#: bCNC/plugins/halftone.py:52 bCNC/plugins/sketch.py:43
#: bCNC/plugins/text.py:39 bCNC/plugins/heightmap.py:65
#: bCNC/plugins/pyrograph.py:39
msgid "Working Depth"
msgstr ""

#: bCNC/plugins/simpleDrill.py:67 bCNC/ControlPage.py:1015
msgid "X"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:80
msgid "X Initial"
msgstr ""

#: bCNC/plugins/scaling.py:39
msgid "X Scale"
msgstr ""

#: bCNC/plugins/scaling.py:42
msgid "X Y Center"
msgstr ""

#: bCNC/ProbePage.py:1275
msgid "X bins"
msgstr ""

#: bCNC/plugins/function_plot.py:30
#, fuzzy
#| msgid "Internal Dimensions"
msgid "X dimension"
msgstr "内部の寸法"

#: bCNC/ProbePage.py:1255
msgid "X maximum"
msgstr ""

#: bCNC/ProbePage.py:1247
msgid "X minimum"
msgstr ""

#: bCNC/plugins/function_plot.py:32
msgid "X number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:289 bCNC/plugins/spiral.py:472
msgid "X start"
msgstr ""

#: bCNC/ProbePage.py:1263
msgid "X step"
msgstr ""

#: bCNC/ControlPage.py:274
msgid "X work position (click to set)"
msgstr ""

#: bCNC/ProbePage.py:1241
msgid "X:"
msgstr ""

#: bCNC/ControlPage.py:349
msgid "X=0"
msgstr ""

#: bCNC/ControlPage.py:399
msgid "XY=0"
msgstr ""

#: bCNC/ControlPage.py:415
msgid "XYZ=0"
msgstr ""

#: bCNC/plugins/simpleDrill.py:68 bCNC/ControlPage.py:936
msgid "Y"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:81
msgid "Y Initial"
msgstr ""

#: bCNC/plugins/scaling.py:40
msgid "Y Scale"
msgstr ""

#: bCNC/ProbePage.py:1315
msgid "Y bins"
msgstr ""

#: bCNC/plugins/function_plot.py:31
#, fuzzy
#| msgid "Internal Dimensions"
msgid "Y dimension"
msgstr "内部の寸法"

#: bCNC/ProbePage.py:1295
msgid "Y maximum"
msgstr ""

#: bCNC/ProbePage.py:1287
msgid "Y minimum"
msgstr ""

#: bCNC/plugins/function_plot.py:33
msgid "Y number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:290 bCNC/plugins/spiral.py:473
msgid "Y start"
msgstr ""

#: bCNC/ProbePage.py:1303
msgid "Y step"
msgstr ""

#: bCNC/ControlPage.py:290
msgid "Y work position (click to set)"
msgstr ""

#: bCNC/ProbePage.py:1281
msgid "Y:"
msgstr ""

#: bCNC/ControlPage.py:365
msgid "Y=0"
msgstr ""

#: bCNC/ToolsPage.py:1154
msgid "You cannot have both the number of tabs or distance equal to zero"
msgstr ""

#: bCNC/ToolsPage.py:807
msgid "You should probably always use 'on path', unless you are threadmilling!"
msgstr ""

#: bCNC/Utils.py:591
msgid "Your email"
msgstr "メールアドレス"

#: bCNC/ControlPage.py:933 bCNC/ProbePage.py:462
msgid "Z"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:88
msgid "Z Feed Multiplier"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:82
msgid "Z Initial"
msgstr ""

#: bCNC/ProbePage.py:1328
msgid "Z Minimum depth to scan"
msgstr ""

#: bCNC/plugins/scaling.py:41
msgid "Z Scale"
msgstr ""

#: bCNC/plugins/simpleDrill.py:72
msgid "Z feed for drilling"
msgstr ""

#: bCNC/ProbePage.py:1336
msgid "Z safe to move"
msgstr ""

#: bCNC/plugins/spiral.py:474
msgid "Z start"
msgstr ""

#: bCNC/ControlPage.py:306
msgid "Z work position (click to set)"
msgstr ""

#: bCNC/ProbePage.py:1322
msgid "Z:"
msgstr ""

#: bCNC/ControlPage.py:381
msgid "Z=0"
msgstr ""

#: bCNC/ProbePage.py:202
msgid "Zero"
msgstr ""

#: bCNC/plugins/zigzag.py:15
msgid "Zig-Zag"
msgstr "ジグザグ"

#: bCNC/plugins/zigzag.py:148
msgid "Zig-Zag abort: depth must be minor or equal to zero"
msgstr "中止 ジグザグ加工: 深さは,0以上である必要があります."

#: bCNC/plugins/zigzag.py:143
msgid "Zig-Zag abort: verify CornerRes >= 0"
msgstr "中止 ジグザグ加工: CornerRes >= 0 であることを確認してください"

#: bCNC/plugins/zigzag.py:135
msgid "Zig-Zag abort: verify LineLen > 0"
msgstr "中止 ジグザグ加工: LineLen > 0 であることを確認してください"

#: bCNC/plugins/zigzag.py:131
msgid "Zig-Zag abort: verify Nlines > 0"
msgstr "中止 ジグザグ加工: Nlines > 0 であることを確認してください"

#: bCNC/plugins/zigzag.py:139
msgid "Zig-Zag abort: verify Step > 0"
msgstr "中止 ジグザグ加工: Step >=0 であることを確認してください"

#: bCNC/CNCCanvas.py:2256
msgid "Zoom In [Ctrl-=]"
msgstr ""

#: bCNC/CNCCanvas.py:2263
msgid "Zoom Out [Ctrl--]"
msgstr ""

#: bCNC/ControlPage.py:1400
#, fuzzy
msgid "abcControl"
msgstr "コントローラー"

#: bCNC/ControlPage.py:625
msgid "abcDRO"
msgstr ""

#: bCNC/ControlPage.py:637
msgid "abcWPos:"
msgstr ""

#: bCNC/plugins/endmilloffset.py:479
msgid "acts like a tool corrector inside the profile"
msgstr ""

#: bCNC/plugins/simpleRotate.py:28
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "angle step (degrees)"
msgstr "解像度 (角度)"

#: bCNC/plugins/dragknife.py:54
msgid "angle threshold"
msgstr ""

#: bCNC/plugins/arcfit.py:38
msgid "arc precision (mm)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:12
msgid "bCNC is currently sending a gcode program to Grbl"
msgstr "GrblにGコードを送っています"

#: bCNC/plugins/simpleRectangle.py:97
msgid "clockwise"
msgstr ""

#: bCNC/plugins/dragknife.py:48
msgid "distance from dragknife rotation center to the tip of the blade"
msgstr ""

#: bCNC/plugins/dragknife.py:55
msgid "do not perform pivot action for angles smaller than this"
msgstr ""

#: bCNC/plugins/dragknife.py:47
msgid "dragknife offset"
msgstr ""

#: bCNC/CNCCanvas.py:678
msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgstr ""

#: bCNC/plugins/dragknife.py:75
msgid "feedrate"
msgstr ""

#: bCNC/ProbePage.py:2047 bCNC/ProbePage.py:2092
msgid "get"
msgstr ""

#: bCNC/plugins/arcfit.py:39
msgid "how precisely must arc fit. set to 0 to disable arc fitting"
msgstr ""

#: bCNC/plugins/endmilloffset.py:463
msgid "indicates the direction (inside / outside) for making profiles"
msgstr ""

#: bCNC/plugins/dragknife.py:70
msgid "initial direction"
msgstr ""

#: bCNC/plugins/slicemesh.py:83
msgid "layer height (0 = only single zmin)"
msgstr ""

#: bCNC/plugins/arcfit.py:45
msgid "line precision (mm)"
msgstr ""

#: bCNC/plugins/slicemesh.py:88
msgid "maximum Z height"
msgstr ""

#: bCNC/plugins/arcfit.py:50
msgid "minimal number of segments to create arc"
msgstr ""

#: bCNC/plugins/slicemesh.py:86
msgid "minimum Z height"
msgstr ""

#: bCNC/plugins/simpleRotate.py:29 bCNC/plugins/simpleTranslate.py:28
msgid "nb repeat including original"
msgstr ""

#: bCNC/CNCCanvas.py:627
msgid "origin {:g} {:g} {:g}"
msgstr ""

#: bCNC/bmain.py:3097
msgid "python serial missing"
msgstr ""

#: bCNC/plugins/slicemesh.py:74
msgid "scale factor"
msgstr ""

#: bCNC/plugins/linearize.py:39
msgid "segment size"
msgstr ""

#: bCNC/ControlPage.py:1961 bCNC/ControlPage.py:2003 bCNC/ControlPage.py:2045
#: bCNC/ProbePage.py:360
msgid "set"
msgstr ""

#: bCNC/plugins/dragknife.py:80
msgid "simulate"
msgstr ""

#: bCNC/plugins/dragknife.py:89
msgid "simulation precision"
msgstr ""

#: bCNC/plugins/slicemesh.py:23
msgid "slicemesh"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:101
msgid "splice steps every 360 degrees"
msgstr ""

#: bCNC/plugins/linearize.py:48
msgid "subdiv lines"
msgstr ""

#: bCNC/plugins/dragknife.py:61
msgid "swivel height"
msgstr ""

#: bCNC/Sender.py:355
msgid "unknown command"
msgstr ""

#: bCNC/plugins/simpleRotate.py:26
msgid "x center"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:26
msgid "x increment"
msgstr ""

#: bCNC/plugins/simpleLine.py:52 bCNC/plugins/simpleRectangle.py:93
msgid "xEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:51 bCNC/plugins/simpleRectangle.py:92
#, fuzzy
#| msgid "Start up"
msgid "xStart"
msgstr "開始"

#: bCNC/plugins/simpleRotate.py:27
msgid "y center"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:27
msgid "y increment"
msgstr ""

#: bCNC/plugins/simpleLine.py:54 bCNC/plugins/simpleRectangle.py:95
msgid "yEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:53 bCNC/plugins/simpleRectangle.py:94
#, fuzzy
#| msgid "Start up"
msgid "yStart"
msgstr "開始"

#: bCNC/plugins/slicemesh.py:77
msgid "z offset"
msgstr ""

#~ msgid "Color configuration"
#~ msgstr "色設定"

#~ msgid "Font configuration"
#~ msgstr "フォント設定"

#~ msgid "Tools"
#~ msgstr "ツール"
