# دليل إعداد الكاميرا وتفعيل المحاذاة بين النقطتين

## 🎥 **خطوات تفعيل الكاميرا**

### الخطوة 1: تفعيل الكاميرا في الإعدادات

1. **افتح البرنامج bCNC**
2. **اذهب إلى Tools → Config/Controller**
3. **ابحث عن قسم Camera**
4. **قم بتعديل الإعدادات التالية**:
   ```
   aligncam = 0  ← غير هذا إلى رقم الكاميرا (عادة 0 أو 1)
   aligncam_width = 640   ← عرض الكاميرا
   aligncam_height = 480  ← ارتفاع الكاميرا
   aligncam_angle = 0     ← زاوية الكاميرا (0, 90, 180, 270)
   ```

### الخطوة 2: حفظ الإعدادات وإعادة التشغيل

1. **اضغط Save** لحفظ الإعدادات
2. **أعد تشغيل البرنامج** لتطبيق التغييرات
3. **تأكد من توصيل الكاميرا بالكمبيوتر**

### الخطوة 3: اختبار الكاميرا

1. **اذهب إلى تبويب Probe**
2. **ابحث عن قسم Camera**
3. **اضغط زر "Switch To"** لتفعيل الكاميرا
4. **يجب أن ترى صورة الكاميرا في منطقة الرسم**

## 🎯 **استخدام ميزة المحاذاة بين النقطتين**

### من الواجهة الرئيسية:

1. **تفعيل الكاميرا**:
   - اذهب إلى Probe → Camera
   - اضغط "Switch To" لتفعيل الكاميرا

2. **فتح أداة المحاذاة**:
   - اضغط زر "Two Point Alignment" في قسم Camera
   - ستفتح نافذة الإضافة

3. **التقاط النقاط**:
   - حرك الآلة إلى النقطة الأولى على قطعة العمل
   - اضغط "Capture Point 1"
   - حرك إلى النقطة الثانية
   - اضغط "Capture Point 2"

4. **تحديد الأهداف**:
   - أدخل إحداثيات النقطة الأولى المطلوبة (Target 1 X, Y)
   - أدخل إحداثيات النقطة الثانية المطلوبة (Target 2 X, Y)

5. **تطبيق المحاذاة**:
   - اضغط "Calculate" لحساب التحويل
   - اختر كتل G-code في المحرر
   - اضغط "Apply" لإنشاء النسخة المحاذاة

### من واجهة الويب:

1. **تشغيل خادم الويب**:
   - في البرنامج، اذهب إلى Tools → Web Pendant
   - اضغط "Start" لتشغيل الخادم

2. **الوصول للكاميرا**:
   - افتح المتصفح واذهب إلى العنوان المعروض
   - اختر "Camera" من القائمة

3. **استخدام أزرار المحاذاة**:
   - "Capture Point 1" - التقاط النقطة الأولى
   - "Capture Point 2" - التقاط النقطة الثانية  
   - "Calculate" - حساب المحاذاة
   - "Apply" - تطبيق التحويل

## 🔧 **حل المشاكل الشائعة**

### مشكلة: الكاميرا لا تعمل
**الحلول**:
- تأكد من توصيل الكاميرا بالكمبيوتر
- جرب أرقام كاميرا مختلفة (0, 1, 2)
- تأكد من أن الكاميرا غير مستخدمة في برنامج آخر
- أعد تشغيل البرنامج بعد تغيير الإعدادات

### مشكلة: زر المحاذاة غير مرئي
**الحلول**:
- تأكد من أن opencv مثبت بشكل صحيح
- أعد تشغيل البرنامج
- تحقق من وجود ملف الإضافة في مجلد plugins

### مشكلة: الكاميرا تظهر صورة مقلوبة
**الحلول**:
- غير قيمة aligncam_angle إلى 180
- أو استخدم 90 أو 270 حسب الحاجة

## 📋 **إعدادات الكاميرا المتقدمة**

```ini
[Camera]
# رقم الكاميرا (0 = الكاميرا الأولى، 1 = الثانية، إلخ)
aligncam = 0

# دقة الكاميرا
aligncam_width = 640
aligncam_height = 480

# زاوية الدوران (0, 90, 180, 270)
aligncam_angle = 0

# نصف قطر الدائرة المرجعية (بالوحدات)
aligncam_r = 3.175

# مقياس الكاميرا (بكسل/وحدة)
aligncam_scale = 10.0

# موضع الكاميرا (Center, N, S, E, W, NE, NW, SE, SW)
aligncam_anchor = Center

# إزاحة الكاميرا عن المغزل
aligncam_dx = 0
aligncam_dy = 0
aligncam_z = 0

# دوران وتوسيط الكاميرا
aligncam_rotation = 0
aligncam_xcenter = 0
aligncam_ycenter = 0
```

## 🎬 **مثال عملي خطوة بخطوة**

1. **الهدف**: محاذاة قطعة عمل مائلة بزاوية 10 درجات

2. **الخطوات**:
   - شغل الكاميرا واضبط الإعدادات
   - حرك الآلة إلى زاوية قطعة العمل (مثلاً النقطة 10,10)
   - اضغط "Capture Point 1"
   - حرك إلى نقطة أخرى على حافة القطعة (مثلاً 50,12)
   - اضغط "Capture Point 2"
   - حدد النقاط المستهدفة: (10,10) و (50,10) للمحاذاة الأفقية
   - اضغط "Calculate" ثم "Apply"

3. **النتيجة**: G-code محاذى مع قطعة العمل المائلة!

---
**ملاحظة**: تأكد من معايرة الكاميرا بدقة للحصول على أفضل النتائج.
