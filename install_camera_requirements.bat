@echo off
echo ========================================
echo   bCNC Camera Requirements Installer
echo   Installing compatible versions
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found in PATH. Trying alternative locations...
    if exist "C:\Python313\python.exe" (
        set PYTHON_CMD=C:\Python313\python.exe
        set PIP_CMD=C:\Python313\Scripts\pip.exe
    ) else if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" (
        set PYTHON_CMD=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
        set PIP_CMD=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\pip.exe
    ) else (
        echo Python not found! Please install Python 3.8 or newer.
        pause
        exit /b 1
    )
) else (
    set PYTHON_CMD=python
    set PIP_CMD=pip
)

echo Using Python: %PYTHON_CMD%
echo Using Pip: %PIP_CMD%
echo.

echo Step 1: Uninstalling conflicting packages...
%PIP_CMD% uninstall -y numpy opencv-python opencv-contrib-python

echo.
echo Step 2: Installing compatible NumPy...
%PIP_CMD% install numpy==1.21.6

echo.
echo Step 3: Installing compatible OpenCV...
%PIP_CMD% install opencv-python==********

echo.
echo Step 4: Installing other requirements...
%PIP_CMD% install pyserial>=3.5
%PIP_CMD% install Pillow>=4.0

echo.
echo Step 5: Testing installation...
%PYTHON_CMD% -c "import numpy; print('NumPy version:', numpy.__version__)"
%PYTHON_CMD% -c "import cv2; print('OpenCV version:', cv2.__version__)"
%PYTHON_CMD% -c "import serial; print('PySerial: OK')"
%PYTHON_CMD% -c "from PIL import Image; print('Pillow: OK')"

echo.
echo Step 6: Testing camera...
%PYTHON_CMD% -c "import cv2; cap = cv2.VideoCapture(0); print('Camera 0:', 'OK' if cap.isOpened() else 'Not found'); cap.release()"

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Update bCNC.ini camera settings:
echo    [Camera]
echo    aligncam = 0
echo    aligncam_width = 640
echo    aligncam_height = 480
echo.
echo 2. Run bCNC:
echo    run_bCNC_with_camera.bat
echo.
echo 3. Enable camera in Probe ^> Camera ^> Switch To
echo.
echo 4. Use Two Point Alignment feature!
echo.

pause
