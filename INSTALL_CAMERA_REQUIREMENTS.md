# دليل تثبيت متطلبات الكاميرا لـ bCNC

## 🚨 **المشاكل الشائعة وحلولها**

### المشكلة 1: OpenCV غير مثبت
```
❌ OpenCV not found. Install with: pip install opencv-python
```

**الحل**:
```bash
pip install opencv-python==********
```

### المشكلة 2: تعارض إصدارات numpy
```
RuntimeError: module compiled against ABI version 0x1000009 but this version of numpy is 0x2000000
```

**الحل**:
```bash
pip uninstall numpy
pip install numpy==1.21.6
```

### المشكلة 3: تعارض إصدارات OpenCV
**الحل**:
```bash
pip uninstall opencv-python opencv-contrib-python
pip install opencv-python==********
```

## 📦 **تثبيت المتطلبات الكاملة**

### الطريقة 1: تثبيت سريع
```bash
pip install pyserial>=3.5 numpy==1.21.6 Pillow>=4.0 opencv-python==********
```

### الطريقة 2: تثبيت من ملف requirements
```bash
cd bCNC-master\bCNC-master
pip install -r bCNC\requirements.txt
```

### الطريقة 3: تثبيت يدوي خطوة بخطوة
```bash
# 1. إزالة الإصدارات المتعارضة
pip uninstall numpy opencv-python opencv-contrib-python

# 2. تثبيت numpy المتوافق
pip install numpy==1.21.6

# 3. تثبيت OpenCV المتوافق
pip install opencv-python==********

# 4. تثبيت باقي المتطلبات
pip install pyserial>=3.5 Pillow>=4.0
```

## 🔧 **اختبار التثبيت**

### اختبار Python والمكتبات:
```python
import sys
print("Python version:", sys.version)

try:
    import numpy
    print("✅ NumPy version:", numpy.__version__)
except ImportError:
    print("❌ NumPy not found")

try:
    import cv2
    print("✅ OpenCV version:", cv2.__version__)
except ImportError:
    print("❌ OpenCV not found")

try:
    import serial
    print("✅ PySerial available")
except ImportError:
    print("❌ PySerial not found")

try:
    from PIL import Image
    print("✅ Pillow available")
except ImportError:
    print("❌ Pillow not found")
```

### اختبار الكاميرا:
```python
import cv2

# اختبار الكاميرا الأولى
cap = cv2.VideoCapture(0)
if cap.isOpened():
    ret, frame = cap.read()
    if ret:
        print("✅ Camera 0 is working")
        print(f"Resolution: {frame.shape[1]}x{frame.shape[0]}")
    else:
        print("❌ Can't read from camera 0")
    cap.release()
else:
    print("❌ Can't open camera 0")
```

## 🎯 **تشغيل bCNC مع الكاميرا**

### الخطوة 1: تأكد من التثبيت الصحيح
```bash
python test_camera.py
```

### الخطوة 2: تحديث إعدادات الكاميرا
1. افتح `bCNC\bCNC.ini`
2. ابحث عن قسم `[Camera]`
3. غير `aligncam = 0` (أو رقم الكاميرا المناسب)
4. احفظ الملف

### الخطوة 3: تشغيل البرنامج
```bash
# من مجلد bCNC
python __main__.py

# أو استخدم الملف المساعد
run_bCNC_with_camera.bat
```

### الخطوة 4: تفعيل الكاميرا في البرنامج
1. اذهب إلى تبويب **Probe**
2. ابحث عن قسم **Camera**
3. اضغط زر **"Switch To"**
4. يجب أن ترى صورة الكاميرا

### الخطوة 5: استخدام المحاذاة بين النقطتين
1. اضغط زر **"Two Point Alignment"** في قسم Camera
2. اتبع التعليمات في النافذة المنبثقة

## 🐛 **حل المشاكل المتقدمة**

### مشكلة: الكاميرا لا تظهر في bCNC
**الحلول**:
1. تأكد من أن الكاميرا تعمل في برامج أخرى
2. جرب أرقام كاميرا مختلفة (0, 1, 2)
3. أغلق أي برامج أخرى تستخدم الكاميرا
4. أعد تشغيل الكمبيوتر

### مشكلة: صورة الكاميرا مقلوبة
**الحل**:
```ini
[Camera]
aligncam_angle = 180  # أو 90 أو 270
```

### مشكلة: الكاميرا بطيئة
**الحلول**:
```ini
[Camera]
aligncam_width = 320   # دقة أقل
aligncam_height = 240
```

### مشكلة: خطأ في الأذونات
**الحل**:
- شغل Command Prompt كمدير
- أو استخدم Virtual Environment

## 📋 **قائمة التحقق النهائية**

- [ ] Python 3.8+ مثبت
- [ ] numpy==1.21.6 مثبت
- [ ] opencv-python==******** مثبت
- [ ] pyserial مثبت
- [ ] Pillow مثبت
- [ ] الكاميرا متصلة ومعرفة في النظام
- [ ] إعدادات الكاميرا محدثة في bCNC.ini
- [ ] البرنامج يعمل بدون أخطاء
- [ ] الكاميرا تظهر في تبويب Probe
- [ ] زر Two Point Alignment مرئي ويعمل

---
**ملاحظة**: إذا استمرت المشاكل، جرب إنشاء Virtual Environment جديد:
```bash
python -m venv bcnc_env
bcnc_env\Scripts\activate
pip install -r requirements.txt
```
