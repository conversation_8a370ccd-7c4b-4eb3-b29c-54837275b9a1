@echo off
echo ========================================
echo   bCNC with Two Point Alignment
echo   Camera-Enabled Version
echo ========================================
echo.

REM Set environment variables for camera
set OPENCV_VIDEOIO_PRIORITY_MSMF=0
set OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS=0

REM Change to bCNC directory
cd /d "%~dp0bCNC"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found in PATH. Trying alternative locations...
    if exist "C:\Python313\python.exe" (
        set PYTHON_CMD=C:\Python313\python.exe
    ) else if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" (
        set PYTHON_CMD=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
    ) else (
        echo Python not found! Please install Python 3.8 or newer.
        pause
        exit /b 1
    )
) else (
    set PYTHON_CMD=python
)

echo Using Python: %PYTHON_CMD%
echo.

REM Set Python path for plugins
set PYTHONPATH=%~dp0bCNC\lib;%~dp0bCNC\plugins;%PYTHONPATH%

echo Starting bCNC with camera support...
echo.
echo Features available:
echo - Two Point Alignment Plugin
echo - Camera Interface
echo - Web Pendant with Camera
echo.
echo To enable camera:
echo 1. Go to Tools ^> Config/Controller
echo 2. Set aligncam = 0 (or your camera number)
echo 3. Set aligncam_width = 640
echo 4. Set aligncam_height = 480
echo 5. Save and restart
echo.

REM Start bCNC
%PYTHON_CMD% __main__.py

echo.
echo bCNC has closed.
pause
