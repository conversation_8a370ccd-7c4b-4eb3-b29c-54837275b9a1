# Dutch translation for bCNC / Nederlandse vertaling voor bCNC
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-19 19:55+0200\n"
"PO-Revision-Date: 2022-07-19 20:30+0200\n"
"Last-Translator: @SteveMoto [GitHub]\n"
"Language-Team: \n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"

#: bCNC/controllers/_GenericGRBL.py:51
msgid ""
"(Compile Option) Grbl '$' setting value exceeds the maximum step rate "
"supported."
msgstr ""
"(Compileer-optie) Grbl '$' instellingswaarde overschrijdt de maximale "
"ondersteunde stapsnelheid."

#: bCNC/controllers/_GenericGRBL.py:55
msgid ""
"(Grbl-Mega Only) Build info or startup line exceeded EEPROM line length "
"limit."
msgstr ""
"(Alleen Grbl-Mega) Bouwinfo of opstartlijn overschrijdt de maximale EEPROM "
"regel lengte."

#: bCNC/controllers/_GenericGRBL.py:69
msgid ""
"A G-code command implicitly or explicitly requires XYZ axis words in the "
"block, but none were detected."
msgstr ""
"Een G-code commando vereist impliciet of expliciet XYZ-as woorden in het "
"blok, maar er is geen één gedetecteerd."

#: bCNC/controllers/_GenericGRBL.py:75
msgid ""
"A G-code command was sent, but is missing some required P or L value words "
"in the line."
msgstr ""
"Een G-code commando werd verzonden, maar het ontbreekt enkele vereiste P- of "
"L-waarde woorden in de regel."

#: bCNC/controllers/_GenericGRBL.py:87
msgid ""
"A G2 or G3 arc was commanded but there are no XYZ axis words in the selected "
"plane to trace the arc."
msgstr ""
"Een G2- of G3-boog werd opgedragen, maar er zijn geen XYZ-as woorden in het "
"geselecteerde vlak om de boog te volgen."

#: bCNC/controllers/_GenericGRBL.py:96
msgid ""
"A G2 or G3 arc, traced with the offset definition, is missing the IJK offset "
"word in the selected plane to trace the arc."
msgstr ""
"Een G2- of G3-boog die getraceerd is met de offsetdefinitie mist het IJK-"
"offsetwoord in het geselecteerde vlak om de boog te volgen."

#: bCNC/controllers/_GenericGRBL.py:93
msgid ""
"A G2 or G3 arc, traced with the radius definition, had a mathematical error "
"when computing the arc geometry. Try either breaking up the arc into semi-"
"circles or quadrants, or redefine them with the arc offset definition."
msgstr ""
"Een G2- of G3-boog die getraceerd is met de straaldefinitie had een "
"wiskundige fout bij het berekenen van de booggeometrie. Probeer de boog in "
"halve cirkels of kwadranten op te delen of herdefinieer ze met de boogoffset."

#: bCNC/ToolsPage.py:824
msgid ""
"Add additional margin/offset around islands to compensate for endmill "
"radius. This is automaticaly done for all islands if they are marked as tabs."
msgstr ""

#: bCNC/ProbePage.py:714
msgid ""
"Add an orientation marker. Jog first the machine to the marker position and "
"then click on canvas to add the marker."
msgstr ""
"Voeg een oriëntatiemarker toe. Jog de machine eerst naar de marker positie "
"en klik dan op het canvas om de marker toe te voegen."

#: bCNC/controllers/_GenericGRBL.py:16
msgid ""
"Alarm is an emergency state. Something has gone terribly wrong when these "
"occur. Typically, they are caused by limit error when the machine has moved "
"or wants to move outside the machine space and crash into something. They "
"also report problems if Grbl is lost and can't guarantee positioning or a "
"probe command has failed. Once in alarm-mode, Grbl will lock out and shut "
"down everything until the user issues a reset. Even after a reset, Grbl will "
"remain in alarm-mode, block all G-code from being executed, but allows the "
"user to override the alarm manually. This is to ensure the user knows and "
"acknowledges the problem and has taken steps to fix or account for it."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:33
msgid ""
"All is good! Everything in the last line was understood by Grbl and was "
"successfully processed and executed."
msgstr ""
"Alles is goed gegaan! Alles in de laatste regel werd begrepen door Grbl en "
"werd met succes verwerkt en uitgevoerd."

#: bCNC/plugins/linearize.py:50
msgid ""
"Also subdivide the lines. Otherwise only arcs and splines will be subdivided"
msgstr ""

#: bCNC/bmain.py:2418
msgid ""
"Autolevel/probe information already exists.\n"
"Delete it?"
msgstr ""
"Autolevel/sonde informatie bestaat al.\n"
"Verwijderen?"

#: bCNC/plugins/slicemesh.py:276
msgid ""
"Calculating distance {} of {} (SciPy not installed => using SLOW fallback "
"method)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:505
msgid ""
"Currently there is some weird behaviour sometimes when trying to link "
"segments of pocket internally, so it can be disabled using this option. This "
"workaround should be fixed and removed in future."
msgstr ""

#: bCNC/ToolsPage.py:797
msgid ""
"Do the last cut once more in opposite direction. Helix bottom is disabled in "
"such case."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:168
msgid ""
"Door closed and resuming. Restoring from park, if applicable. Reset will "
"throw an alarm."
msgstr ""
"Deur gesloten en hervatten voltooid. Herstellen van park indien van "
"toepassing. De reset zal een alarm geven."

#: bCNC/controllers/_GenericGRBL.py:165
#, fuzzy
msgid ""
"Door opened. Hold (or parking retract) in-progress. Reset will throw an "
"alarm."
msgstr ""
"Deur is geopend. Bezig met in bedrijf houden. De reset zal een alarm geven."

#: bCNC/controllers/_GenericGRBL.py:131
#, fuzzy
msgid ""
"G-code motion target exceeds machine travel. Machine position safely "
"retained. Alarm may be unlocked."
msgstr ""
"G-code bewegingsdoel overschrijdt machinereis. Positie van machine is veilig "
"behouden. Het alarm kan worden ontgrendeld."

#: bCNC/bmain.py:2526
#, fuzzy
#| msgid ""
#| "Gcode file {} was changed since editing started\n"
#| "Reload new version?"
msgid ""
"Gcode file {} was changed since editing started\n"
"Reload new version?"
msgstr ""
"Gcode bestand {} was gewijzigd sinds bewerken was gestart\n"
"Nieuwe versie laden?"

#: bCNC/ToolsPage.py:1027
msgid ""
"Generate pocket after profiling? Useful for making pockets with overcuts."
msgstr ""

#: bCNC/plugins/sketch.py:368
#, fuzzy
#| msgid "Generated Sketch size W={} x H={} x distance={}, Total length:{}"
msgid ""
"Generated Sketch size W={} x H={} x distance={}, Total line:{}, Total length:"
"{}"
msgstr "Gegenereerde schetsgrootte W={} H={} x afstand={}, Totale lengte:{}"

#: bCNC/controllers/_GenericGRBL.py:43
msgid ""
"Grbl '$' command cannot be used unless Grbl is IDLE. Ensures smooth "
"operation during a job."
msgstr ""
"Grbl '$' commando kan niet worden gebruikt tenzij Grbl in de rusttoestand "
"verkeert. Het zorgt voor een soepele bediening tijdens een klus."

#: bCNC/controllers/_GenericGRBL.py:19
msgid ""
"Grbl is in g-code check mode. If you send g-code to it, it will only check "
"it without actualy doing any motion. You can exit this by $C command (Or "
"equivalent button in terminal tab)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:26
msgid ""
"Grbl is in queue state. This also means you have relatively old GRBL "
"version, there are even 0.9 versions newer than this."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:23
msgid ""
"Grbl is in sleep mode. Motors are disabled, so you can move them manualy. "
"That also means that your machine might have lost the position (or "
"microsteps) and you may need to re-zero. Perform reset+unlock (or stop) to "
"wake Grbl again."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:78
msgid ""
"Grbl supports six work coordinate systems G54-G59. G59.1, G59.2, and G59.3 "
"are not supported."
msgstr ""
"Grbl ondersteunt werkcoördinatenstelsels G54 tot G59. G59.1, G59.2, en G59.3 "
"worden niet ondersteund."

#: bCNC/controllers/_GenericGRBL.py:128
msgid ""
"Hard limit triggered. Machine position is likely lost due to sudden and "
"immediate halt. Re-homing is highly recommended."
msgstr ""
"Het harde limiet werd bereikt. De machine's positie is waarschijnlijk "
"verloren als gevolg van onverwachte stop. Opnieuw aanwijzen wordt aanbevolen."

#: bCNC/controllers/_GenericGRBL.py:148
msgid ""
"Homing fail. Could not find limit switch within search distance. Defined as "
"1.5 * max_travel on search and 5 * pulloff on locate phases."
msgstr ""
"Homing mislukt. Kon de limietschakelaar niet vinden binnen zoekafstand. Het "
"is gedefinieerd als 1.5 * max_travel bij het zoeken en 5 * pulloff bij "
"lokalisatiefasen."

#: bCNC/controllers/_GenericGRBL.py:158
#, fuzzy
#| msgid ""
#| "Homing fail. Could not find limit switch within search distance. Defined "
#| "as 1.5 * max_travel on search and 5 * pulloff on locate phases."
msgid ""
"Homing fail. Could not find second limit switch for auto squared axis within "
"search distances. Try increasing max travel, decreasing pull-off distance, "
"or check wiring. (grblHAL)"
msgstr ""
"Homing mislukt. Kon de limietschakelaar niet vinden binnen zoekafstand. Het "
"is gedefinieerd als 1.5 * max_travel bij het zoeken en 5 * pulloff bij "
"lokalisatiefasen."

#: bCNC/controllers/_GenericGRBL.py:145
msgid ""
"Homing fail. Cycle failed to clear limit switch when pulling off. Try "
"increasing pull-off setting or check wiring."
msgstr ""
"Homing mislukt. De cyclus heeft de limietschakelaar niet gewist tijdens het "
"uittrekken. Probeer de aftrekinstelling te verhogen of controleer de "
"bedrading."

#: bCNC/bmain.py:231
msgid ""
"MDI Command line: Accept g-code commands or macro commands (RESET/HOME...) "
"or editor commands (move,inkscape, round...) [Space or Ctrl-Space]"
msgstr ""
"MDI commando regel: Accepteer g-code commando's of macro commando's (RESET/"
"HOME) of bewerkingsprogramma commando's (move,inkscape,round...) [Space of "
"Ctrl-Space]"

#: bCNC/plugins/linearize.py:41
msgid ""
"Maximal length of resulting lines, smaller number means more precise output "
"and longer g-code. Length will be automaticaly truncated to be even across "
"whole subdivided segment."
msgstr ""

#: bCNC/ControlPage.py:1045
msgid ""
"Move to Origin.\n"
"User configurable button.\n"
"Right click to configure."
msgstr ""
"Beweeg naar Oorsprong.\n"
"Door gebruiker instelbare knop\n"
"Klik met rechtermuisknop om in te stellen."

#: bCNC/ControlPage.py:609 bCNC/ControlPage.py:916
msgid ""
"No info available.\n"
"Please contact the author."
msgstr ""
"Geen informatie beschikbaar. Neem alstublieft contact op met de auteur."

#: bCNC/plugins/endmilloffset.py:454
msgid ""
"Number of contours (Custom offset count) : indicates the number of contours "
"if custom selected. MAX:"
msgstr ""

#: bCNC/ProbePage.py:904
msgid ""
"Please verify that the probe is connected.\n"
"\n"
"Show this message again?"
msgstr ""
"Verifieer of de sonde is aangesloten.\n"
"\n"
"Dit bericht nog een keer tonen?"

#: bCNC/controllers/_GenericGRBL.py:140
msgid ""
"Probe fail. Probe did not contact the workpiece within the programmed travel "
"for G38.2 and G38.4."
msgstr ""
"Fout met de sonde. De sonde heeft geen contact opgenomen met het werkstuk in "
"het geprogrammeerde gebied voor G38.2 en G38.4."

#: bCNC/controllers/_GenericGRBL.py:137
#, fuzzy
msgid ""
"Probe fail. The probe is not in the expected initial state before starting "
"probe cycle, where G38.2 and G38.3 is not triggered and G38.4 and G38.5 is "
"triggered."
msgstr ""
"Fout met de sonde. De sonde bevindt zich niet in de verwachte begintoestand "
"voordat de cyclus wordt gestart, waarbij G38.2 en G38.3 niet zijn "
"geactiveerd en G38.4 en G38.5 worden geactiveerd."

#: bCNC/controllers/_GenericGRBL.py:134
msgid ""
"Reset while in motion. Grbl cannot guarantee position. Lost steps are "
"likely. Re-homing is highly recommended."
msgstr ""
"Een reset gebeurde tijdens het bewegen. Grbl kan de positie niet garanderen. "
"Er kunnen stappen verloren zijn. Opnieuw aanwijzen wordt sterk aanbevolen."

#: bCNC/ProbePage.py:211
msgid ""
"Set current XY location as autoleveling Z-zero (recalculate probed data to "
"be relative to this XY origin point)"
msgstr ""

#: bCNC/ControlPage.py:251
msgid ""
"Show current state of the machine\n"
"Click to see details\n"
"Right-Click to clear alarm/errors"
msgstr ""
"Laat huidige status van machine zien\n"
"Klik op de details te zien\n"
"Klik met de rechtermuisknop om alarm en fouten te wissen"

#: bCNC/plugins/dragknife.py:91
msgid ""
"Simulation is currently approximated by using lots of short lines. This is "
"the length of these lines."
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:110
msgid ""
"Spindle not running when motion commanded in CSS or spindle sync mode. "
"(grblHAL)"
msgstr ""

#: bCNC/plugins/endmilloffset.py:487 bCNC/plugins/endmilloffset.py:496
msgid ""
"Tabs are always ignored. You can select if all islands are active, none, or "
"only selected"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:102
msgid ""
"The G43.1 dynamic tool length offset command cannot apply an offset to an "
"axis other than its configured axis. The Grbl default axis is the Z-axis."
msgstr ""
"Het dynamische tool lengte offset commando G43.1 kan geen offset toepassen "
"op een andere as dan de geconfigureerde as. De Z-as is de standaard as van "
"Grbl."

#: bCNC/controllers/_GenericGRBL.py:81
msgid ""
"The G53 G-code command requires either a G0 seek or G1 feed motion mode to "
"be active. A different motion was active."
msgstr ""
"Het G53 G-code commando vereist dat een G0 zoek of G1 toevoerbewegingsmodus "
"actief is. Er was een andere beweging actief."

#: bCNC/controllers/_GenericGRBL.py:90
msgid ""
"The motion command has an invalid target. G2, G3, and G38.2 generates this "
"error, if the arc is impossible to generate or if the probe target is the "
"current position."
msgstr ""
"Het beweegcommando heeft een ongeldig doel. G2, G3 en G38.2 veroorzaken deze "
"fout als de boog niet gegenereerd kan worden of als het probe-doel de "
"huidige positie is."

#: bCNC/controllers/_GenericGRBL.py:84
msgid ""
"There are unused axis words in the block and G80 motion mode cancel is "
"active."
msgstr "Er zijn ongebruikte as woorden in het blok en de G80 modus is actief."

#: bCNC/controllers/_GenericGRBL.py:99
msgid ""
"There are unused, leftover G-code words that aren't used by any command in "
"the block."
msgstr ""
"Er bevinden zich ongebruikte en overgebleven G-code woorden die niet door "
"een opdracht in het blok worden gebruikt."

#: bCNC/Utils.py:709
#, fuzzy
#| msgid ""
#| "There was an error sending the report\n"
#| "Code={} {}"
msgid ""
"There was an error sending the report\n"
"Code={} {}"
msgstr ""
"Er trad een probleem op tijdens het versturen van het rapport\n"
"Code={} {}"

#: bCNC/plugins/endmilloffset.py:471
msgid ""
"This can be used to switch between Conventional and Climb milling. If unsure "
"use Convetional (default)."
msgstr ""

#: bCNC/plugins/drillmark.py:23
msgid ""
"This plugin is for creating drilling marks with a laser engraver\n"
"        for manual drilling"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:105
msgid ""
"Tool number greater than max supported value or undefined tool selected. "
"(grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:65
msgid ""
"Two G-code commands that both require the use of the XYZ axis words were "
"detected in the block."
msgstr ""
"Twee G-code commando's die beide het gebruik van de XYZ-as woorden vereisen "
"werden in het blok gedetecteerd."

#: bCNC/plugins/dragknife.py:82
msgid ""
"Use this option to simulate cuting of dragknife path. Resulting shape will "
"reflect what shape will actuall be cut. This should reverse the dragknife "
"procedure and give you back the original shape from g-code that was "
"previously processed for dragknife."
msgstr ""

#: bCNC/bmain.py:2722
msgid ""
"\n"
"Would you like open it locally?"
msgstr ""
"\n"
"Wilt u het lokaal openen?"

#: bCNC/bmain.py:888
msgid ""
"bCNC/\tAn advanced fully featured\n"
"\tg-code sender for GRBL."
msgstr ""
"bCNC/\tEen geavanceerd en volledig\n"
"\tg-code zender voor GRBL."

#: bCNC/plugins/dragknife.py:72
msgid ""
"direction that knife blade is facing before and after cut. Eg.: if you set "
"this to X+, then the knifes rotation axis should be on the right side of the "
"tip. Meaning that the knife is ready to cut towards right immediately "
"without pivoting. If you cut multiple shapes in single operation, it's "
"important to have this set consistently across all of them."
msgstr ""

#: bCNC/plugins/arcfit.py:47
msgid ""
"how precisely must line fit. set to 0 to disable line fitting, but at least "
"some line fitting (0.001 to 0.01) might be needed to fix arcs, so they can "
"be fit"
msgstr ""

#: bCNC/plugins/endmilloffset.py:445
msgid ""
"indicates the number of profile passes (single,custom number,full pocket)"
msgstr ""

#: bCNC/ToolsPage.py:783
msgid ""
"positive value = relative to tool diameter (5 to 10 probably makes sense), "
"negative = absolute ramp distance (you probably don't need this). Also note "
"that ramp can't currently be shorter than affected g-code segment."
msgstr ""

#: bCNC/plugins/dragknife.py:63
msgid ""
"retract to this height for pivots (useful for thick materials, you should "
"enter number slightly lower than material thickness)"
msgstr ""

#: bCNC/CNCCanvas.py:2003
#, fuzzy
#| msgid "     line: {}\n"
msgid "     line: {}\n"
msgstr "     regel: {}\n"

#: bCNC/bmain.py:1195 bCNC/bmain.py:1276
msgid "# Blocks:"
msgstr "# Blokken:"

#: bCNC/TerminalPage.py:182
msgid "$ Display build information of Grbl"
msgstr "$ Laat build informatie van Grbl zien"

#: bCNC/TerminalPage.py:103
msgid "$# Display parameters of Grbl"
msgstr "$# Laat parameters van Grbl zien"

#: bCNC/TerminalPage.py:88
msgid "$$ Display settings of Grbl"
msgstr "$$ Laat instellingen van Grbl zien"

#: bCNC/ToolsPage.py:1173
msgid "$0 Step pulse time [us]"
msgstr ""

#: bCNC/ToolsPage.py:1174
msgid "$1 Step idle delay [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1180
msgid "$10 Status report [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1195
msgid "$100 X steps/mm"
msgstr "$100 X stappen/mm"

#: bCNC/ToolsPage.py:1196
msgid "$101 Y steps/mm"
msgstr "$101 Y stappen/mm"

#: bCNC/ToolsPage.py:1197
msgid "$102 Z steps/mm"
msgstr "$102 Z stappen/mm"

#: bCNC/ToolsPage.py:1181
msgid "$11 Junction deviation [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1198
msgid "$110 X max rate [mm/min]"
msgstr "$110 X maximale snelheid [mm/min]"

#: bCNC/ToolsPage.py:1199
msgid "$111 Y max rate [mm/min]"
msgstr "$111 Y maximale snelheid [mm/min]"

#: bCNC/ToolsPage.py:1200
msgid "$112 Z max rate [mm/min]"
msgstr "$112 Z maximale snelheid [mm/min]"

#: bCNC/ToolsPage.py:1182
msgid "$12 Arc tolerance [mm]"
msgstr "$12 Boog tolerantie [mm]"

#: bCNC/ToolsPage.py:1201
msgid "$120 X acceleration [mm/sec^2]"
msgstr "$120 X versnelling [mm/sec^2]"

#: bCNC/ToolsPage.py:1202
msgid "$121 Y acceleration [mm/sec^2]"
msgstr "$121 Y versnelling [mm/sec^2]"

#: bCNC/ToolsPage.py:1203
msgid "$122 Z acceleration [mm/sec^2]"
msgstr "$122 Z versnelling [mm/sec^2]"

#: bCNC/ToolsPage.py:1183
msgid "$13 Report inches"
msgstr ""

#: bCNC/ToolsPage.py:1204
msgid "$130 X max travel [mm]"
msgstr "$130 X maximale afstand [mm]"

#: bCNC/ToolsPage.py:1205
msgid "$131 Y max travel [mm]"
msgstr "$131 Y maximale afstand [mm]"

#: bCNC/ToolsPage.py:1206
msgid "$132 Z max travel [mm]"
msgstr "$132 Z maximale afstand [mm]"

#: bCNC/ToolsPage.py:1207
msgid "$140 X homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1208
msgid "$141 Y homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1209
msgid "$142 Z homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1175
msgid "$2 Step port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1184
msgid "$20 Soft limits"
msgstr ""

#: bCNC/ToolsPage.py:1185
msgid "$21 Hard limits"
msgstr ""

#: bCNC/ToolsPage.py:1186
msgid "$22 Homing cycle"
msgstr ""

#: bCNC/ToolsPage.py:1187
msgid "$23 Homing direction invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1188
msgid "$24 Homing feed [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1189
msgid "$25 Homing seek [mm/min]"
msgstr ""

#: bCNC/ToolsPage.py:1190
msgid "$26 Homing debounce [ms]"
msgstr ""

#: bCNC/ToolsPage.py:1191
msgid "$27 Homing pull-off [mm]"
msgstr ""

#: bCNC/ToolsPage.py:1176
msgid "$3 Direction port invert [mask]"
msgstr ""

#: bCNC/ToolsPage.py:1192
msgid "$30 Max spindle speed [RPM]"
msgstr "$30 Maximale spoel snelheid [RPM]"

#: bCNC/ToolsPage.py:1193
msgid "$31 Min spindle speed [RPM]"
msgstr "$31 Minimale spoel snelheid [RPM]"

#: bCNC/ToolsPage.py:1194
msgid "$32 Laser mode enable"
msgstr "$32 Lasermodus ingeschakeld"

#: bCNC/ToolsPage.py:1177
msgid "$4 Step enable invert"
msgstr ""

#: bCNC/ToolsPage.py:1178
msgid "$5 Limit pins invert"
msgstr ""

#: bCNC/ToolsPage.py:1179
msgid "$6 Probe pin invert"
msgstr ""

#: bCNC/TerminalPage.py:165
msgid "$C Enable/Disable checking of gcode"
msgstr "$C Schakel controleren van gcode aan/uit"

#: bCNC/TerminalPage.py:117
msgid "$G Display state of Grbl"
msgstr "$G Laat staat van Grbl zien"

#: bCNC/TerminalPage.py:133
msgid "$I Display build information of Grbl"
msgstr "$I Laat build informatie van Grbl zien"

#: bCNC/TerminalPage.py:148
msgid "$N Display startup configuration of Grbl"
msgstr "$N Laat opstartconfiguratie van Grbl zien"

#: bCNC/bmain.py:2450
#, fuzzy
#| msgid "'{}' loaded"
msgid "'{}' loaded"
msgstr "'{}' geladen"

#: bCNC/bmain.py:2446
#, fuzzy
#| msgid "'{}' reloaded at '{}'"
msgid "'{}' reloaded at '{}'"
msgstr "'{}' was herladen bij '{}'"

#: bCNC/bmain.py:2459
#, fuzzy
#| msgid "'{}' saved"
msgid "'{}' saved"
msgstr "'{}' opgeslagen"

#: bCNC/EditorPage.py:376
msgid "(Un)Comment selected lines"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:120
msgid "(grblHAL internal)"
msgstr ""

#: bCNC/ControlPage.py:1005 bCNC/ControlPage.py:1482
msgid "+"
msgstr ""

#: bCNC/ControlPage.py:1174 bCNC/ControlPage.py:1646
msgid "-"
msgstr ""

#: bCNC/plugins/slicemesh.py:58
msgid ".STL/.PLY file to slice"
msgstr ""

#: bCNC/ProbePage.py:1742
msgid "1. Spindle"
msgstr "1. Spoel"

#: bCNC/ProbePage.py:1748
msgid "2. Camera"
msgstr "2. Camera"

#: bCNC/plugins/slicemesh.py:67
msgid "3D slice (devel)"
msgstr ""

#: bCNC/CNCCanvas.py:2001
#, fuzzy
#| msgid ">>> ERROR: {}\n"
msgid ">>> ERROR: {}\n"
msgstr ">>> FOUT: {}\n"

#: bCNC/ControlPage.py:1407
msgid "A"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:67
msgid "A G-code word was repeated in the block."
msgstr "Een G-code woord werd herhaald in het blok."

#: bCNC/ControlPage.py:651
#, fuzzy
#| msgid "X work position (click to set)"
msgid "A work position (click to set)"
msgstr "X werkpositie (klik op in te stellen)"

#: bCNC/ControlPage.py:727
msgid "A=0"
msgstr ""

#: bCNC/ControlPage.py:793
msgid "ABC=0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:117
msgid "ATC: current tool is not set. Set current tool with M61. (grblHAL)"
msgstr ""

#: bCNC/FilePage.py:216
msgid "About"
msgstr "Over"

#: bCNC/FilePage.py:224
msgid "About the program"
msgstr "Over het programma"

#: bCNC/bmain.py:847
#, fuzzy
#| msgid "About {}"
msgid "About {} v{}"
msgstr "Over {}"

#: bCNC/ToolsPage.py:641
msgid "Acceleration x"
msgstr "Versnelling x"

#: bCNC/ToolsPage.py:642
msgid "Acceleration y"
msgstr "Versnelling y"

#: bCNC/ToolsPage.py:643
msgid "Acceleration z"
msgstr "Versnelling x"

#: bCNC/EditorPage.py:335
msgid "Active"
msgstr "Actief"

#: bCNC/plugins/trochoidPath.py:48 bCNC/plugins/trochoidal_3D.py:72
msgid "Adaptative"
msgstr ""

#: bCNC/EditorPage.py:258 bCNC/ProbePage.py:702 bCNC/ToolsPage.py:1472
msgid "Add"
msgstr "Voeg toe"

#: bCNC/ToolsPage.py:1478
msgid "Add a new operation/object"
msgstr "Voeg een nieuwe opdracht/object toe"

#: bCNC/CNCCanvas.py:468
msgid "Add an orientation marker"
msgstr "Voeg een oriëntatiemarkering toe"

#: bCNC/plugins/zigzag.py:108
msgid "Additional length at start/end"
msgstr "Extra lengte bij start/einde"

#: bCNC/plugins/endmilloffset.py:478
#, fuzzy
#| msgid "Additional offset distance"
msgid "Additional offset (mm)"
msgstr "Extra afstandscompensatie"

#: bCNC/plugins/trochoidPath.py:41 bCNC/plugins/trochoidal_3D.py:65
#: bCNC/ToolsPage.py:1013
msgid "Additional offset distance"
msgstr "Extra afstandscompensatie"

#: bCNC/ProbePage.py:2111
msgid "After a tool change distance to scan starting from ProbeZ"
msgstr ""

#: bCNC/ToolsPage.py:617
msgid "Align Camera"
msgstr "Camera uitlijnen"

#: bCNC/ToolsPage.py:620
msgid "Align Camera Angle"
msgstr "Camerahoek uitlijnen"

#: bCNC/ToolsPage.py:619
msgid "Align Camera Height"
msgstr "Camerahoogte uitlijnen"

#: bCNC/ToolsPage.py:618
msgid "Align Camera Width"
msgstr "Camerabreedte uitlijnen"

#: bCNC/ProbePage.py:831
msgid "Align GCode with the machine markers"
msgstr "Lijn GCode uit met de machine's markeringen"

#: bCNC/EditorPage.py:106 bCNC/bmain.py:140
msgid "All"
msgstr "Alle"

#: bCNC/bmain.py:1235
msgid "All GCode"
msgstr "Alle GCode"

#: bCNC/bmain.py:119
msgid "All accepted"
msgstr "Alles geaccepteerd"

#: bCNC/bmain.py:2609
msgid "Already running"
msgstr "Al aan het uitvoeren"

#: bCNC/ToolsPage.py:717
msgid "Angle"
msgstr "Hoek"

#: bCNC/ProbePage.py:808
msgid "Angle:"
msgstr "Hoek:"

#: bCNC/plugins/spiral.py:481
msgid "Approach Height (Safe Z)"
msgstr ""

#: bCNC/plugins/arcfit.py:11
msgid "ArcFit"
msgstr ""

#: bCNC/ProbePage.py:956
msgid "At least one probe direction should be specified"
msgstr "Ten minste één sonde richting zou gedefinieerd moeten worden"

#: bCNC/EditorPage.py:198 bCNC/ProbePage.py:128 bCNC/ProbePage.py:254
#: bCNC/ProbePage.py:1224
msgid "Autolevel"
msgstr "Autolevel"

#: bCNC/ProbePage.py:135
msgid "Autolevel Z surface"
msgstr "Autolevel Z vlak"

#: bCNC/ProbePage.py:573
msgid "Automatic GOTO after probing"
msgstr ""

#: bCNC/Utils.py:602
msgid "Automatic error reporting"
msgstr "Automatische foutrapportage"

#: bCNC/plugins/midi2cnc.py:70
msgid "Axis to be used"
msgstr "As om te gebruiken"

#: bCNC/ControlPage.py:1491
msgid "B"
msgstr ""

#: bCNC/ControlPage.py:668
#, fuzzy
#| msgid "X work position (click to set)"
msgid "B work position (click to set)"
msgstr "X werkpositie (klik op in te stellen)"

#: bCNC/ControlPage.py:743
msgid "B=0"
msgstr ""

#: bCNC/ControlPage.py:777
msgid "BC=0"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "BEFORE & AFTER probing"
msgstr "VOOR  & NA het sonderen"

#: bCNC/FilePage.py:316
msgid "Baud:"
msgstr "Baud:"

#: bCNC/EditorPage.py:250
msgid "Block"
msgstr "Blok"

#: bCNC/controllers/_GenericGRBL.py:126
msgid "Bluetooth initalisation failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/jigsaw.py:362
msgid "Board height"
msgstr "Bordhoogte"

#: bCNC/plugins/jigsaw.py:361
msgid "Board width"
msgstr "Bordbreedte"

#: bCNC/EditorPage.py:464
msgid "Bottom"
msgstr "Onder"

#: bCNC/EditorPage.py:461
msgid "Bottom-Left"
msgstr "Linksonder"

#: bCNC/EditorPage.py:467
msgid "Bottom-Right"
msgstr "Rechtsonder"

#: bCNC/plugins/bowl.py:14
msgid "Bowl"
msgstr "Kom"

#: bCNC/plugins/box.py:19
msgid "Box"
msgstr "Doos"

#: bCNC/TerminalPage.py:215
msgid "Buffered commands"
msgstr ""

#: bCNC/TerminalPage.py:126
msgid "Build"
msgstr "Build"

#: bCNC/plugins/drillmark.py:49
msgid "Burn power for drillmark"
msgstr ""

#: bCNC/plugins/drillmark.py:48
msgid "Burn time for drillmark"
msgstr ""

#: bCNC/ControlPage.py:1410
#, fuzzy
#| msgid "CW"
msgid "C"
msgstr "CW"

#: bCNC/ControlPage.py:685
#, fuzzy
#| msgid "X work position (click to set)"
msgid "C work position (click to set)"
msgstr "X werkpositie (klik op in te stellen)"

#: bCNC/ControlPage.py:759
msgid "C=0"
msgstr ""

#: bCNC/ToolsPage.py:1522 bCNC/ToolsPage.py:2090
msgid "CAM"
msgstr ""

#: bCNC/EditorPage.py:596
msgid "CCW"
msgstr "CCW"

#: bCNC/ProbePage.py:507
msgid "CIRCLE"
msgstr ""

#: bCNC/ControlPage.py:2363
msgid "CNC communication and control"
msgstr "CNC communicatie en controle"

#: bCNC/bmain.py:636
msgid "CNC is currently running, please stop it before."
msgstr "CNC is momenteel aan het uitvoeren, stop het a.u.b. van tevoren."

#: bCNC/ToolsPage.py:926
msgid "CUT selected paths"
msgstr "Verwijder geselecteerde paden"

#: bCNC/EditorPage.py:566
msgid "CW"
msgstr "CW"

#: bCNC/ProbePage.py:1926 bCNC/ProbePage.py:2132
msgid "Calibrate"
msgstr "Kalibreer"

#: bCNC/ProbePage.py:2121
msgid "Calibration:"
msgstr "Kalibratie:"

#: bCNC/ProbePage.py:142 bCNC/ProbePage.py:1630 bCNC/ToolsPage.py:1870
msgid "Camera"
msgstr "Camera"

#: bCNC/ToolsPage.py:1878
msgid "Camera Configuration"
msgstr "Camera configuratie"

#: bCNC/ProbePage.py:1701
msgid "Camera cross hair diameter [units]"
msgstr "Camera aanwijzer diameter [eenheden]"

#: bCNC/ProbePage.py:1643
msgid "Camera location inside canvas"
msgstr "Camera locatie in het canvas"

#: bCNC/ProbePage.py:1718 bCNC/ProbePage.py:1727
msgid "Camera offset from gantry"
msgstr "Camera compensatie van portiek"

#: bCNC/ProbePage.py:1862
msgid "Camera offset is updated"
msgstr "Camera compensatie is geupdate"

#: bCNC/ProbePage.py:1655
msgid "Camera rotation [degrees]"
msgstr "Camera omwenteling [graden]"

#: bCNC/ProbePage.py:1688
msgid "Camera scale [pixels / unit]"
msgstr "Camera schaal [pixels / eenheid]"

#: bCNC/lib/bFileDialog.py:361 bCNC/Utils.py:876
msgid "Cancel"
msgstr "Annuleer"

#: bCNC/lib/bFileDialog.py:582
#, fuzzy
#| msgid "Cannot access path \"{}\""
msgid "Cannot access path \"{}\""
msgstr "Kan het pad niet bereiken: \"{}\""

#: bCNC/plugins/sketch.py:50
msgid "Casual first point"
msgstr ""

#: bCNC/plugins/halftone.py:47
msgid "Cell size"
msgstr "Celgrootte"

#: bCNC/plugins/center.py:12 bCNC/EditorPage.py:463 bCNC/ProbePage.py:646
#: bCNC/ProbePage.py:662
msgid "Center"
msgstr "Centrum"

#: bCNC/plugins/simpleArc.py:65
#, fuzzy
#| msgid "Center"
msgid "Center X"
msgstr "Centrum"

#: bCNC/plugins/function_plot.py:28
msgid "Center X coordinate"
msgstr ""

#: bCNC/plugins/simpleArc.py:66
#, fuzzy
#| msgid "Center"
msgid "Center Y"
msgstr "Centrum"

#: bCNC/plugins/function_plot.py:29
msgid "Center Y coordinate"
msgstr ""

#: bCNC/ProbePage.py:671
msgid "Center probing using a ring"
msgstr "Centrum sonde die een ring gebruikt"

#: bCNC/ProbePage.py:1944
msgid "Change"
msgstr "Verander"

#: bCNC/EditorPage.py:732
msgid "Change cut direction to CCW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:715
msgid "Change cut direction to CW for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:682
msgid "Change cut direction to climb for selected gcode blocks"
msgstr ""

#: bCNC/EditorPage.py:665
msgid "Change cut direction to conventional for selected gcode blocks"
msgstr ""

#: bCNC/ToolsPage.py:1810
msgid "Change program language restart is required"
msgstr "Verander interface taal (herstart vereist)"

#: bCNC/CNCCanvas.py:2250
msgid "Change viewing angle"
msgstr "Wijzig kijkhoek"

#: bCNC/ProbePage.py:2011
msgid "Change:"
msgstr "Verander:"

#: bCNC/plugins/halftone.py:43 bCNC/plugins/sketch.py:57
msgid "Channel to analyze"
msgstr "Kanaal om te analyseren"

#: bCNC/Updates.py:86
msgid "Check Interval"
msgstr "Controleer Interval"

#: bCNC/Updates.py:135
msgid "Check Now"
msgstr "Controleer Nu"

#: bCNC/FilePage.py:211
msgid "Check Updates"
msgstr "Controleer voor updates"

#: bCNC/TerminalPage.py:157
msgid "Check gcode"
msgstr "Controleer gcode"

#: bCNC/Updates.py:142
msgid "Check the web site for new versions of bCNC"
msgstr "Controleer de website voor nieuwe versies van bCNC"

#: bCNC/lib/bFileDialog.py:998
msgid "Choose Directory"
msgstr "Kies een Map"

#: bCNC/ProbePage.py:530
msgid "Circle radius"
msgstr ""

#: bCNC/plugins/trochoidal.py:46
msgid "Circular"
msgstr ""

#: bCNC/plugins/gear.py:163
msgid "Circular Pitch"
msgstr "Circulaire toonhoogte"

#: bCNC/ProbePage.py:224 bCNC/ProbePage.py:794 bCNC/TerminalPage.py:45
msgid "Clear"
msgstr "Wis"

#: bCNC/ControlPage.py:479
msgid "Clear Message"
msgstr "Wis Bericht"

#: bCNC/ProbePage.py:230
msgid "Clear probe data"
msgstr "Wis sonder gegevens"

#: bCNC/TerminalPage.py:50
msgid "Clear terminal"
msgstr "Wis terminal"

#: bCNC/CNCCanvas.py:440
msgid "Click to set the origin (zero)"
msgstr "Klik op oorsprong in te stellen (nul)"

#: bCNC/EditorPage.py:674
msgid "Climb"
msgstr "Klim"

#: bCNC/plugins/endmilloffset.py:469
msgid "Climb milling"
msgstr ""

#: bCNC/EditorPage.py:40
msgid "Clipboard"
msgstr "Klembord"

#: bCNC/plugins/trochoidal.py:45 bCNC/plugins/trochoidal_3D.py:92
msgid "Clockwise"
msgstr ""

#: bCNC/EditorPage.py:276 bCNC/ToolsPage.py:1489
msgid "Clone"
msgstr "Kloon"

#: bCNC/EditorPage.py:282
msgid "Clone selected lines or blocks [Ctrl-D]"
msgstr "Kloon geselecteerde regels of blokken [Ctrl-D]"

#: bCNC/ToolsPage.py:1495
msgid "Clone selected operation/object"
msgstr "Kloon de geselecteerde opdracht/object"

#: bCNC/FilePage.py:267 bCNC/Updates.py:126 bCNC/Utils.py:613
#: bCNC/bmain.py:1085 bCNC/bmain.py:1320 bCNC/bmain.py:2554
msgid "Close"
msgstr "Sluiten"

#: bCNC/plugins/text.py:42
msgid "Close Contours"
msgstr ""

#: bCNC/FilePage.py:280
msgid "Close program [Ctrl-Q]"
msgstr "Sluit programma af [Ctrl-Q]"

#: bCNC/plugins/closepath.py:17
msgid "Close the path"
msgstr ""

#: bCNC/plugins/closepath.py:11
msgid "ClosePath"
msgstr ""

#: bCNC/ToolsPage.py:712
msgid "Coating"
msgstr "Coating"

#: bCNC/CNCList.py:749 bCNC/EditorPage.py:203
msgid "Color"
msgstr "Kleur"

#: bCNC/ToolsPage.py:1934
msgid "Colors"
msgstr "Kleuren"

#: bCNC/Utils.py:861 bCNC/bmain.py:215
msgid "Command:"
msgstr "Commando:"

#: bCNC/TerminalPage.py:61
msgid "Commands"
msgstr "Commando's"

#: bCNC/EditorPage.py:370 bCNC/ToolsPage.py:681 bCNC/ToolsPage.py:708
#: bCNC/ToolsPage.py:738
msgid "Comment"
msgstr "Opmerking"

#: bCNC/ProbePage.py:295
msgid "Common"
msgstr "Algemeen"

#: bCNC/ToolsPage.py:822
msgid "Compensate islands for cutter radius"
msgstr ""

#: bCNC/ToolsPage.py:1795 bCNC/ToolsPage.py:1821
msgid "Config"
msgstr "Configuratie"

#: bCNC/FilePage.py:348
msgid "Connect on startup"
msgstr "Verbind bij opstarten"

#: bCNC/FilePage.py:352
msgid "Connect to serial on startup of the program"
msgstr "Verbind met seriële bij het opstarten van het programma"

#: bCNC/ControlPage.py:82 bCNC/ControlPage.py:122
msgid "Connection"
msgstr "Verbinding"

#: bCNC/controllers/_GenericGRBL.py:31
msgid "Connection is established with Grbl"
msgstr "Verbinding is gemaakt met Grbl"

#: bCNC/ControlPage.py:927 bCNC/ControlPage.py:2364
msgid "Control"
msgstr "Controle"

#: bCNC/ToolsPage.py:591 bCNC/ToolsPage.py:592 bCNC/ToolsPage.py:593
#: bCNC/ToolsPage.py:594 bCNC/ToolsPage.py:595 bCNC/ToolsPage.py:596
#: bCNC/ToolsPage.py:597 bCNC/ToolsPage.py:598 bCNC/ToolsPage.py:599
#: bCNC/ToolsPage.py:600 bCNC/ToolsPage.py:601 bCNC/ToolsPage.py:602
msgid "Control-"
msgstr "Ctrl-"

#: bCNC/ToolsPage.py:1854
msgid "Controller"
msgstr "Controller"

#: bCNC/ToolsPage.py:1862
msgid "Controller (GRBL) configuration"
msgstr "Controller (GRBL) configuratie"

#: bCNC/bmain.py:202
msgid "Controller buffer fill"
msgstr "Controller buffer vullen"

#: bCNC/FilePage.py:330
msgid "Controller:"
msgstr "Controller:"

#: bCNC/EditorPage.py:655
msgid "Conventional"
msgstr "Conventioneel"

#: bCNC/ControlPage.py:2149
msgid "Coolant:"
msgstr ""

#: bCNC/EditorPage.py:80
msgid "Copy"
msgstr "Copy"

#: bCNC/EditorPage.py:86
msgid "Copy [Ctrl-C]"
msgstr "Copy [Ctrl-C]"

#: bCNC/plugins/simpleRectangle.py:96
#, fuzzy
#| msgid "Internal Radius"
msgid "Corner Radius"
msgstr "Interne Radius"

#: bCNC/plugins/zigzag.py:110
msgid "Corner resolution"
msgstr "Hoek resolutie"

#: bCNC/plugins/simpleArc.py:89
#, fuzzy
#| msgid "Create Spur GEAR"
msgid "Create Simple Arc"
msgstr "Genereer tandwiel met rechte vertanding"

#: bCNC/plugins/simpleDrill.py:104
msgid "Create Simple Drill"
msgstr ""

#: bCNC/plugins/simpleLine.py:73
msgid "Create Simple Line"
msgstr ""

#: bCNC/plugins/simpleRectangle.py:118
msgid "Create Simple Rectangle"
msgstr ""

#: bCNC/plugins/gear.py:178
msgid "Create Spur GEAR"
msgstr "Genereer tandwiel met rechte vertanding"

#: bCNC/plugins/hilbert.py:104
#, fuzzy
msgid "Create a Hilbert path"
msgstr "Maak een Hilbert pad"

#: bCNC/plugins/zigzag.py:98
msgid "Create a Zig-Zag path"
msgstr "Maak een Zig-Zag pad"

#: bCNC/plugins/spirograph.py:111
msgid "Create a spirograph path"
msgstr "Maak een spirograaf pad"

#: bCNC/plugins/trochoidPath.py:29
msgid "Create a trochoid rute along selected blocks"
msgstr ""

#: bCNC/plugins/pyrograph.py:29
msgid "Create a variable feed path based upon image brightness"
msgstr ""

#: bCNC/plugins/box.py:393
msgid "Create finger BOX"
msgstr "Maak vingerdoos"

#: bCNC/plugins/halftone.py:29
msgid "Create halftone pattern from a picture"
msgstr "Maak halftone patroon van een afbeelding"

#: bCNC/plugins/driller.py:36
msgid "Create holes along selected blocks"
msgstr "Maak gaten langs geselecteerde blokken"

#: bCNC/plugins/sketch.py:28
msgid "Create sketch based on picture brightness"
msgstr "Maak een schets met behulp van helderheid van afbeelding"

#: bCNC/ToolsPage.py:1162
msgid "Create tabs on blocks"
msgstr "Maak tabs op blokken"

#: bCNC/plugins/text.py:29
msgid "Create text using a ttf font"
msgstr "Maak tekst met behulp van een TFF-lettertype"

#: bCNC/plugins/trochoidal_3D.py:38
#, fuzzy
#| msgid "Create holes along selected blocks"
msgid "Create trochoids along selected blocks"
msgstr "Maak gaten langs geselecteerde blokken"

#: bCNC/ProbePage.py:1692
msgid "Crosshair:"
msgstr "Aanwijzer:"

#: bCNC/plugins/endmilloffset.py:452
msgid "Custom offset count"
msgstr ""

#: bCNC/plugins/box.py:356 bCNC/EditorPage.py:64 bCNC/ToolsPage.py:1530
msgid "Cut"
msgstr "Knip"

#: bCNC/plugins/heightmap.py:75
msgid "Cut Border"
msgstr "Snij Rand"

#: bCNC/EditorPage.py:724
msgid "Cut CCW"
msgstr ""

#: bCNC/EditorPage.py:707
msgid "Cut CW"
msgstr ""

#: bCNC/plugins/trochoidPath.py:38
#, fuzzy
#| msgid "Diameter"
msgid "Cut Diameter"
msgstr "Diameter"

#: bCNC/plugins/flatten.py:298
msgid "Cut Direction"
msgstr "Snijrichting"

#: bCNC/plugins/spiral.py:482
msgid "Cut Pattern"
msgstr ""

#: bCNC/plugins/heightmap.py:74
msgid "Cut Top"
msgstr "Snij Top"

#: bCNC/EditorPage.py:70
msgid "Cut [Ctrl-X]"
msgstr "Knip [Ctrl-X]"

#: bCNC/ToolsPage.py:829
msgid "Cut contours of selected islands"
msgstr ""

#: bCNC/ToolsPage.py:1539
msgid "Cut for the full stock thickness selected code"
msgstr ""

#: bCNC/plugins/spiral.py:483
#, fuzzy
#| msgid "Cut Direction"
msgid "Cut in Both Directions"
msgstr "Snijrichting"

#: bCNC/ToolsPage.py:775
msgid "Cutting strategy"
msgstr ""

#: bCNC/ToolsPage.py:987
msgid "DRILL selected points"
msgstr "Geselecteerde DRILL punten"

#: bCNC/ToolsPage.py:655
msgid "DRO Zero padding"
msgstr ""

#: bCNC/ToolsPage.py:1397
msgid "Database"
msgstr "Gegevensbank"

#: bCNC/lib/bFileDialog.py:307
msgid "Date"
msgstr "Datum"

#: bCNC/Updates.py:101
#, fuzzy
#| msgid "Date of last checking"
msgid "Date last checked"
msgstr "Datum van laatste controle"

#: bCNC/Updates.py:119
msgid "Days-interval to remind again for checking"
msgstr "Herinnering-interval in dagen om opnieuw te checken"

#: bCNC/ToolsPage.py:650
msgid "Decimal digits"
msgstr "Decimale cijfers"

#: bCNC/ControlPage.py:1176 bCNC/ControlPage.py:1648
msgid "Decrease step by 1 unit"
msgstr "Verminder stap met 1 eenheid"

#: bCNC/EditorPage.py:292 bCNC/ProbePage.py:750 bCNC/ToolsPage.py:1506
msgid "Delete"
msgstr "Verwijder"

#: bCNC/ProbePage.py:803 bCNC/ProbePage.py:1061
msgid "Delete all markers"
msgstr "Verwijder alle markeringen"

#: bCNC/ProbePage.py:1497
msgid "Delete autolevel information"
msgstr "Verwijder autolevel informatie"

#: bCNC/ProbePage.py:759
msgid "Delete current marker"
msgstr "Verwijder huidige marker"

#: bCNC/EditorPage.py:298
msgid "Delete selected lines or blocks [Del]"
msgstr "Verwijder geselecteerde regels of blokken [Del]"

#: bCNC/ToolsPage.py:1512
msgid "Delete selected operation/object"
msgstr "Verwijder geselecteerde opdracht/object toe"

#: bCNC/plugins/hilbert.py:114 bCNC/plugins/zigzag.py:111
msgid "Depth"
msgstr "Diepte"

#: bCNC/plugins/box.py:349
msgid "Depth Dy"
msgstr "Diepte Dy"

#: bCNC/plugins/trochoidPath.py:55 bCNC/plugins/trochoidal_3D.py:89
#: bCNC/ToolsPage.py:684 bCNC/ToolsPage.py:768
msgid "Depth Increment"
msgstr "Toename van diepte"

#: bCNC/plugins/spiral.py:478
#, fuzzy
#| msgid "Depth to flatten"
msgid "Depth to Reduce"
msgstr "Diepte om af te platten"

#: bCNC/plugins/flatten.py:293
msgid "Depth to flatten"
msgstr "Diepte om af te platten"

#: bCNC/plugins/bowl.py:113 bCNC/ToolsPage.py:713
msgid "Diameter"
msgstr "Diameter"

#: bCNC/plugins/Helical_Descent.py:83
#, fuzzy
msgid "Diameter Cut"
msgstr "Diameter"

#: bCNC/ProbePage.py:649
msgid "Diameter:"
msgstr "Diameter:"

#: bCNC/plugins/difference.py:18
msgid "Difference"
msgstr ""

#: bCNC/plugins/jigsaw.py:365
msgid "Difference between pieces"
msgstr "Verschil tussen stukken"

#: bCNC/plugins/difference.py:24
msgid "Difference of two shapes"
msgstr ""

#: bCNC/plugins/trochoidPath.py:40 bCNC/plugins/trochoidal_3D.py:60
#: bCNC/plugins/pyrograph.py:46 bCNC/ToolsPage.py:1010
msgid "Direction"
msgstr "Richting"

#: bCNC/bmain.py:1584
msgid "Direction command error"
msgstr "Richting commando fout"

#: bCNC/lib/bFileDialog.py:275
msgid "Directory:"
msgstr "Map:"

#: bCNC/EditorPage.py:327
msgid "Disable"
msgstr "Uitschakelen"

#: bCNC/ToolsPage.py:942
msgid "Distance (mm)"
msgstr "Afstand (mm)"

#: bCNC/ControlPage.py:1921
msgid "Distance Mode [G90,G91]"
msgstr "Afstandsmodus [G90,G91]"

#: bCNC/plugins/driller.py:45
msgid "Distance between holes"
msgstr "Afstand tussen gaten"

#: bCNC/ControlPage.py:1910 bCNC/ProbePage.py:2103
msgid "Distance:"
msgstr "Afstand:"

#: bCNC/ControlPage.py:1170 bCNC/ControlPage.py:1642
msgid "Divide step by 10"
msgstr "Deel stap door 10"

#: bCNC/ProbePage.py:1498
msgid "Do you want to delete all autolevel in formation?"
msgstr "Wilt u alle autolevel informatie verwijderen?"

#: bCNC/ProbePage.py:1062
msgid "Do you want to delete all orientation markers?"
msgstr "Wilt u alle oriëntatiemarkeringen verwijderen?"

#: bCNC/controllers/_GenericGRBL.py:162
msgid "Door closed. Ready to resume."
msgstr "Deur gesloten. Klaar om te hervatten."

#: bCNC/ToolsPage.py:639
msgid "Double Size Icon"
msgstr ""

#: bCNC/EditorPage.py:524
msgid "Down"
msgstr "Omlaag"

#: bCNC/Updates.py:182
msgid "Download"
msgstr "Download"

#: bCNC/CNCCanvas.py:463
msgid "Drag a ruler to measure distances"
msgstr "Gebruik een liniaal om afstanden te meten"

#: bCNC/plugins/dragknife.py:25
msgid "Drag knife postprocessor"
msgstr ""

#: bCNC/plugins/dragknife.py:19
msgid "DragKnife"
msgstr ""

#: bCNC/plugins/halftone.py:51 bCNC/plugins/sketch.py:49
#: bCNC/plugins/pyrograph.py:47
msgid "Draw border"
msgstr "Teken rand"

#: bCNC/plugins/function_plot.py:35
msgid "Draw coordinate system?"
msgstr ""

#: bCNC/CNCCanvas.py:2432
msgid "Draw timeout in seconds"
msgstr "Teken timeout in seconden"

#: bCNC/CNCCanvas.py:2335
msgid "Draw:"
msgstr "Teken:"

#: bCNC/ToolsPage.py:1621
msgid "Drill"
msgstr "Drill"

#: bCNC/ToolsPage.py:938
msgid "Drill in center only"
msgstr ""

#: bCNC/plugins/drillmark.py:44
msgid "Drill mark size"
msgstr ""

#: bCNC/plugins/driller.py:20
msgid "Driller"
msgstr "Driller"

#: bCNC/plugins/driller.py:276
msgid "Driller abort: Distance must be > 0"
msgstr "Driller fout: Afstand moet groter dan 0 zijn"

#: bCNC/plugins/driller.py:285
msgid "Driller abort: Dwell time >= 0, here time runs only forward!"
msgstr "Driller fout: Dwell tijd moet groter of gelijk aan nul zijn!"

#: bCNC/plugins/driller.py:293
msgid "Driller abort: Excellon-File not a file"
msgstr ""

#: bCNC/plugins/driller.py:280
msgid "Driller abort: Peck must be >= 0"
msgstr "Driller fout: Pik moet groter of gelijk aan nul zijn"

#: bCNC/plugins/driller.py:303
msgid "Driller abort: Please select some path"
msgstr "Driller fout: Selecteer alstublieft een pad"

#: bCNC/plugins/Helical_Descent.py:86
msgid "Drop by lap"
msgstr ""

#: bCNC/ToolsPage.py:941
msgid "Dwell (s)"
msgstr ""

#: bCNC/plugins/simpleDrill.py:71
#, fuzzy
#| msgid "Dwell time, 0 means None"
msgid "Dwell time (s)"
msgstr "Dwell tijd, 0 betekent Geen"

#: bCNC/plugins/driller.py:48
msgid "Dwell time, 0 means None"
msgstr "Dwell tijd, 0 betekent Geen"

#: bCNC/controllers/_GenericGRBL.py:41
msgid "EEPROM read failed. Reset and restored to default values."
msgstr ""
"Het lezen van de EEPROM is mislukt. Het is gereset en terug gezet naar "
"standaardwaarden."

#: bCNC/CNCCanvas.py:525
msgid "ERROR: Cannot set X-Y marker  with the current view"
msgstr "FOUT: Kan de X-Y markering niet instellen met de huidige weergave"

#: bCNC/controllers/_GenericGRBL.py:150
msgid "EStop asserted. Clear and reset (grblHAL)"
msgstr ""

#: bCNC/ProbePage.py:1560
msgid "Edge Detection"
msgstr "Rand Detectie"

#: bCNC/EditorPage.py:194
msgid "Edit"
msgstr "Wijzig"

#: bCNC/ToolsPage.py:1461
msgid "Edit name of current operation/object"
msgstr "Verander naam van de huidige opdracht/object"

#: bCNC/ToolsPage.py:1445
msgid "Editable database of EndMills properties"
msgstr "Wijzigbare gegevensbank van vingerfrees eigenschappen"

#: bCNC/ToolsPage.py:1429
msgid "Editable database of material properties"
msgstr "Wijzigbare gegevensbank van materiaaleigenschappen"

#: bCNC/EditorPage.py:804
msgid "Editor"
msgstr "Bewerker"

#: bCNC/controllers/_GenericGRBL.py:119
msgid "Emergency stop active. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2657
msgid "Empty gcode"
msgstr "Lege gcode"

#: bCNC/EditorPage.py:322
msgid "Enable"
msgstr "Inschakelen"

#: bCNC/ToolsPage.py:640
msgid "Enable 6 Axis Displays"
msgstr ""

#: bCNC/EditorPage.py:342
msgid "Enable or disable blocks of gcode"
msgstr "Blokken gcode in- of uitschakelen"

#: bCNC/bmain.py:1154
msgid "Enabled GCode"
msgstr "Ingeschakelde GCode"

#: bCNC/plugins/simpleArc.py:69
msgid "End Angle in Degrees "
msgstr ""

#: bCNC/plugins/Helical_Descent.py:84 bCNC/plugins/endmilloffset.py:438
#: bCNC/plugins/trochoidPath.py:43 bCNC/plugins/trochoidal_3D.py:48
#: bCNC/ToolsPage.py:1003 bCNC/ToolsPage.py:1074 bCNC/ToolsPage.py:1437
msgid "End Mill"
msgstr "Frees"

#: bCNC/plugins/Helical_Descent.py:100
msgid "End in the Deep"
msgstr ""

#: bCNC/bmain.py:1953
#, fuzzy
#| msgid "EndMill: {} {:g}"
msgid "EndMill: {} {}"
msgstr "Vingerfrees: {} {:g}"

#: bCNC/plugins/Helical_Descent.py:96
msgid "Entry and Exit"
msgstr ""

#: bCNC/lib/bFileDialog.py:582 bCNC/lib/bFileDialog.py:655
#: bCNC/lib/bFileDialog.py:879 bCNC/lib/bFileDialog.py:907
#: bCNC/lib/bFileDialog.py:931
msgid "Error"
msgstr "Fout"

#: bCNC/Utils.py:565
msgid "Error Reporting"
msgstr "Foutmelding"

#: bCNC/lib/bFileDialog.py:880
#, fuzzy
#| msgid "Error creating folder \"{}\""
msgid "Error creating folder \"{}\""
msgstr "Fout bij het maken van map \"{}\""

#: bCNC/lib/bFileDialog.py:931
#, fuzzy
#| msgid "Error deleting file \"{}\""
msgid "Error deleting file \"{}\""
msgstr "Fout bij het verwijderen van bestand \"{}\""

#: bCNC/lib/bFileDialog.py:655
#, fuzzy
#| msgid "Error listing folder \"{}\""
msgid "Error listing folder \"{}\""
msgstr "Fout bij het indexen van map \"{}\""

#: bCNC/bmain.py:2568
msgid "Error opening serial"
msgstr "Fout bij het openen van seriële poort"

#: bCNC/lib/bFileDialog.py:908
#, fuzzy
#| msgid "Error renaming \"{}\" to \"{}\""
msgid "Error renaming \"{}\" to \"{}\""
msgstr "Fout bij het veranderen van naam van \"{}\" naar \"{}\""

#: bCNC/Utils.py:694 bCNC/Utils.py:708
msgid "Error sending report"
msgstr "Fout tijdens het zenden van rapport"

#: bCNC/Updates.py:193
#, fuzzy
#| msgid "Error {} in connection"
msgid "Error {} in connection"
msgstr "Fout {} in verbinding"

#: bCNC/ProbePage.py:853
msgid "Error:"
msgstr "Fout:"

#: bCNC/plugins/bowl.py:136
msgid "Error: Check the Bowl and End Mill parameters"
msgstr "Fout: Controleer de kom en End Mill parameters"

#: bCNC/plugins/jigsaw.py:406
msgid "Error: Check the parameters and your endmill config"
msgstr "Error: Controleer de parameters en uw endmill configuratie"

#: bCNC/plugins/midi2cnc.py:155
msgid "Error: Sorry can't parse the Midi file."
msgstr "Fout: Excuses, kan het Midi bestand niet lezen."

#: bCNC/plugins/midi2cnc.py:116
msgid "Error: This plugin requires midiparser.py"
msgstr "Fout: Deze plugin vereist midiparser.py"

#: bCNC/bmain.py:1487
msgid "Evaluation error"
msgstr "Evaluatiefout"

#: bCNC/plugins/trochoidal.py:47
msgid "Even spacing across segment"
msgstr ""

#: bCNC/ToolsPage.py:1927
msgid "Events"
msgstr "Evenementen"

#: bCNC/plugins/driller.py:50
msgid "Excellon-File"
msgstr ""

#: bCNC/ToolsPage.py:1967
msgid "Execute"
msgstr "Uitvoeren"

#: bCNC/bmain.py:2417
msgid "Existing Autolevel"
msgstr "Bestaande Autolevel"

#: bCNC/FilePage.py:272
msgid "Exit"
msgstr "Afsluiten"

#: bCNC/ToolsPage.py:805
msgid "Exit strategy (usefull for threads)"
msgstr ""

#: bCNC/EditorPage.py:351
msgid "Expand"
msgstr "Uitvouwen"

#: bCNC/plugins/spirograph.py:119
msgid "External Radius"
msgstr "Externe Radius"

#: bCNC/ToolsPage.py:567 bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:591
msgid "F1"
msgstr "F1"

#: bCNC/ToolsPage.py:576 bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:600
msgid "F10"
msgstr "F10"

#: bCNC/ToolsPage.py:577 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:601
msgid "F11"
msgstr "F11"

#: bCNC/ToolsPage.py:578 bCNC/ToolsPage.py:590 bCNC/ToolsPage.py:602
msgid "F12"
msgstr "F12"

#: bCNC/ToolsPage.py:568 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:592
msgid "F2"
msgstr "F2"

#: bCNC/ToolsPage.py:569 bCNC/ToolsPage.py:581 bCNC/ToolsPage.py:593
msgid "F3"
msgstr "F3"

#: bCNC/ToolsPage.py:570 bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:594
msgid "F4"
msgstr "F4"

#: bCNC/ToolsPage.py:571 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:595
msgid "F5"
msgstr "F5"

#: bCNC/ToolsPage.py:572 bCNC/ToolsPage.py:584 bCNC/ToolsPage.py:596
msgid "F6"
msgstr "F6"

#: bCNC/ToolsPage.py:573 bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:597
msgid "F7"
msgstr "F7"

#: bCNC/ToolsPage.py:574 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:598
msgid "F8"
msgstr "F8"

#: bCNC/ToolsPage.py:575 bCNC/ToolsPage.py:587 bCNC/ToolsPage.py:599
msgid "F9"
msgstr "F9"

#: bCNC/ProbePage.py:485
msgid "FEED"
msgstr ""

#: bCNC/ProbePage.py:518
msgid "FINISH"
msgstr ""

#: bCNC/plugins/sketch.py:47
msgid "Fading force"
msgstr ""

#: bCNC/ProbePage.py:306
msgid "Fast Probe Feed:"
msgstr "Snelle Sondevoeding:"

#: bCNC/plugins/scaling.py:43 bCNC/plugins/trochoidal_3D.py:95
#: bCNC/ToolsPage.py:682 bCNC/ToolsPage.py:769
msgid "Feed"
msgstr ""

#: bCNC/ControlPage.py:2021
msgid "Feed Mode [G93, G94, G95]"
msgstr "Voedingsmodus [G93,G94,G95]"

#: bCNC/ControlPage.py:1999
msgid "Feed Rate [F#]"
msgstr "Voedingssnelheid [F#]"

#: bCNC/plugins/trochoidal_3D.py:411
#, fuzzy
msgid "Feed has to be greater than 0"
msgstr "Hoogtekaart fout: diepte moet lager zijn dan 0"

#: bCNC/ControlPage.py:487
msgid "Feed hold"
msgstr ""

#: bCNC/ToolsPage.py:644
msgid "Feed max x"
msgstr ""

#: bCNC/ToolsPage.py:645
msgid "Feed max y"
msgstr ""

#: bCNC/ToolsPage.py:646
msgid "Feed max z"
msgstr ""

#: bCNC/plugins/driller.py:56
msgid "Feed rapid G0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:62
msgid "Feed rate has not yet been set or is undefined."
msgstr "Aanvoersnelheid is nog niet ingesteld of gedefinieerd."

#: bCNC/ControlPage.py:1987
msgid "Feed:"
msgstr "Voeding:"

#: bCNC/plugins/trochoidal.py:53
msgid "Feedrate"
msgstr ""

#: bCNC/FilePage.py:75 bCNC/FilePage.py:464
msgid "File"
msgstr "Bestand"

#: bCNC/FilePage.py:463
msgid "File I/O and configuration"
msgstr "Bestand I/O en configuratie"

#: bCNC/lib/bFileDialog.py:951 bCNC/lib/bFileDialog.py:962
#, fuzzy
#| msgid "File \"{}\" does not exist"
msgid "File \"{}\" does not exist"
msgstr "Bestand \"{}\" bestaat niet"

#: bCNC/lib/bFileDialog.py:984
msgid "File already exists"
msgstr "Bestand bestaat al"

#: bCNC/lib/bFileDialog.py:950 bCNC/lib/bFileDialog.py:961
msgid "File does not exist"
msgstr "Bestand bestaat niet"

#: bCNC/bmain.py:2378
msgid "File modified"
msgstr "Bestand is gewijzigd"

#: bCNC/lib/bFileDialog.py:332
msgid "File name:"
msgstr "Bestandsnaam:"

#: bCNC/lib/bFileDialog.py:340
msgid "Files of type:"
msgstr "Bestanden van het type:"

#: bCNC/EditorPage.py:167
msgid "Filter"
msgstr ""

#: bCNC/EditorPage.py:175
msgid "Filter blocks"
msgstr "Filter blokken"

#: bCNC/plugins/Helical_Descent.py:87
#, fuzzy
msgid "Final Depth"
msgstr "Werkdiepte"

#: bCNC/plugins/center.py:18
msgid "Find center of bounding box"
msgstr ""

#: bCNC/plugins/box.py:351
msgid "Fingers Nx"
msgstr "Vingers Nx"

#: bCNC/plugins/box.py:352
msgid "Fingers Ny"
msgstr "Vingers Ny"

#: bCNC/plugins/box.py:353
msgid "Fingers Nz"
msgstr "Vingers Nz"

#: bCNC/ProbePage.py:1214
msgid "Finished recording"
msgstr ""

#: bCNC/ToolsPage.py:790
msgid "First cut at surface height"
msgstr "Snijd eerst op oppervlaktehoogte"

#: bCNC/CNCCanvas.py:2270
msgid "Fit to screen [F]"
msgstr "Aanpassen aan scherm [F]"

#: bCNC/plugins/flatpath.py:12
msgid "FlatPath"
msgstr ""

#: bCNC/plugins/flatten.py:13
msgid "Flatten"
msgstr "Afplatten"

#: bCNC/plugins/flatten.py:43
msgid "Flatten abort: Cut Direction is undefined"
msgstr "Fout tijdens afplatten: Snijrichting is niet gespecificeerd"

#: bCNC/plugins/flatten.py:52
msgid "Flatten abort: Flatten Area dimensions must be > 0"
msgstr "Fout tijdens afplatten: Dimensies van gebied moeten groter dan 0 zijn"

#: bCNC/plugins/flatten.py:98
msgid "Flatten abort: Flatten area is too small for this End Mill."
msgstr "Fout tijdens afplatten: Gebied is te klein voor deze vingerfrees."

#: bCNC/plugins/flatten.py:58
msgid "Flatten abort: Hey this is only for subtractive machine! Check depth!"
msgstr ""
"Fout tijdens afplatten: Dit is alleen een subtractieve machine, controleer "
"de diepte!"

#: bCNC/plugins/flatten.py:47
#, fuzzy
msgid "Flatten abort: Pocket Type is undefined"
msgstr "Fout tijdens afplatten: Pocket Type is niet gespecificeerd"

#: bCNC/plugins/flatten.py:281
msgid "Flatten an area in different ways"
msgstr "Afplatten op diverse manieren"

#: bCNC/plugins/flatpath.py:18
msgid "Flatten the path"
msgstr ""

#: bCNC/plugins/flatten.py:328
msgid "Flatten: Generated flatten surface"
msgstr "Afplatten: Oppervlak gegenereerd"

#: bCNC/EditorPage.py:581
msgid "Flip"
msgstr "Omdraaien"

#: bCNC/plugins/slicemesh.py:68
msgid "Flip upwards"
msgstr ""

#: bCNC/ControlPage.py:2168
msgid "Flood"
msgstr ""

#: bCNC/ToolsPage.py:715
msgid "Flutes"
msgstr "Groeven"

#: bCNC/plugins/text.py:41
msgid "Font file"
msgstr "Lettertype bestand"

#: bCNC/plugins/text.py:40
msgid "Font size"
msgstr "Lettertypegrootte"

#: bCNC/ToolsPage.py:1941
msgid "Fonts"
msgstr "Lettertypes"

#: bCNC/ToolsPage.py:657
msgid "Footer gcode"
msgstr "Footer gcode"

#: bCNC/plugins/function_plot.py:24
msgid "Formula"
msgstr ""

#: bCNC/ProbePage.py:1575
msgid "Freeze"
msgstr "Bevries"

#: bCNC/plugins/function_plot.py:8
#, fuzzy
#| msgid "Connection"
msgid "Function"
msgstr "Verbinding"

#: bCNC/bmain.py:133 bCNC/bmain.py:2487
msgid "G-Code"
msgstr "G-Code"

#: bCNC/plugins/arcfit.py:17
msgid "G-Code arc-fit"
msgstr ""

#: bCNC/bmain.py:134
msgid "G-Code clean"
msgstr "G-Code schoon"

#: bCNC/plugins/linearize.py:17
msgid "G-Code linearizer"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:63
msgid "G-code command in block requires an integer value."
msgstr "G-code commando in blok vereist een geheel getal (integer)."

#: bCNC/controllers/_GenericGRBL.py:108
msgid "G-code command not allowed when tool change is pending. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:45
msgid "G-code locked out during alarm or jog state"
msgstr "G-code geblokkeerd tijdens alarm- of jog-status"

#: bCNC/controllers/_GenericGRBL.py:35
msgid "G-code words consist of a letter and a value. Letter was not found."
msgstr ""
"G-code woorden bestaan uit en letter en een waarde. De letter was niet "
"gevonden."

#: bCNC/ProbePage.py:59
msgid "G38.2 stop on contact else error"
msgstr "G38.2 stop bij contact else fout"

#: bCNC/ProbePage.py:60
msgid "G38.3 stop on contact"
msgstr "G38.3 stop bij contact"

#: bCNC/ProbePage.py:61
msgid "G38.4 stop on loss contact else error"
msgstr "G38.4 stop bij verlies contact fout"

#: bCNC/ProbePage.py:62
msgid "G38.5 stop on loss contact"
msgstr "G38.5 stop bij verlies contact"

#: bCNC/ControlPage.py:2051
msgid "G92:"
msgstr ""

#: bCNC/ProbePage.py:733
msgid "GCode X coordinate of orientation point"
msgstr "GCode X-coördinaat van oriëntatiepunt"

#: bCNC/ProbePage.py:744
msgid "GCode Y coordinate of orientation point"
msgstr "GCode Y-coördinaat van oriëntatiepunt"

#: bCNC/EditorPage.py:803
msgid "GCode editor"
msgstr "Gcode bewerker"

#: bCNC/ToolsPage.py:2089
msgid "GCode manipulation tools and user plugins"
msgstr "GCode bewerkmiddelen en plugins"

#: bCNC/bmain.py:2379
msgid "Gcode was modified do you want to save it first?"
msgstr "Gcode is gewijzigd, wilt u het eerst opslaan?"

#: bCNC/ProbePage.py:723
msgid "Gcode:"
msgstr "Gcode:"

#: bCNC/plugins/gear.py:16
msgid "Gear"
msgstr "Tandwiel"

#: bCNC/plugins/bowl.py:105
msgid "Generate a bowl cavity"
msgstr "Genereer een komholte"

#: bCNC/plugins/box.py:339
msgid "Generate a finger box"
msgstr "Genereer een vingerdoos"

#: bCNC/plugins/endmilloffset.py:430
msgid "Generate a pocket or profile for selected shape (regarding islands)"
msgstr ""

#: bCNC/plugins/simpleArc.py:58
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Arc"
msgstr "Genereer een tandwiel net rechte vertanding"

#: bCNC/plugins/simpleDrill.py:60
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple Drill"
msgstr "Genereer een tandwiel net rechte vertanding"

#: bCNC/plugins/simpleLine.py:44
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple line"
msgstr "Genereer een tandwiel net rechte vertanding"

#: bCNC/plugins/simpleRectangle.py:85
#, fuzzy
#| msgid "Generate a spur gear"
msgid "Generate a simple rectangle"
msgstr "Genereer een tandwiel net rechte vertanding"

#: bCNC/plugins/gear.py:153
msgid "Generate a spur gear"
msgstr "Genereer een tandwiel net rechte vertanding"

#: bCNC/plugins/halftone.py:53
msgid "Generate for conical end mill"
msgstr "Genereer voor conische vingerfrees"

#: bCNC/plugins/endmilloffset.py:175 bCNC/plugins/endmilloffset.py:183
#: bCNC/plugins/endmilloffset.py:191 bCNC/plugins/endmilloffset.py:199
#: bCNC/plugins/endmilloffset.py:207 bCNC/plugins/endmilloffset.py:215
#: bCNC/plugins/endmilloffset.py:223 bCNC/plugins/endmilloffset.py:573
#: bCNC/ToolsPage.py:1088
msgid "Generate pocket path"
msgstr ""

#: bCNC/ToolsPage.py:1062
msgid "Generate profile path"
msgstr "Profielpad genereren"

#: bCNC/plugins/Random.py:22 bCNC/plugins/tile.py:20
msgid "Generate replicas of selected code"
msgstr "Genereer replica's van geselecteerde code"

#: bCNC/plugins/driller.py:459
#, fuzzy
#| msgid "Generated Driller: {} holes"
msgid "Generated Driller: {} holes"
msgstr "Driller gegenereerd: {} gaten"

#: bCNC/plugins/halftone.py:285
#, fuzzy
#| msgid "Generated Halftone size W={} x H={} x D={} ,Total points:%i"
msgid "Generated Halftone size W={} x H={} x D={}, Total points: {}"
msgstr "Halftone gegereneerd W={} x H={} x D={}, Totale punten: {}"

#: bCNC/plugins/heightmap.py:408
#, fuzzy
#| msgid "Generated Heightmap {} x {} x {} "
msgid "Generated Heightmap {} x {} x {}"
msgstr "Gegenereerde Hoogtekaart {} x {} x {} "

#: bCNC/plugins/midi2cnc.py:353
msgid "Generated Midi2CNC, ready to play?"
msgstr "Midi2CNC is gegenereerd, klaar om af te spelen?"

#: bCNC/plugins/pyrograph.py:213
msgid "Generated Pyrograph W={:g} x H={:g} x D={:g}"
msgstr ""

#: bCNC/plugins/function_plot.py:273
#, fuzzy
#| msgid "Generated: Spirograph"
msgid "Generated function graph"
msgstr "Spirograaf gegenereerd"

#: bCNC/plugins/trochoidPath.py:110
#, fuzzy
#| msgid "Generate for conical end mill"
msgid "Generated path for trochoidal cutting"
msgstr "Genereer voor conische vingerfrees"

#: bCNC/plugins/arcfit.py:102
msgid "Generated: Arc fit"
msgstr ""

#: bCNC/plugins/bowl.py:134
msgid "Generated: BOWL"
msgstr "Kom gegenereerd"

#: bCNC/plugins/box.py:395
msgid "Generated: BOX with fingers"
msgstr "Doos met vingers gegenereerd"

#: bCNC/plugins/center.py:70
msgid "Generated: Center"
msgstr ""

#: bCNC/plugins/closepath.py:52
msgid "Generated: Closepath"
msgstr ""

#: bCNC/plugins/difference.py:102
msgid "Generated: Diff"
msgstr ""

#: bCNC/plugins/dragknife.py:229
msgid "Generated: Dragknife"
msgstr ""

#: bCNC/plugins/flatpath.py:60
msgid "Generated: Flat"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:545
#, fuzzy
msgid "Generated: Helical_Descent Result"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/hilbert.py:146
msgid "Generated: Hilbert"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/intersection.py:86
msgid "Generated: Intersect"
msgstr ""

#: bCNC/plugins/linearize.py:97
msgid "Generated: Linearize"
msgstr ""

#: bCNC/plugins/drillmark.py:319
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Manual drillmark"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/simpleArc.py:91
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Arc"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/simpleDrill.py:106
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Drill"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/simpleLine.py:75
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Line"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/simpleRectangle.py:120
#, fuzzy
#| msgid "Generated: Hilbert"
msgid "Generated: Simple Rectangle"
msgstr "Hilbert gegenereerd"

#: bCNC/plugins/spirograph.py:145
msgid "Generated: Spirograph"
msgstr "Spirograaf gegenereerd"

#: bCNC/plugins/gear.py:180
msgid "Generated: Spur GEAR"
msgstr "Gegenereerd: Tandwiel met rechte vertanding"

#: bCNC/plugins/trochoidal.py:177
msgid "Generated: Trochoidal"
msgstr ""

#: bCNC/plugins/zigzag.py:158
msgid "Generated: Zig-Zag"
msgstr "Zig-Zag gegenereerd"

#: bCNC/plugins/function_plot.py:14
msgid "Generates gcode from a formula"
msgstr ""

#: bCNC/plugins/jigsaw.py:387
msgid "Generating puzzle..."
msgstr "Puzzel aan het genereren..."

#: bCNC/ProbePage.py:1704
msgid "Get"
msgstr "Verkrijg"

#: bCNC/ProbePage.py:2050
msgid "Get current gantry position as machine tool change location"
msgstr ""
"Verkrijg huidige portiek positie als machine's gereedschapswijzingslocatie"

#: bCNC/ProbePage.py:2096
msgid "Get current gantry position as machine tool probe location"
msgstr ""

#: bCNC/ProbePage.py:1706
#, fuzzy
msgid "Get diameter from active endmill"
msgstr "Verkrijg diameter van actieve endmill"

#: bCNC/plugins/slicemesh.py:63
msgid "Get flat slice"
msgstr ""

#: bCNC/ProbePage.py:192
msgid "Get margins from gcode file"
msgstr "Verkrijg marges van gcode bestand"

#: bCNC/ProbePage.py:583
msgid "Goto"
msgstr "Ga naar"

#: bCNC/controllers/_GenericGRBL.py:37
msgid "Grbl '$' system command was not recognized or supported."
msgstr "Grbl '$' systeemcommando werd niet herkend of ondersteund."

#: bCNC/controllers/_GenericGRBL.py:21
msgid "Grbl executes jogging motion"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:13
msgid "Grbl is in idle state and waiting for user commands"
msgstr "Grbl bevindt zich in de rusttoestand en wacht op gebruikersopdrachten"

#: bCNC/controllers/_GenericGRBL.py:29
msgid "Grbl is not connected. Please specify the correct port and click Open."
msgstr "Grbl is niet verbonden. Geef de juiste poort op en klik op Openen."

#: bCNC/controllers/_GenericGRBL.py:14
msgid "Grbl is on hold state. Click on resume (pause) to continue"
msgstr "Grbl staat in de wacht. Klik op hervatten (pauze) om verder te gaan"

#: bCNC/plugins/sketch.py:41
msgid "Grundgy, search radius"
msgstr ""

#: bCNC/ProbePage.py:1659
msgid "Haircross Offset:"
msgstr "Aanwijzer compensatie:"

#: bCNC/ProbePage.py:1667
msgid "Haircross X offset [unit]"
msgstr "Aanwijzer X compensatie [eenheid]"

#: bCNC/ProbePage.py:1676
msgid "Haircross Y offset [unit]"
msgstr "Aanwijzer Y compensatie [eenheid]"

#: bCNC/plugins/halftone.py:15
msgid "Halftone"
msgstr "Halftone"

#: bCNC/plugins/halftone.py:159
msgid "Halftone abort: Angle in V-Cutting end mill is missing"
msgstr "Halftone fout: Hoek in V-snijdende vingerfrees mist"

#: bCNC/plugins/halftone.py:173
msgid "Halftone abort: Can't read image file"
msgstr "Halftone fout: Kan afbeelding niet lezen"

#: bCNC/plugins/halftone.py:148
msgid "Halftone abort: Cell size too small"
msgstr "Halftone fout: Celgrootte te klein"

#: bCNC/plugins/halftone.py:165
msgid "Halftone abort: Conical path need V-Cutting end mill"
msgstr "Halftone fout: Conische baan vereist V-snijdende vingerfrees"

#: bCNC/plugins/halftone.py:144
msgid "Halftone abort: Maximum diameter too small"
msgstr "Halftone fout: Maximale diameter is te klein"

#: bCNC/plugins/halftone.py:138
msgid "Halftone abort: Minimum diameter must be minor then Maximum"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/halftone.py:133
msgid "Halftone abort: Size too small to draw anything!"
msgstr "Halftone fout: Grootte te klein om iets te tekenen!"

#: bCNC/plugins/halftone.py:109
msgid "Halftone abort: This plugin requires PIL/Pillow to read image data"
msgstr ""
"Halftone fout: Deze plugin vereist PIL/Pillow om afbeeldingsdata te lezen"

#: bCNC/ControlPage.py:84
msgid "Hard Reset"
msgstr "Harde Reset"

#: bCNC/ToolsPage.py:656
msgid "Header gcode"
msgstr "Header gcode"

#: bCNC/ToolsPage.py:1105
msgid "Height"
msgstr "Hoogte"

#: bCNC/plugins/box.py:350
msgid "Height Dz"
msgstr "Hoogte Dz"

#: bCNC/plugins/flatten.py:292
msgid "Height to flatten"
msgstr "Hoogte om af te platten"

#: bCNC/plugins/heightmap.py:40
msgid "Heightmap"
msgstr "Hoogtekaart"

#: bCNC/plugins/heightmap.py:98
msgid "Heightmap abort: Can't read image file"
msgstr "Hoogtekaart fout: Kan de afbeelding niet lezen"

#: bCNC/plugins/heightmap.py:89
msgid "Heightmap abort: This plugin requires PIL/Pillow"
msgstr "Hoogtekaart fout: Deze plugin vereist PIL/Pillow"

#: bCNC/plugins/heightmap.py:171
msgid "Heightmap abort: angle not defined for selected End Mill"
msgstr ""
"Hoogtekaart fout: hoek voor geselecteerde vingerfrees niet geselecteerd"

#: bCNC/plugins/heightmap.py:102
msgid "Heightmap abort: depth must be < 0"
msgstr "Hoogtekaart fout: diepte moet lager zijn dan 0"

#: bCNC/plugins/Helical_Descent.py:271
#, fuzzy
msgid "Helical Abort: Drop must be greater than 0"
msgstr "Hoogtekaart fout: diepte moet lager zijn dan 0"

#: bCNC/plugins/Helical_Descent.py:286
msgid "Helical Abort: Entry Edge Clearence may be positive"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:261
#, fuzzy
msgid "Helical Abort: Helix diameter must be greater than the end mill"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/Helical_Descent.py:267
#, fuzzy
msgid "Helical Abort: Helix diameter must be positive"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/Helical_Descent.py:257
#, fuzzy
msgid "Helical Abort: Please select helical type"
msgstr "Driller fout: Selecteer alstublieft een pad"

#: bCNC/plugins/Helical_Descent.py:281
#, fuzzy
msgid "Helical Abort: Please selecte Entry and Exit type"
msgstr "Tekst fout: selecteer alstublieft een lettertype bestand"

#: bCNC/plugins/Helical_Descent.py:275
#, fuzzy
msgid "Helical Abort: Z Feed Multiplier must be greater than 0"
msgstr "De minimale stap-impulstijd moet groter zijn dan 3 usec"

#: bCNC/plugins/trochoidal_3D.py:407
#, fuzzy
msgid "Helical Descent Diameter has to be greater than End mill"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/Helical_Descent.py:94
msgid "Helical Type"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:320
#, fuzzy
msgid "Helical abort: Please select some path"
msgstr "Driller fout: Selecteer alstublieft een pad"

#: bCNC/TerminalPage.py:175
msgid "Help"
msgstr "Help"

#: bCNC/Ribbon.py:641
msgid "Help [F1]"
msgstr "Help [F1]"

#: bCNC/plugins/hilbert.py:15
msgid "Hilbert"
msgstr ""

#: bCNC/plugins/hilbert.py:136
msgid "Hilbert abort: depth must be minor or equal to zero"
msgstr "Hilbert fout: diepte moet lager of gelijk zijn aan nul"

#: bCNC/plugins/hilbert.py:131
msgid "Hilbert abort: verify the size"
msgstr "Hilbert fout: controleer de grootte"

#: bCNC/controllers/_GenericGRBL.py:160
#, fuzzy
msgid "Hold complete. Ready to resume."
msgstr "Wachten voltooid. Klaar om te hervatten."

#: bCNC/controllers/_GenericGRBL.py:161
msgid "Hold in-progress. Reset will throw an alarm."
msgstr "Wordt momenteel in bedrijf gehouden. De reset zal een alarm geven."

#: bCNC/ControlPage.py:93
msgid "Home"
msgstr "Thuis"

#: bCNC/controllers/_GenericGRBL.py:116
msgid "Home machine to continue. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:39
msgid "Homing cycle is not enabled via settings."
msgstr "De homing-cyclus is niet ingeschakeld in de instellingen."

#: bCNC/controllers/_GenericGRBL.py:142
msgid "Homing fail. Reset during active homing cycle."
msgstr ""
"Homing mislukt. Een reset heeft plaatsgevonden tijdens de homing cycle "
"actief was."

#: bCNC/controllers/_GenericGRBL.py:143
msgid "Homing fail. Safety door was opened during active homing cycle."
msgstr ""
"Homing mislukt. Veiligheidsdeur was geopend tijdens de homing cyclus actief "
"was."

#: bCNC/controllers/_GenericGRBL.py:152
msgid "Homing required. Execute homing command ($H) to continue. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:612
msgid "Horizontal"
msgstr "Horizontaal"

#: bCNC/Utils.py:848
msgid "Icon to appear on button"
msgstr "Icoon dat op de knop moet verschijnen"

#: bCNC/Utils.py:837
msgid "Icon:"
msgstr "Icoon:"

#: bCNC/plugins/Helical_Descent.py:97
msgid "If Eddge, Edge Clearance"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:253
msgid "If block selected false, please make a value of x"
msgstr ""

#: bCNC/ProbePage.py:67
msgid "Ignore M6 commands"
msgstr "Negeer M6 commando"

#: bCNC/plugins/text.py:44
msgid "Image chars width"
msgstr "Afbeelding karakterbreedte"

#: bCNC/plugins/halftone.py:50
msgid "Image rotation angle"
msgstr "Afbeeldingsrotatiehoek"

#: bCNC/plugins/text.py:43
msgid "Image to Ascii"
msgstr "Afbeelding naar Ascii"

#: bCNC/plugins/halftone.py:38 bCNC/plugins/sketch.py:52
#: bCNC/plugins/heightmap.py:78 bCNC/plugins/pyrograph.py:48
msgid "Image to process"
msgstr "Afbeelding om te verwerken"

#: bCNC/EditorPage.py:207 bCNC/FilePage.py:126
msgid "Import"
msgstr "Importeer"

#: bCNC/bmain.py:2482
msgid "Import Gcode/DXF file"
msgstr "Importeer Gcode/DXF bestand"

#: bCNC/FilePage.py:131
msgid "Import gcode/dxf file"
msgstr ""

#: bCNC/ControlPage.py:1008 bCNC/ControlPage.py:1484
msgid "Increase step by 1 unit"
msgstr "Verhoog stap met 1 eenheid"

#: bCNC/EditorPage.py:742 bCNC/EditorPage.py:765
msgid "Info"
msgstr "Informatie"

#: bCNC/ToolsPage.py:1630
msgid "Insert a drill cycle on current objects/location"
msgstr "Voeg een drill-cyclus toe in de huidige objecten/locatie"

#: bCNC/EditorPage.py:239 bCNC/EditorPage.py:266
msgid "Insert a new block or line of code [Ins or Ctrl-Enter]"
msgstr "Voeg nieuw blok of regel of code toe [Ins of Ctrl-Enter]"

#: bCNC/ToolsPage.py:1646
msgid "Insert holding tabs"
msgstr ""

#: bCNC/Updates.py:60
msgid "Installed Version:"
msgstr "Geïnstalleerde Versie:"

#: bCNC/plugins/box.py:347
msgid "Internal Dimensions"
msgstr "Interne dimensies"

#: bCNC/plugins/spirograph.py:120
msgid "Internal Radius"
msgstr "Interne Radius"

#: bCNC/plugins/intersection.py:18
msgid "Intersection"
msgstr ""

#: bCNC/plugins/intersection.py:24
msgid "Intersection of two shapes"
msgstr ""

#: bCNC/Updates.py:103
msgid "Interval (days):"
msgstr "Interval (dagen):"

#: bCNC/ProbePage.py:1412
msgid "Invalid X probing region"
msgstr "Ongeldig X meetgebied"

#: bCNC/ProbePage.py:1421
msgid "Invalid X range [xmin>=xmax]"
msgstr "Ongeldig X meetbereik [xmin>=xmax]"

#: bCNC/ProbePage.py:1436
msgid "Invalid Y probing region"
msgstr "Ongeldig Y meetgebied"

#: bCNC/ProbePage.py:1445
msgid "Invalid Y range [ymin>=ymax]"
msgstr "Ongeldig Y meetbereik [ymin>=ymax]"

#: bCNC/ProbePage.py:1457
msgid "Invalid Z probing region"
msgstr "Ongeldig Z meetgebied"

#: bCNC/ProbePage.py:1466
msgid "Invalid Z range [zmin>=zmax]"
msgstr "Ongeldig Z meetbereik [zmin>=zmax]"

#: bCNC/Sender.py:355
#, fuzzy
#| msgid "Invalid command {}"
msgid "Invalid command {}"
msgstr "Ongeldig commando {}"

#: bCNC/ProbePage.py:988
msgid "Invalid diameter entered"
msgstr "Ongeldige diameter ingevoerd"

#: bCNC/bmain.py:1585
#, fuzzy
#| msgid "Invalid direction {} specified"
msgid "Invalid direction {} specified"
msgstr "Ongeldige richting {} gespecificeerd"

#: bCNC/ProbePage.py:923 bCNC/ProbePage.py:1475
msgid "Invalid probe feed rate"
msgstr "Ongeldige sonde voedingssnelheid"

#: bCNC/ProbePage.py:2199
msgid "Invalid tool change position"
msgstr "Ongeldige gereedschap verander positie"

#: bCNC/ProbePage.py:2231
msgid "Invalid tool height or not calibrated"
msgstr "Ongeldige gereedschapshoogte of niet gekalibreerd"

#: bCNC/ProbePage.py:2211
msgid "Invalid tool probe location"
msgstr ""

#: bCNC/ProbePage.py:2221 bCNC/ProbePage.py:2241
msgid "Invalid tool scanning distance entered"
msgstr "Ongeldige gereedschap scan afstand ingevoerd"

#: bCNC/bmain.py:1978
#, fuzzy
#| msgid "Invalid user command {}"
msgid "Invalid user command {}"
msgstr "Ongeldig gebruikerscommando {}"

#: bCNC/plugins/heightmap.py:76 bCNC/EditorPage.py:138 bCNC/EditorPage.py:543
msgid "Invert"
msgstr "Omkeren"

#: bCNC/plugins/halftone.py:45
msgid "Invert Colors"
msgstr "Kleuren Omkeren"

#: bCNC/EditorPage.py:549
msgid "Invert cutting order of selected blocks"
msgstr "Knipvolgorde van geselecteerde blokken omkeren"

#: bCNC/EditorPage.py:144
msgid "Invert selection [Ctrl-I]"
msgstr "Keer selectie om [Ctrl-I]"

#: bCNC/ToolsPage.py:1655
msgid "Island"
msgstr ""

#: bCNC/plugins/endmilloffset.py:494
msgid "Island behaviour"
msgstr ""

#: bCNC/plugins/jigsaw.py:403
msgid "Jigsaw puzzle generated in {}s"
msgstr "Puzzel gegenereerd in {}s"

#: bCNC/plugins/jigsaw.py:353
msgid "Jigsaw puzzle generator"
msgstr "Puzzel generator"

#: bCNC/controllers/_GenericGRBL.py:58
msgid "Jog command with no '=' or contains prohibited g-code."
msgstr "Jog-opdracht zonder '=' of bevat verboden g-code."

#: bCNC/controllers/_GenericGRBL.py:57
msgid "Jog target exceeds machine travel. Command ignored."
msgstr "Jog doel overschrijdt machine afstand. Commando genegeerd."

#: bCNC/EditorPage.py:386
msgid "Join"
msgstr ""

#: bCNC/EditorPage.py:392
msgid "Join selected blocks"
msgstr ""

#: bCNC/plugins/simpleTranslate.py:29
msgid "Keep original Yes/No"
msgstr ""

#: bCNC/plugins/simpleRotate.py:30
msgid "Keep original yes/no"
msgstr ""

#: bCNC/ToolsPage.py:1911
msgid "Language change"
msgstr "Taal veranderen"

#: bCNC/ToolsPage.py:638
#, fuzzy
msgid "Laser Adaptive Power"
msgstr "Laser kracht"

#: bCNC/ToolsPage.py:637
msgid "Laser Cutter"
msgstr "Lasersnijder"

#: bCNC/controllers/_GenericGRBL.py:59
msgid "Laser mode requires PWM output."
msgstr "Laser modus vereist PWM-uitvoer."

#: bCNC/plugins/driller.py:70
msgid "Laser power maximum"
msgstr ""

#: bCNC/plugins/driller.py:63
msgid "Laser power minimum"
msgstr ""

#: bCNC/Updates.py:89
msgid "Last Check:"
msgstr "Laatst gecontroleerd:"

#: bCNC/ControlPage.py:603 bCNC/ControlPage.py:910
#, fuzzy
#| msgid "Last error: {}\n"
msgid "Last error: {}\n"
msgstr "Laatste fout: {}\n"

#: bCNC/Updates.py:67
msgid "Latest Github Version:"
msgstr "Laatste Github Versie:"

#: bCNC/Updates.py:73
#, fuzzy
#| msgid "Latest release version on on github"
msgid "Latest release version on github"
msgstr "Laatste uitgegeven versie op github"

#: bCNC/EditorPage.py:154
msgid "Layer"
msgstr "Laag"

#: bCNC/ToolsPage.py:811
msgid "Leave islands uncut"
msgstr ""

#: bCNC/EditorPage.py:460
msgid "Left"
msgstr "Links"

#: bCNC/ToolsPage.py:716
msgid "Length"
msgstr "Lengte"

#: bCNC/plugins/spiral.py:477
msgid "Length of Stock to Reduce"
msgstr ""

#: bCNC/bmain.py:1204 bCNC/bmain.py:1290
msgid "Length:"
msgstr "Lengte:"

#: bCNC/plugins/spiral.py:484
msgid "Lift before rotate"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:154
msgid "Limit switch engaged. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:245
msgid "Line"
msgstr "Regel"

#: bCNC/plugins/zigzag.py:107
msgid "Line length"
msgstr "Regellengte"

#: bCNC/plugins/linearize.py:11
msgid "Linearize"
msgstr ""

#: bCNC/plugins/endmilloffset.py:503
msgid "Link segments"
msgstr ""

#: bCNC/plugins/slicemesh.py:136
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading mesh: {}"
msgstr "Laden: {} ..."

#: bCNC/bmain.py:2424
#, fuzzy
#| msgid "Loading: {} ..."
msgid "Loading: {} ..."
msgstr "Laden: {} ..."

#: bCNC/ProbePage.py:1635
msgid "Location:"
msgstr "Locatie:"

#: bCNC/plugins/driller.py:51
msgid "M3 for laser (settings below)"
msgstr ""

#: bCNC/ControlPage.py:314 bCNC/ControlPage.py:693
#, fuzzy
msgid "MPos:"
msgstr "MPos:"

#: bCNC/ProbePage.py:2002
msgid "MX"
msgstr ""

#: bCNC/ProbePage.py:2004
msgid "MY"
msgstr ""

#: bCNC/ProbePage.py:2006
msgid "MZ"
msgstr ""

#: bCNC/ProbePage.py:775
msgid "Machine X coordinate of orientation point"
msgstr "Machine X-coördinaat van oriëntatiepunt"

#: bCNC/ProbePage.py:787
msgid "Machine Y coordinate of orientation point"
msgstr "Machine Y-coördinaat van oriëntatiepunt"

#: bCNC/ToolsPage.py:1829
msgid "Machine configuration for bCNC"
msgstr "Machine configuratie voor bCNC"

#: bCNC/controllers/_GenericGRBL.py:163
msgid "Machine stopped. Door still ajar. Can't resume until closed."
msgstr ""
"Machine is gestopt. De deur staat nog steeds op een kier. Kan niet doorgaan "
"tot de deur gesloten is."

#: bCNC/ProbePage.py:1962
msgid "Manual Tool Change"
msgstr "Handmatige gereedschapswijziging"

#: bCNC/ProbePage.py:70
msgid "Manual Tool Change (NoProbe)"
msgstr "Handmatig Gereedschap Wijzigen (NoProbe)"

#: bCNC/ProbePage.py:69
msgid "Manual Tool Change (TLO)"
msgstr "Handmatig Gereedschap Wijzigen (TLO)"

#: bCNC/ProbePage.py:68
msgid "Manual Tool Change (WCS)"
msgstr "Handmatig Gereedschap Wijzigen (WCS)"

#: bCNC/plugins/drillmark.py:317
msgid "Manual drill mark"
msgstr ""

#: bCNC/ProbePage.py:2018
msgid "Manual tool change Machine X location"
msgstr "Handmatige gereedschapswijziging voor machine's X locatie"

#: bCNC/ProbePage.py:2029
msgid "Manual tool change Machine Y location"
msgstr "Handmatige gereedschapswijziging voor machine's Ylocatie"

#: bCNC/ProbePage.py:2040
msgid "Manual tool change Machine Z location"
msgstr "Handmatige gereedschapswijziging voor machine's Z locatie"

#: bCNC/ProbePage.py:2064
msgid "Manual tool change Probing MX location"
msgstr "Handmatige gereedschapswijziging voor sonde's MX locatie"

#: bCNC/ProbePage.py:2075
msgid "Manual tool change Probing MY location"
msgstr "Handmatige gereedschapswijziging voor sonde's MY locatie"

#: bCNC/ProbePage.py:2086
msgid "Manual tool change Probing MZ location"
msgstr "Handmatige gereedschapswijziging voor sonde's MZ locatie"

#: bCNC/ProbePage.py:186
msgid "Margins"
msgstr "Marges"

#: bCNC/bmain.py:1159 bCNC/bmain.py:1240
msgid "Margins X:"
msgstr "Marges X:"

#: bCNC/plugins/drillmark.py:46
msgid "Mark X center"
msgstr ""

#: bCNC/plugins/drillmark.py:47
msgid "Mark Y center"
msgstr ""

#: bCNC/ProbePage.py:1752
msgid "Mark camera position for calculating offset"
msgstr "Markeer camera positie voor het berekenen van compensatie"

#: bCNC/plugins/drillmark.py:50
msgid "Mark drawing power"
msgstr ""

#: bCNC/ProbePage.py:1745
msgid "Mark spindle position for calculating offset"
msgstr "Markeer spoel positie voor het berekenen van compensatie"

#: bCNC/ProbePage.py:683
msgid "Markers:"
msgstr "Markeringen:"

#: bCNC/ToolsPage.py:711 bCNC/ToolsPage.py:739 bCNC/ToolsPage.py:1421
msgid "Material"
msgstr "Materiaal"

#: bCNC/ProbePage.py:1232
msgid "Max"
msgstr "Maximale"

#: bCNC/plugins/spiral.py:479
msgid "Max Depth per Pass"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:48
msgid "Max characters per line exceeded. Line was not processed and executed."
msgstr ""
"Maximum aantal karakters per regel overschreden. De regel is niet verwerkt "
"en niet uitgevoerd."

#: bCNC/plugins/halftone.py:48
msgid "Max diameter, cap limit"
msgstr "Maximum diameter, cap limiet"

#: bCNC/plugins/halftone.py:46
msgid "Max draw size (Width or Height)"
msgstr "Maximale tekengrootte (Breedte of Hoogte)"

#: bCNC/controllers/_GenericGRBL.py:113
msgid "Max. feed rate exceeded. (grblHAL)"
msgstr ""

#: bCNC/plugins/midi2cnc.py:64
msgid "Maximum X travel"
msgstr "Maximum X afstand"

#: bCNC/plugins/midi2cnc.py:65
msgid "Maximum Y travel"
msgstr "Maximum Y afstand"

#: bCNC/plugins/midi2cnc.py:66
msgid "Maximum Z travel"
msgstr "Maximum Z afstand"

#: bCNC/plugins/pyrograph.py:42
msgid "Maximum feed"
msgstr ""

#: bCNC/plugins/sketch.py:48
msgid "Maximum light"
msgstr ""

#: bCNC/plugins/sketch.py:44 bCNC/plugins/heightmap.py:66
#: bCNC/plugins/pyrograph.py:40
msgid "Maximum size"
msgstr "Maximum grootte"

#: bCNC/plugins/slicemesh.py:194
msgid "Mesh sliced"
msgstr ""

#: bCNC/plugins/midi2cnc.py:71
msgid "Midi to process"
msgstr "Midi om te verwerken"

#: bCNC/ProbePage.py:1230
msgid "Min"
msgstr "Minimale"

#: bCNC/plugins/halftone.py:49
msgid "Min diameter, cut off"
msgstr "Minimum diameter, afgesneden"

#: bCNC/ToolsPage.py:1102
#, fuzzy
msgid "Min. Distance of tabs"
msgstr "Minimale afstand van tabs"

#: bCNC/plugins/trochoidal_3D.py:420
#, fuzzy
#| msgid "Minimum step pulse time must be greater than 3usec"
msgid "Minimum Adaptative Feed has to be greater than 0"
msgstr "De minimale stap-impulstijd moet groter zijn dan 3 usec"

#: bCNC/plugins/pyrograph.py:41
msgid "Minimum feed"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:40
msgid "Minimum step pulse time must be greater than 3usec"
msgstr "De minimale stap-impulstijd moet groter zijn dan 3 usec"

#: bCNC/EditorPage.py:619
msgid "Mirror horizontally X=-X selected gcode"
msgstr "Spiegel horizontale geselecteerde X=-X gcode"

#: bCNC/EditorPage.py:634
msgid "Mirror vertically Y=-Y selected gcode"
msgstr "Spiegel verticale geselecteerde X=-X gcode"

#: bCNC/ControlPage.py:2182
msgid "Mist"
msgstr ""

#: bCNC/ControlPage.py:2009
msgid "Mode:"
msgstr "Modus:"

#: bCNC/ProbePage.py:261
msgid "Modify selected G-Code to match autolevel"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:61
msgid "More than one g-code command from same modal group found in block."
msgstr ""
"Meer dan één g-code commando van dezelfde modale groep gevonden in blok."

#: bCNC/ToolsPage.py:714
msgid "Mount Axis"
msgstr ""

#: bCNC/EditorPage.py:416 bCNC/EditorPage.py:424
msgid "Move"
msgstr "Beweeg"

#: bCNC/ControlPage.py:1428
#, fuzzy
#| msgid "Move +X"
msgid "Move +A"
msgstr "Beweeg +X"

#: bCNC/ControlPage.py:1530
#, fuzzy
#| msgid "Move +X"
msgid "Move +B"
msgstr "Beweeg +X"

#: bCNC/ControlPage.py:1468
#, fuzzy
#| msgid "Move +X +Y"
msgid "Move +B +C"
msgstr "Beweeg +X +Y"

#: bCNC/ControlPage.py:1635
#, fuzzy
#| msgid "Move +X -Y"
msgid "Move +B -C"
msgstr "Beweeg +X -Y"

#: bCNC/ControlPage.py:1455
#, fuzzy
#| msgid "Move +X"
msgid "Move +C"
msgstr "Beweeg +X"

#: bCNC/ControlPage.py:1060
msgid "Move +X"
msgstr "Beweeg +X"

#: bCNC/ControlPage.py:992
msgid "Move +X +Y"
msgstr "Beweeg +X +Y"

#: bCNC/ControlPage.py:1163
msgid "Move +X -Y"
msgstr "Beweeg +X -Y"

#: bCNC/ControlPage.py:980
msgid "Move +Y"
msgstr "Beweeg +Y"

#: bCNC/ControlPage.py:954
msgid "Move +Z"
msgstr "Beweeg +Z"

#: bCNC/ControlPage.py:1596
#, fuzzy
#| msgid "Move -X"
msgid "Move -A"
msgstr "Beweeg -X"

#: bCNC/ControlPage.py:1504
#, fuzzy
#| msgid "Move -X"
msgid "Move -B"
msgstr "Beweeg -X"

#: bCNC/ControlPage.py:1442
#, fuzzy
#| msgid "Move -X +Y"
msgid "Move -B +C"
msgstr "Beweeg -X +Y"

#: bCNC/ControlPage.py:1609
#, fuzzy
#| msgid "Move -X -Y"
msgid "Move -B -C"
msgstr "Beweeg -X -Y"

#: bCNC/ControlPage.py:1622
#, fuzzy
#| msgid "Move -X"
msgid "Move -C"
msgstr "Beweeg -X"

#: bCNC/ControlPage.py:1028
msgid "Move -X"
msgstr "Beweeg -X"

#: bCNC/ControlPage.py:967
msgid "Move -X +Y"
msgstr "Beweeg -X +Y"

#: bCNC/ControlPage.py:1137
msgid "Move -X -Y"
msgstr "Beweeg -X -Y"

#: bCNC/ControlPage.py:1150
msgid "Move -Y"
msgstr "Beweeg -Y"

#: bCNC/ControlPage.py:1124
msgid "Move -Z"
msgstr "Beweeg -Z"

#: bCNC/CNCCanvas.py:451
msgid "Move CNC gantry to mouse location"
msgstr ""

#: bCNC/ControlPage.py:451
msgid "Move Gantry"
msgstr "Beweeg het portiek"

#: bCNC/EditorPage.py:451
msgid "Move all gcode such as origin is on mouse location [O]"
msgstr "Verplaats alle gcode zodat oorsprong op muislocatie is [O]"

#: bCNC/CNCCanvas.py:760
#, fuzzy
#| msgid "Move by {:g}, {:g}, {:g}"
msgid "Move by {:g}, {:g}, {:g}"
msgstr "Verplaats met {:g}, {:g}, {:g}"

#: bCNC/ControlPage.py:461
msgid "Move gantry to mouse location [g]"
msgstr "Beweeg het portiek naar huidige muislocatie [g]"

#: bCNC/CNCCanvas.py:445
msgid "Move graphically objects"
msgstr "Verplaats grafische objecten"

#: bCNC/EditorPage.py:433
msgid "Move objects [M]"
msgstr "Beweeg objecten [M]"

#: bCNC/EditorPage.py:532
msgid "Move selected g-code down [Ctrl-Down, Ctrl-PgDn]"
msgstr "Verschuif geselecteerde gcode omlaag [Ctrl-Down, Ctrl-PgDn]"

#: bCNC/EditorPage.py:513
msgid "Move selected g-code up [Ctrl-Up, Ctrl-PgUp]"
msgstr "Verschuif geselecteerde gcode omhoog [Ctrl-Up, Ctrl-PgUp]"

#: bCNC/plugins/simpleTranslate.py:68
#, fuzzy
msgid "Moved selected blocks"
msgstr "Selecteer"

#: bCNC/ControlPage.py:1001 bCNC/ControlPage.py:1477
msgid "Multiply step by 10"
msgstr "Vermenigvuldig stap 10 keer"

#: bCNC/ProbePage.py:1236
msgid "N"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:72
msgid "N line number value is not within the valid range of 1 - 9,999,999."
msgstr ""
"N regel nummer waarde valt niet binnen het geldige bereik van 1- 9,999,999."

#: bCNC/lib/bFileDialog.py:304 bCNC/plugins/Helical_Descent.py:77
#: bCNC/plugins/Random.py:29 bCNC/plugins/arcfit.py:32 bCNC/plugins/bowl.py:112
#: bCNC/plugins/box.py:346 bCNC/plugins/center.py:35
#: bCNC/plugins/difference.py:40 bCNC/plugins/dragknife.py:41
#: bCNC/plugins/driller.py:44 bCNC/plugins/drillmark.py:38
#: bCNC/plugins/endmilloffset.py:437 bCNC/plugins/flatten.py:288
#: bCNC/plugins/function_plot.py:23 bCNC/plugins/gear.py:160
#: bCNC/plugins/halftone.py:37 bCNC/plugins/hilbert.py:111
#: bCNC/plugins/intersection.py:41 bCNC/plugins/jigsaw.py:360
#: bCNC/plugins/linearize.py:33 bCNC/plugins/midi2cnc.py:60
#: bCNC/plugins/scaling.py:38 bCNC/plugins/sketch.py:36
#: bCNC/plugins/spiral.py:471 bCNC/plugins/spirograph.py:118
#: bCNC/plugins/text.py:37 bCNC/plugins/tile.py:27
#: bCNC/plugins/trochoidPath.py:37 bCNC/plugins/trochoidal.py:43
#: bCNC/plugins/trochoidal_3D.py:46 bCNC/plugins/zigzag.py:105
#: bCNC/plugins/heightmap.py:64 bCNC/plugins/pyrograph.py:37
#: bCNC/plugins/slicemesh.py:55 bCNC/ToolsPage.py:680 bCNC/ToolsPage.py:707
#: bCNC/ToolsPage.py:737 bCNC/ToolsPage.py:765 bCNC/ToolsPage.py:937
#: bCNC/ToolsPage.py:998 bCNC/ToolsPage.py:1073 bCNC/ToolsPage.py:1099
#: bCNC/ToolsPage.py:1981
msgid "Name"
msgstr "Naam"

#: bCNC/Utils.py:833
msgid "Name to appear on button"
msgstr "Naam die op de knop moet verschijnen"

#: bCNC/Utils.py:829
msgid "Name:"
msgstr "Naam:"

#: bCNC/controllers/_GenericGRBL.py:38
msgid "Negative value received for an expected positive value."
msgstr "Negatieve waarde ontvangen voor een verwachte positieve waarde."

#: bCNC/FilePage.py:85
msgid "New"
msgstr "Nieuw"

#: bCNC/Ribbon.py:574
msgid "New file"
msgstr "Nieuw bestand"

#: bCNC/FilePage.py:90
msgid "New gcode/dxf file"
msgstr "Nieuw gcode/dxf bestand"

#: bCNC/lib/bFileDialog.py:863
msgid "NewFolder"
msgstr "Nieuwe map"

#: bCNC/plugins/Random.py:45 bCNC/plugins/simpleRotate.py:49
#: bCNC/plugins/simpleTranslate.py:47 bCNC/plugins/tile.py:44
msgid "No g-code blocks selected"
msgstr "Geen g-code blokken geselecteerd"

#: bCNC/plugins/gear.py:161
msgid "No of teeth"
msgstr "Nummer van de tand"

#: bCNC/EditorPage.py:122
msgid "None"
msgstr "Geen"

#: bCNC/bmain.py:2658
msgid "Not gcode file was loaded"
msgstr "Geen gcode bestand werd geladen"

#: bCNC/bmain.py:2035
msgid "Nothing to do"
msgstr "Niets te doen"

#: bCNC/ToolsPage.py:943
msgid "Number"
msgstr "Nummer"

#: bCNC/plugins/trochoidPath.py:60
#, fuzzy
msgid "Number of Tabs 0 = Not Tabs"
msgstr "Hoeveelheid tabs"

#: bCNC/plugins/zigzag.py:106
msgid "Number of lines"
msgstr "Hoeveelheid regels"

#: bCNC/ToolsPage.py:1101
#, fuzzy
msgid "Number of tabs"
msgstr "Hoeveelheid tabs"

#: bCNC/controllers/_GenericGRBL.py:36
msgid "Numeric value format is not valid or missing an expected value."
msgstr ""
"Het formaat van de numerieke waarde is ongeldig of mist een verwachte waarde."

#: bCNC/ControlPage.py:2154
msgid "OFF"
msgstr ""

#: bCNC/ProbePage.py:73
msgid "ONLY before probing"
msgstr "ALLEEN voor het sonderen"

#: bCNC/plugins/endmilloffset.py:14
#, fuzzy
#| msgid "Offset:"
msgid "Offset"
msgstr "Compensatie:"

#: bCNC/plugins/spirograph.py:121
msgid "Offset radius"
msgstr "Compensatie radius"

#: bCNC/plugins/endmilloffset.py:462
#, fuzzy
#| msgid "Offset radius"
msgid "Offset side"
msgstr "Compensatie radius"

#: bCNC/ProbePage.py:836 bCNC/ProbePage.py:1710
msgid "Offset:"
msgstr "Compensatie:"

#: bCNC/Utils.py:877
msgid "Ok"
msgstr "Akkoord"

#: bCNC/controllers/_GenericGRBL.py:115
msgid "Only homing is allowed when a limit switch is engaged. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:816
msgid "Only leave selected islands uncut"
msgstr ""

#: bCNC/lib/bFileDialog.py:337 bCNC/lib/bFileDialog.py:938
#: bCNC/lib/bFileDialog.py:1063 bCNC/FilePage.py:110 bCNC/FilePage.py:376
#: bCNC/bmain.py:2546
msgid "Open"
msgstr "Open"

#: bCNC/FilePage.py:103
msgid "Open existing gcode/dxf file [Ctrl-O]"
msgstr "Open bestaand gcode/dxf bestand [Ctrl-O]"

#: bCNC/bmain.py:2345
msgid "Open file"
msgstr "Open bestand"

#: bCNC/Ribbon.py:584
msgid "Open file [Ctrl-O]"
msgstr "Open bestand [Ctrl-O]"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
msgid "Open paths"
msgstr "Open paden"

#: bCNC/FilePage.py:116
msgid "Open recent file"
msgstr "Open recent bestand"

#: bCNC/Updates.py:186
msgid "Open web browser to download bCNC"
msgstr "Open de webbrowser om bCNC te downloaden"

#: bCNC/ControlPage.py:129
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Open/Close connection"
msgstr "Open/Sluit seriële poort"

#: bCNC/FilePage.py:384
msgid "Open/Close serial port"
msgstr "Open/Sluit seriële poort"

#: bCNC/plugins/endmilloffset.py:443
#, fuzzy
#| msgid "Operation error"
msgid "Operation"
msgstr "Handelingsfout"

#: bCNC/bmain.py:2083
msgid "Operation error"
msgstr "Handelingsfout"

#: bCNC/bmain.py:2036
#, fuzzy
#| msgid "Operation {} requires some gcode to be selected"
msgid "Operation {} requires some gcode to be selected"
msgstr "Handeling {} vereist dat enige gcode geselecteerd is"

#: bCNC/EditorPage.py:490 bCNC/bmain.py:1780
msgid "Optimize"
msgstr "Optimaliseren"

#: bCNC/FilePage.py:168
msgid "Options"
msgstr "Opties"

#: bCNC/plugins/hilbert.py:113 bCNC/EditorPage.py:486
msgid "Order"
msgstr "Volgorde"

#: bCNC/ProbePage.py:677 bCNC/ProbePage.py:822 bCNC/bmain.py:138
msgid "Orient"
msgstr "Orienteer"

#: bCNC/EditorPage.py:441
msgid "Origin"
msgstr "Oorsprong"

#: bCNC/plugins/box.py:355 bCNC/plugins/trochoidPath.py:52
#: bCNC/plugins/trochoidal_3D.py:76 bCNC/ToolsPage.py:1018
msgid "Overcut"
msgstr "Overmaat"

#: bCNC/plugins/endmilloffset.py:485
#, fuzzy
#| msgid "Overcut"
msgid "Overcut corners"
msgstr "Overmaat"

#: bCNC/lib/bFileDialog.py:985
#, fuzzy
#| msgid "Overwrite existing file %r?"
msgid "Overwrite existing file {}?"
msgstr "Bestaand bestand {} overschrijven?"

#: bCNC/ProbePage.py:496
msgid "POINT"
msgstr ""

#: bCNC/CNCCanvas.py:435
msgid "Pan viewport"
msgstr "Draai kader"

#: bCNC/CNCCanvas.py:2300
msgid "Pan viewport [X]"
msgstr "Draai kader [X]"

#: bCNC/TerminalPage.py:96
msgid "Parameters"
msgstr "Parameters"

#: bCNC/EditorPage.py:49
msgid "Paste"
msgstr "Plak"

#: bCNC/EditorPage.py:55
msgid "Paste [Ctrl-V]"
msgstr "Plak [Ctrl-V]"

#: bCNC/ControlPage.py:192
msgid "Pause"
msgstr "Pauzeer"

#: bCNC/ControlPage.py:214
msgid "Pause running program and soft reset controller to empty the buffer."
msgstr ""
"Pauzeer het programma en voer een soft reset uit op de controller om de "
"buffer te legen."

#: bCNC/ControlPage.py:199
msgid "Pause running program. Sends either FEED_HOLD ! or CYCLE_START ~"
msgstr "Pauzeer het programma. Dit verstuurt FEED_HOLD ! of CYCLE_START ~"

#: bCNC/ProbePage.py:1985
msgid "Pause:"
msgstr "Pauzeer:"

#: bCNC/ToolsPage.py:940
msgid "Peck depth"
msgstr "Pik diepte"

#: bCNC/plugins/simpleDrill.py:70
#, fuzzy
#| msgid "Peck depth"
msgid "Peck depth (positive)"
msgstr "Pik diepte"

#: bCNC/plugins/driller.py:47
#, fuzzy
#| msgid "Peck, 0 meas None"
msgid "Peck, 0 means None"
msgstr "Pik, 0 betekent Geen"

#: bCNC/FilePage.py:232 bCNC/bmain.py:2713 bCNC/bmain.py:2719
#: bCNC/bmain.py:2733
#, fuzzy
msgid "Pendant"
msgstr "Pendant"

#: bCNC/bmain.py:2720
#, fuzzy
msgid "Pendant already started:\n"
msgstr "Pendant al gestart:\n"

#: bCNC/bmain.py:2714
#, fuzzy
msgid "Pendant started:\n"
msgstr "Pendant gestart:\n"

#: bCNC/bmain.py:2734
#, fuzzy
msgid "Pendant stopped"
msgstr "Pendant gestopt"

#: bCNC/ProbePage.py:2135
msgid "Perform a calibration probing to determine the height"
msgstr "Voer met de sonde een kalibratie uit om de hoogte te bepalen"

#: bCNC/ControlPage.py:100
#, fuzzy
#| msgid "Perform a homing cycle [$H]"
msgid "Perform a homing cycle [$H] now"
msgstr "Voer een homing cyclus uit [$H]"

#: bCNC/ToolsPage.py:1613
msgid "Perform a pocket operation on selected code"
msgstr ""

#: bCNC/ToolsPage.py:1595
msgid "Perform a profile operation on selected code"
msgstr ""

#: bCNC/ProbePage.py:1935
#, fuzzy
msgid "Perform a single a tool change cycle to set the calibration field"
msgstr ""
"Voer een enkele gereedschapswijzigingscyclus uit om het kalibratieveld in te "
"stellen"

#: bCNC/ProbePage.py:636
msgid "Perform a single probe cycle"
msgstr "Voer een enkele probecyclus uit"

#: bCNC/ProbePage.py:1951
msgid "Perform a tool change cycle"
msgstr "Voer een gereedschapswijzigingscyclus uit"

#: bCNC/plugins/jigsaw.py:363
msgid "Piece count"
msgstr "Aantal stuks"

#: bCNC/ControlPage.py:1978
msgid "Plane [G17,G18,G19]"
msgstr "Vlak [G17,G18,G19]"

#: bCNC/controllers/_GenericGRBL.py:112
msgid "Plane must be ZX for threading. (grblHAL)"
msgstr ""

#: bCNC/ControlPage.py:1967
msgid "Plane:"
msgstr "Vlak:"

#: bCNC/ToolsPage.py:1912
msgid "Please restart the program."
msgstr "Start het programma alstublieft opnieuw."

#: bCNC/bmain.py:1781
msgid "Please select the blocks of gcode you want to optimize."
msgstr "Selecteer alstublieft de blokken gcode die u wil optimaliseren."

#: bCNC/bmain.py:2609
msgid "Please stop before"
msgstr "Stop alstublieft voordat"

#: bCNC/ToolsPage.py:651
msgid "Plotting Arc accuracy"
msgstr "Nauwkeurigheid van het plotten van een boog"

#: bCNC/plugins/scaling.py:44 bCNC/plugins/trochoidal_3D.py:96
#: bCNC/ToolsPage.py:683 bCNC/ToolsPage.py:770
msgid "Plunge Feed"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:415
msgid "Plunge Feed has to be greater than 0"
msgstr ""

#: bCNC/ToolsPage.py:1025 bCNC/ToolsPage.py:1604
msgid "Pocket"
msgstr ""

#: bCNC/plugins/flatten.py:299
msgid "Pocket type"
msgstr ""

#: bCNC/ProbePage.py:1164
msgid "Pointrec"
msgstr ""

#: bCNC/ProbePage.py:1967
msgid "Policy:"
msgstr "Beleid:"

#: bCNC/FilePage.py:294
msgid "Port:"
msgstr "Poort:"

#: bCNC/ProbePage.py:596
msgid "Pos:"
msgstr "Positie:"

#: bCNC/EditorPage.py:209
msgid "Postprocess Inkscape g-code"
msgstr ""

#: bCNC/plugins/gear.py:162
msgid "Pressure angle"
msgstr "Druk hoek"

#: bCNC/ProbePage.py:106 bCNC/ProbePage.py:114 bCNC/ProbePage.py:539
#: bCNC/ProbePage.py:627 bCNC/bmain.py:137
msgid "Probe"
msgstr "Sonde"

#: bCNC/ProbePage.py:987
msgid "Probe Center Error"
msgstr "Sonde Centrum Fout"

#: bCNC/ProbePage.py:369
msgid "Probe Command"
msgstr "Sonde Commando"

#: bCNC/ProbePage.py:922 bCNC/ProbePage.py:955 bCNC/ProbePage.py:1411
#: bCNC/ProbePage.py:1420 bCNC/ProbePage.py:1435 bCNC/ProbePage.py:1444
#: bCNC/ProbePage.py:1456 bCNC/ProbePage.py:1465 bCNC/ProbePage.py:1474
msgid "Probe Error"
msgstr "Sonde fout"

#: bCNC/ProbePage.py:328
msgid "Probe Feed:"
msgstr "Sondevoeding:"

#: bCNC/bmain.py:2390
msgid "Probe File modified"
msgstr "Sonde bestand is gewijzigd"

#: bCNC/ProbePage.py:2198 bCNC/ProbePage.py:2210 bCNC/ProbePage.py:2220
#: bCNC/ProbePage.py:2230 bCNC/ProbePage.py:2240
msgid "Probe Tool Change Error"
msgstr "Fout bij wisselen van gereedschap van sonde"

#: bCNC/ProbePage.py:603
msgid "Probe along X direction"
msgstr "Sonde in X-richting"

#: bCNC/ProbePage.py:611
msgid "Probe along Y direction"
msgstr "Sonde in Y-richting"

#: bCNC/ProbePage.py:619
msgid "Probe along Z direction"
msgstr "Sonde in Z-richting"

#: bCNC/ProbePage.py:2366
msgid "Probe configuration and probing"
msgstr "Sonde configuratie en sondering"

#: bCNC/ProbePage.py:902
msgid "Probe connected?"
msgstr "Sonde verbonden?"

#: bCNC/controllers/_GenericGRBL.py:155
msgid "Probe protection triggered. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/bmain.py:2391
msgid "Probe was modified do you want to save it first?"
msgstr "Sonde is gewijzigd, wilt u het eerst opslaan?"

#: bCNC/ProbePage.py:543 bCNC/ProbePage.py:2057
msgid "Probe:"
msgstr "Sonde:"

#: bCNC/ProbePage.py:655
msgid "Probing ring internal diameter"
msgstr "Sonde ring interne diameter"

#: bCNC/plugins/box.py:354 bCNC/ToolsPage.py:1586
msgid "Profile"
msgstr "Profiel"

#: bCNC/bmain.py:2129 bCNC/bmain.py:2231
#, fuzzy
#| msgid "Profile block distance={:g}"
msgid "Profile block distance={:g}"
msgstr "Profiel blok afstand={:g}"

#: bCNC/plugins/bowl.py:115
msgid "Progressive"
msgstr "Progressief"

#: bCNC/Updates.py:74
msgid "Published at:"
msgstr "Uitgegeven op:"

#: bCNC/Updates.py:80
msgid "Published date of the latest github release"
msgstr "Publiceerdatum van de laatste github versie"

#: bCNC/plugins/midi2cnc.py:61
msgid "Pulse per unit for X"
msgstr "Puls per eenheid voor X"

#: bCNC/plugins/midi2cnc.py:62
msgid "Pulse per unit for Y"
msgstr "Puls per eenheid voor Y"

#: bCNC/plugins/midi2cnc.py:63
msgid "Pulse per unit for Z"
msgstr "Puls per eenheid voor Z"

#: bCNC/plugins/pyrograph.py:13
msgid "Pyrograph"
msgstr "Pyrograaf"

#: bCNC/plugins/pyrograph.py:97
msgid "Pyrograph abort: Can't read image file"
msgstr "Pyrograaf fout: Kan deze afbeelding niet lezen"

#: bCNC/plugins/pyrograph.py:85
msgid "Pyrograph abort: Please check feed rate parameters"
msgstr ""

#: bCNC/plugins/pyrograph.py:58
msgid "Pyrograph abort: This plugin requires PIL/Pillow"
msgstr "Pyrograaf fout: Deze plugin vereist PIL/Pillow"

#: bCNC/plugins/pyrograph.py:80
msgid "Pyrograph abort: Tool Size must be > 0"
msgstr "Pyrograaf fout: Gereedschapsgrootte moet groter dan 0 zijn"

#: bCNC/plugins/pyrograph.py:76
msgid "Pyrograph abort: please define a scan Direction"
msgstr "Pyrograaf fout: Specificeer alstublieft een scanrichting"

#: bCNC/plugins/pyrograph.py:38
msgid "Pyrograph tip size"
msgstr ""

#: bCNC/ProbePage.py:474
msgid "RAPID"
msgstr ""

#: bCNC/plugins/scaling.py:45 bCNC/plugins/trochoidal_3D.py:94
msgid "RPM"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:114
msgid "RPM out of range. (grblHAL)"
msgstr ""

#: bCNC/plugins/trochoidal.py:51
msgid "Radial depth of cut (<= cutter D * 0.4)"
msgstr ""

#: bCNC/plugins/simpleArc.py:67
msgid "Radius"
msgstr ""

#: bCNC/ToolsPage.py:781
msgid "Ramp length"
msgstr ""

#: bCNC/plugins/Random.py:15
msgid "Random"
msgstr ""

#: bCNC/plugins/jigsaw.py:364
msgid "Random seed"
msgstr "Willekeurige seed"

#: bCNC/plugins/function_plot.py:26
msgid "Range of X"
msgstr ""

#: bCNC/plugins/function_plot.py:27
msgid "Range of Y"
msgstr ""

#: bCNC/ProbePage.py:592
msgid "Rapid goto to last probe location"
msgstr "Ga snel naar de laatste sonde locatie"

#: bCNC/bmain.py:1213
msgid "Rapid:"
msgstr "Snel:"

#: bCNC/plugins/flatten.py:294
msgid "Raster border"
msgstr "Rasterrand"

#: bCNC/ProbePage.py:452
msgid "Record"
msgstr ""

#: bCNC/ProbePage.py:468
msgid "Record Z coordinate?"
msgstr ""

#: bCNC/Ribbon.py:624
msgid "Redo [Ctrl-Y]"
msgstr "Opnieuw doen [Ctrl-Y]"

#: bCNC/CNCCanvas.py:2425
msgid "Redraw display [Ctrl-R]"
msgstr "Weergave opnieuw tekenen [Ctrl-R]"

#: bCNC/plugins/spiral.py:464
msgid "Reduce Diameter of 4th Axis Stock"
msgstr ""

#: bCNC/FilePage.py:361
msgid "Refresh"
msgstr ""

#: bCNC/FilePage.py:367
#, fuzzy
#| msgid "Open/Close serial port"
msgid "Refresh list of serial ports"
msgstr "Open/Sluit seriële poort"

#: bCNC/ProbePage.py:1740
msgid "Register:"
msgstr "Registreer:"

#: bCNC/ToolsPage.py:1455
msgid "Rename"
msgstr "Naam wijzigen"

#: bCNC/plugins/sketch.py:51
msgid "Repetition of a point"
msgstr ""

#: bCNC/FilePage.py:189 bCNC/Utils.py:568
msgid "Report"
msgstr "Melden"

#: bCNC/Utils.py:701
msgid "Report successfully send"
msgstr "Rapport succesvol verzonden"

#: bCNC/Utils.py:702
msgid "Report was successfully uploaded to web site"
msgstr "Het rapport is succesvol geüpload naar de website"

#: bCNC/ControlPage.py:136 bCNC/ControlPage.py:2080
msgid "Reset"
msgstr "Reset"

#: bCNC/ControlPage.py:2082
msgid "Reset override to 100%"
msgstr "Reset overschrijving naar 100%"

#: bCNC/plugins/function_plot.py:25
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "Resolution"
msgstr "Resolutie (graden)"

#: bCNC/plugins/bowl.py:114
msgid "Resolution (degrees)"
msgstr "Resolutie (graden)"

#: bCNC/TerminalPage.py:67
msgid "Restore All"
msgstr "Alles Herstellen"

#: bCNC/TerminalPage.py:64
msgid "Restore Settings"
msgstr "Instellingen Herstellen"

#: bCNC/TerminalPage.py:66
msgid "Restore Workspace"
msgstr "Werkomgeving Herstellen"

#: bCNC/ControlPage.py:493
msgid "Resume"
msgstr "Hervat"

#: bCNC/ControlPage.py:1517
msgid "Return ABC to 0."
msgstr ""

#: bCNC/EditorPage.py:691
msgid "Reverse"
msgstr "Omkeren"

#: bCNC/EditorPage.py:699
msgid "Reverse cut direction for selected gcode blocks"
msgstr "Keer snijrichting om voor geselecteerde gcode blokken"

#: bCNC/EditorPage.py:466
msgid "Right"
msgstr "Rechts"

#: bCNC/plugins/drillmark.py:58
msgid "Ring mark (d/2)"
msgstr ""

#: bCNC/plugins/spiral.py:94
#, fuzzy
#| msgid "All accepted"
msgid "Risk Accepted"
msgstr "Alles geaccepteerd"

#: bCNC/plugins/spiral.py:475
msgid "Rotary Alignment Axis"
msgstr ""

#: bCNC/plugins/spiral.py:476
msgid "Rotary Axis"
msgstr ""

#: bCNC/EditorPage.py:588
msgid "Rotate selected gcode by 180deg"
msgstr "Draai geselecteerde gcode 180 graden"

#: bCNC/EditorPage.py:573
msgid "Rotate selected gcode clock-wise (-90deg)"
msgstr "Draai geselecteerde gcode met de klok mee (-90 graden)"

#: bCNC/EditorPage.py:604
msgid "Rotate selected gcode counter-clock-wise (90deg)"
msgstr "Draai geselecteerde gcode tegen de klok in (90 graden)"

#: bCNC/plugins/simpleRotate.py:68
#, fuzzy
#| msgid "Create holes along selected blocks"
msgid "Rotated selected blocks"
msgstr "Maak gaten langs geselecteerde blokken"

#: bCNC/plugins/simpleRotate.py:19
msgid "Rotates a block to a new position"
msgstr ""

#: bCNC/ProbePage.py:1647
msgid "Rotation:"
msgstr "Omwenteling:"

#: bCNC/EditorPage.py:213
msgid "Round"
msgstr "Roond"

#: bCNC/EditorPage.py:647
msgid "Route"
msgstr "Route"

#: bCNC/CNCCanvas.py:2329
msgid "Ruler [R]"
msgstr "Liniaal [R]"

#: bCNC/Sender.py:685
msgid "Run ended"
msgstr "Uitvoeren beëindigd"

#: bCNC/ControlPage.py:184
msgid "Run g-code commands from editor to controller"
msgstr "Voer g-code commando's uit van bewerkingsprogramma naar controller"

#: bCNC/bmain.py:635
msgid "Running"
msgstr "Uitvoeren"

#: bCNC/Updates.py:65
msgid "Running version of bCNC"
msgstr "Uitgevoerde versie van bCNC"

#: bCNC/bmain.py:2695
msgid "Running..."
msgstr "Uitvoeren..."

#: bCNC/controllers/_GenericGRBL.py:123
msgid "SD Card directory listing failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:124
msgid "SD Card directory not found. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:125
msgid "SD Card file empty. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:122
msgid "SD Card file open/read failed. (grblHAL bdring)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:121
msgid "SD Card mount failed. (grblHAL bdring)"
msgstr ""

#: bCNC/plugins/slicemesh.py:40
msgid "STL/PLY Slicer"
msgstr ""

#: bCNC/ToolsPage.py:740
msgid "Safe Z"
msgstr "Veilige Z"

#: bCNC/plugins/simpleDrill.py:73
msgid "Safe z secu for G0"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:53
msgid "Safety door detected as opened and door state initiated."
msgstr "Veiligheidsdeur gedetecteerd als geopend en deurstatus geïnitieerd."

#: bCNC/lib/bFileDialog.py:977 bCNC/FilePage.py:153
msgid "Save"
msgstr "Opslaan"

#: bCNC/lib/bFileDialog.py:972
msgid "Save As"
msgstr "Opslaan Als"

#: bCNC/Ribbon.py:594
msgid "Save all [Ctrl-S]"
msgstr "Sla alles op [Ctrl-S]"

#: bCNC/bmain.py:2366
msgid "Save file"
msgstr "Sla bestand op"

#: bCNC/FilePage.py:159
msgid "Save gcode/dxf AS"
msgstr "Sla gcode/dxf op als"

#: bCNC/FilePage.py:145
msgid "Save gcode/dxf file [Ctrl-S]"
msgstr "Sla gcode/dxf bestand op [Ctrl-S]"

#: bCNC/ProbePage.py:1680
msgid "Scale:"
msgstr "Schaal:"

#: bCNC/plugins/scaling.py:22
msgid "Scaling"
msgstr ""

#: bCNC/plugins/scaling.py:291
msgid "Scaling Generated"
msgstr ""

#: bCNC/plugins/scaling.py:175
msgid "Scaling abort: Please select some path"
msgstr ""

#: bCNC/plugins/scaling.py:30
msgid "Scaling the selected block"
msgstr ""

#: bCNC/plugins/heightmap.py:67 bCNC/ProbePage.py:241 bCNC/ProbePage.py:271
msgid "Scan"
msgstr "Scan"

#: bCNC/ProbePage.py:247
msgid "Scan Autolevel Margins"
msgstr ""

#: bCNC/ProbePage.py:280
msgid "Scan probed area for level information on Z plane"
msgstr "Scan gesondeerd gebied voor niveau informatie op het Z-vlak"

#: bCNC/plugins/heightmap.py:72
msgid "ScanDir"
msgstr "ScanMap"

#: bCNC/EditorPage.py:96
msgid "Select"
msgstr "Selecteer"

#: bCNC/FilePage.py:307
msgid "Select (or manual enter) port to connect"
msgstr "Selecteer (of geef op) een poort om te verbinden"

#: bCNC/EditorPage.py:112
msgid "Select all blocks [Ctrl-A]"
msgstr "Selecteer alle blokken [Ctrl-A]"

#: bCNC/EditorPage.py:160
msgid "Select all blocks from current layer"
msgstr "Selecteer alle blokken van huidige laag"

#: bCNC/FilePage.py:323
msgid "Select connection baud rate"
msgstr "Selecteer verbinding baud rate"

#: bCNC/FilePage.py:340
msgid "Select controller board"
msgstr "Selecteer controllerkaart"

#: bCNC/CNCCanvas.py:430
msgid "Select objects with mouse"
msgstr "Selecteer objecten met de muis"

#: bCNC/ProbePage.py:696
msgid "Select orientation marker"
msgstr "Selecteer oriëntatie marker"

#: bCNC/ControlPage.py:2078
msgid "Select override type."
msgstr "Selecteer overschrijvingstype."

#: bCNC/CNCCanvas.py:2288
msgid "Select tool [S]"
msgstr "Selecteer gereedschap [S]"

#: bCNC/plugins/Helical_Descent.py:79
#, fuzzy
msgid "Selected Block"
msgstr "Selecteer"

#: bCNC/FilePage.py:197
msgid "Send Error Report"
msgstr "Verstuur foutrapport"

#: bCNC/ProbePage.py:66
msgid "Send M6 commands"
msgstr "Verstuur M6 commando"

#: bCNC/Utils.py:617
msgid "Send report"
msgstr "Verstuur rapport"

#: bCNC/FilePage.py:289
msgid "Serial"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial Error"
msgstr "Serial fout"

#: bCNC/TerminalPage.py:241
msgid "Serial Terminal"
msgstr ""

#: bCNC/bmain.py:2601
msgid "Serial is not connected"
msgstr "Serial niet verbonden"

#: bCNC/ControlPage.py:735
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set A coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:801
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set ABC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:751
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set B coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:785
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set BC coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:767
#, fuzzy
#| msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgid "Set C coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:2101
#, fuzzy
msgid "Set Feed/Rapid/Spindle Override. Right or Double click to reset."
msgstr ""
"Stel Feed/Rapid/Spindle overschrijving in. Rechtermuisklik op dubbelklikken "
"om te resetten."

#: bCNC/ControlPage.py:436
msgid "Set WPOS"
msgstr "Stel WPOS in"

#: bCNC/ControlPage.py:445
msgid "Set WPOS to mouse location"
msgstr "Stel WPOS in op muislocatie"

#: bCNC/ControlPage.py:357
msgid "Set X coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel X-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:407
msgid "Set XY coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:424
msgid "Set XYZ coordinate to zero (or to typed coordinate in WPos)"
msgstr ""

#: bCNC/ControlPage.py:373
msgid "Set Y coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel Y-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ControlPage.py:389
msgid "Set Z coordinate to zero (or to typed coordinate in WPos)"
msgstr "Stel Z-coördinaat in op nul (of getypte coordinaat in WPos)"

#: bCNC/ProbePage.py:320
msgid "Set initial probe feed rate for tool change and calibration"
msgstr ""
"Stel de beginsnelheid voor de sondetoevoer in voor wisseling van gereedschap "
"en kalibratie"

#: bCNC/CNCCanvas.py:458
msgid "Set mouse location as current machine position (X/Y only)"
msgstr "Stel muispositie in als huidige machinepositie (Alleen X/Y)"

#: bCNC/ControlPage.py:2056
msgid "Set position [G92 X# Y# Z#]"
msgstr ""

#: bCNC/ProbePage.py:340
msgid "Set probe feed rate"
msgstr "Stel sondevoeding snelheid in"

#: bCNC/ControlPage.py:2135
msgid "Set spindle RPM"
msgstr "Stel spoel RPM in"

#: bCNC/ProbePage.py:354
msgid "Set tool offset for probing"
msgstr "Stel gereedschapscorrectie in voor sondering"

#: bCNC/controllers/_GenericController.py:163
#, fuzzy
#| msgid "Set workspace {} to {}"
msgid "Set workspace {} to {}"
msgstr "Stel werkruimte {} in op {}"

#: bCNC/ToolsPage.py:1019
msgid "Sets if we want to overcut or not."
msgstr ""

#: bCNC/TerminalPage.py:80
msgid "Settings"
msgstr "Instellingen"

#: bCNC/ProbePage.py:165
msgid "Setup probing for manual tool change"
msgstr "Stel sonderen in voor het handmatig gereedschap wijzigen"

#: bCNC/ToolsPage.py:710
msgid "Shape"
msgstr "Vorm"

#: bCNC/plugins/jigsaw.py:369
msgid "Shape of the tap"
msgstr "Vorm van de tap"

#: bCNC/ToolsPage.py:579 bCNC/ToolsPage.py:580 bCNC/ToolsPage.py:581
#: bCNC/ToolsPage.py:582 bCNC/ToolsPage.py:583 bCNC/ToolsPage.py:584
#: bCNC/ToolsPage.py:585 bCNC/ToolsPage.py:586 bCNC/ToolsPage.py:587
#: bCNC/ToolsPage.py:588 bCNC/ToolsPage.py:589 bCNC/ToolsPage.py:590
msgid "Shift-"
msgstr "Shift-"

#: bCNC/ToolsPage.py:1837
msgid "Shortcuts"
msgstr "Snelkoppelingen"

#: bCNC/ToolsPage.py:1845
msgid "Shortcuts configuration"
msgstr "Snelkoppelingen configuratie"

#: bCNC/ToolsPage.py:1011
msgid "Should we machine on inside or outside of the shape?"
msgstr ""

#: bCNC/ControlPage.py:473
msgid "Show Info"
msgstr "Laat Informatie zien"

#: bCNC/EditorPage.py:773
msgid "Show cutting information on selected blocks [Ctrl-n]"
msgstr "Laat snijinformatie zien voor geselecteerde blokken [Ctrl-n]"

#: bCNC/EditorPage.py:757
msgid "Show statistics for enabled gcode"
msgstr "Laat statistieken zien voor ingeschakelde gcode"

#: bCNC/ProbePage.py:121
msgid "Simple probing along a direction"
msgstr "Simpel sonderen langs een richting"

#: bCNC/plugins/heightmap.py:77
msgid "Single pass"
msgstr "Enkele pas"

#: bCNC/lib/bFileDialog.py:306 bCNC/plugins/hilbert.py:112
msgid "Size"
msgstr "Grootte"

#: bCNC/ToolsPage.py:1004
msgid "Size of this endmill will be used as offset distance"
msgstr ""

#: bCNC/plugins/sketch.py:20
msgid "Sketch"
msgstr "Schets"

#: bCNC/plugins/sketch.py:255
msgid "Sketch abort: Can't read image file"
msgstr "Schetsfout: Kan afbeelding niet lezen"

#: bCNC/plugins/sketch.py:244
msgid "Sketch abort: Please let me draw at least 1 squiggle"
msgstr "Schetsfout: Laat mij alstublieft ten minste 1 squiggle tekenen"

#: bCNC/plugins/sketch.py:248
msgid "Sketch abort: Squiggle Length must be > 0"
msgstr "Schetsfout: Squiggle lengte moet groter dan 0 zijn"

#: bCNC/plugins/sketch.py:200
msgid "Sketch abort: This plugin requires PIL/Pillow to read image data"
msgstr ""
"Schetsfout: Deze plugin vereist dat PIL/Pillow afbeeldingsgegevens leest"

#: bCNC/plugins/sketch.py:234
msgid "Sketch abort: Too small to draw anything!"
msgstr "Schetsfout: Te klein om iets te tekenen!"

#: bCNC/plugins/slicemesh.py:179
msgid "Slicing {} {:f} in {:f} -> {:f} of {}"
msgstr ""

#: bCNC/plugins/function_plot.py:34
#, fuzzy
#| msgid "Line length"
msgid "Small line length"
msgstr "Regellengte"

#: bCNC/controllers/_GenericGRBL.py:46
msgid "Soft limits cannot be enabled without homing also enabled."
msgstr ""
"Zachte limieten kunnen niet worden ingeschakeld tenzij homing ingeschakeld "
"is."

#: bCNC/ControlPage.py:143
msgid "Software reset of controller [ctrl-x]"
msgstr "Software reset van controller [ctrl-x]"

#: bCNC/plugins/midi2cnc.py:32
msgid "Sound your machine from a midi file"
msgstr "Speel audio af van een midi bestand"

#: bCNC/ControlPage.py:2112
msgid "Spindle"
msgstr "Spoel"

#: bCNC/ProbePage.py:1737
msgid "Spindle Z position when camera was registered"
msgstr "Spoel Z positie"

#: bCNC/controllers/_GenericGRBL.py:156
msgid "Spindle at speed timeout. Clear before continuing. (grblHAL)"
msgstr ""

#: bCNC/ToolsPage.py:654
msgid "Spindle max (RPM)"
msgstr "Spoel maximale RPM"

#: bCNC/ToolsPage.py:653
msgid "Spindle min (RPM)"
msgstr "Spoel minimale RPM"

#: bCNC/ProbePage.py:1854
msgid "Spindle position is not registered"
msgstr "Spoel positie is niet geregistreerd"

#: bCNC/ProbePage.py:1846
msgid "Spindle position is registered"
msgstr "Spoel positie is geregistreerd"

#: bCNC/ProbePage.py:1855
msgid "Spindle position must be registered before camera"
msgstr "Spoel positie moet geregistreerd zijn voor de camera"

#: bCNC/plugins/spiral.py:18
#, fuzzy
#| msgid "Spirograph"
msgid "Spiral"
msgstr "Spirograaf"

#: bCNC/plugins/spiral.py:69
#, fuzzy
msgid "Spiral abort: Approach height must be greater than Z Start"
msgstr "Hoogtekaart fout: diepte moet lager zijn dan 0"

#: bCNC/plugins/spiral.py:74
msgid "Spiral abort: Depth Reduction must be negative"
msgstr ""

#: bCNC/plugins/spiral.py:156 bCNC/plugins/spiral.py:179
#: bCNC/plugins/spiral.py:192 bCNC/plugins/spiral.py:220
#: bCNC/plugins/spiral.py:284 bCNC/plugins/spiral.py:318
#: bCNC/plugins/spiral.py:357 bCNC/plugins/spiral.py:379
#: bCNC/plugins/spiral.py:430
msgid "Spiral abort: Rotary Axis Not Assigned."
msgstr ""

#: bCNC/plugins/spiral.py:60
#, fuzzy
msgid "Spiral abort: Rotary Axis is undefined"
msgstr "Fout tijdens afplatten: Pocket Type is niet gespecificeerd"

#: bCNC/plugins/spiral.py:64
#, fuzzy
msgid "Spiral abort: Spiral Type is undefined"
msgstr "Fout tijdens afplatten: Pocket Type is niet gespecificeerd"

#: bCNC/plugins/spiral.py:80
msgid "Spiral abort: Step Over exceeds tool limits"
msgstr ""

#: bCNC/plugins/spiral.py:124
#, fuzzy
#| msgid "Flatten abort: Flatten area is too small for this End Mill."
msgid "Spiral abort: Stock Length is too small for this End Mill."
msgstr "Fout tijdens afplatten: Gebied is te klein voor deze vingerfrees."

#: bCNC/plugins/spiral.py:100
#, fuzzy
msgid "Spiral abort: Stock Length to cut must be positive"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/spiral.py:518
msgid "Spiral: Reduced 4th Axis Stock"
msgstr ""

#: bCNC/plugins/spirograph.py:17
msgid "Spirograph"
msgstr "Spirograaf"

#: bCNC/EditorPage.py:401
msgid "Split"
msgstr ""

#: bCNC/EditorPage.py:407
msgid "Split selected blocks"
msgstr ""

#: bCNC/ToolsPage.py:795
msgid "Spring pass"
msgstr ""

#: bCNC/plugins/sketch.py:46
msgid "Squiggle Length"
msgstr ""

#: bCNC/plugins/sketch.py:45
msgid "Squiggle total count"
msgstr ""

#: bCNC/ControlPage.py:178 bCNC/FilePage.py:238
msgid "Start"
msgstr "Start"

#: bCNC/plugins/simpleArc.py:68
msgid "Start Angle in Degrees"
msgstr ""

#: bCNC/ControlPage.py:2175
msgid "Start flood (M8)"
msgstr ""

#: bCNC/ControlPage.py:2189
msgid "Start mist (M7)"
msgstr ""

#: bCNC/FilePage.py:246
#, fuzzy
msgid "Start pendant"
msgstr "Start pendant"

#: bCNC/ToolsPage.py:652
msgid "Start up"
msgstr "Beginnen"

#: bCNC/ControlPage.py:2121
msgid "Start/Stop spindle (M3/M5)"
msgstr "Start/Stop spoel (M3/M5)"

#: bCNC/TerminalPage.py:140
msgid "Startup"
msgstr "Opstarten"

#: bCNC/ControlPage.py:1877 bCNC/TerminalPage.py:110
msgid "State"
msgstr "Staat"

#: bCNC/ControlPage.py:611 bCNC/ControlPage.py:918
#, fuzzy
#| msgid "State: {}"
msgid "State: {}"
msgstr "Staat: {}"

#: bCNC/EditorPage.py:750 bCNC/bmain.py:1133
msgid "Statistics"
msgstr "Statistieken"

#: bCNC/ControlPage.py:236
msgid "Status:"
msgstr "Status:"

#: bCNC/ProbePage.py:1234
msgid "Step"
msgstr "Stap"

#: bCNC/plugins/zigzag.py:109
msgid "Step distance"
msgstr "Stapafstand"

#: bCNC/ControlPage.py:1562
#, fuzzy
#| msgid "Step for Z move operation"
msgid "Step for A move operation"
msgstr "Stap voor Z verplaatsingshandeling"

#: bCNC/ControlPage.py:1090
msgid "Step for Z move operation"
msgstr "Stap voor Z verplaatsingshandeling"

#: bCNC/ControlPage.py:1075 bCNC/ControlPage.py:1546
msgid "Step for every move operation"
msgstr "Stap voor elke verplaatsingshandeling"

#: bCNC/ControlPage.py:1265 bCNC/ControlPage.py:1739
#, fuzzy
#| msgid "Step: {:g}"
msgid "Step: {:g}"
msgstr "Stap: {:g}"

#: bCNC/ControlPage.py:1744
#, fuzzy
#| msgid "Step: {:g}    Zstep:{:g} "
msgid "Step: {:g}   Astep:{:g} "
msgstr "Stap: {:g}    Zstap:{:g} "

#: bCNC/ControlPage.py:1270
#, fuzzy
#| msgid "Step: {:g}    Zstep:{:g} "
msgid "Step: {:g}  Zstep: {:g} "
msgstr "Stap: {:g}    Zstap:{:g} "

#: bCNC/ToolsPage.py:718
msgid "Stepover %"
msgstr ""

#: bCNC/plugins/spiral.py:480
msgid "Stepover (spiral=mm, lines=deg)"
msgstr ""

#: bCNC/ToolsPage.py:1405
msgid "Stock"
msgstr "Voorraad"

#: bCNC/ToolsPage.py:1413
msgid "Stock material currently on machine"
msgstr "Voorraadmateriaal wordt momenteel door de machine gebruikt"

#: bCNC/ControlPage.py:208 bCNC/FilePage.py:251
msgid "Stop"
msgstr "Stop"

#: bCNC/ControlPage.py:2161
msgid "Stop cooling (M9)"
msgstr ""

#: bCNC/FilePage.py:259
#, fuzzy
msgid "Stop pendant"
msgstr "Stop pendant"

#: bCNC/ToolsPage.py:741 bCNC/ToolsPage.py:766
msgid "Surface Z"
msgstr "Oppervlak Z"

#: bCNC/ProbePage.py:1543
msgid "Switch To"
msgstr "Schakel naar"

#: bCNC/ProbePage.py:1553
msgid "Switch between camera and spindle"
msgstr "Schakel tussen camera en spoel"

#: bCNC/ControlPage.py:1904
#, fuzzy
#| msgid "Switch to workspace {}"
msgid "Switch to workspace {}"
msgstr "Schakel over naar werkruimte {}"

#: bCNC/ProbePage.py:347
msgid "TLO"
msgstr "TLO"

#: bCNC/ControlPage.py:2029
msgid "TLO:"
msgstr ""

#: bCNC/ToolsPage.py:1638
msgid "Tabs"
msgstr ""

#: bCNC/plugins/trochoidPath.py:63
#, fuzzy
#| msgid "Diameter"
msgid "Tabs Diameter"
msgstr "Diameter"

#: bCNC/plugins/trochoidPath.py:64
#, fuzzy
#| msgid "Height"
msgid "Tabs Height"
msgstr "Hoogte"

#: bCNC/ToolsPage.py:1153
#, fuzzy
msgid "Tabs error"
msgstr "Tabs fout"

#: bCNC/plugins/driller.py:46 bCNC/plugins/spirograph.py:122
#: bCNC/plugins/trochoidPath.py:53 bCNC/plugins/trochoidal_3D.py:82
#: bCNC/ToolsPage.py:767 bCNC/ToolsPage.py:939
msgid "Target Depth"
msgstr "Diepte van het doel"

#: bCNC/plugins/simpleDrill.py:69
msgid "Target z (negative under surface)"
msgstr ""

#: bCNC/TerminalPage.py:38 bCNC/TerminalPage.py:189
msgid "Terminal"
msgstr "Terminal"

#: bCNC/TerminalPage.py:205
msgid "Terminal communication with controller"
msgstr ""

#: bCNC/plugins/text.py:94
#, fuzzy
#| msgid "Text abort: That embarrassing, I can't read this font file!"
msgid "Text abort: That's embarrassing, I can't read this font file!"
msgstr "Tekst fout: ik kan dit lettertype bestand niet lezen!"

#: bCNC/plugins/text.py:62
msgid "Text abort: please input a Font size > 0"
msgstr "Tekst fout: lettertype moet hoger zijn dan 0"

#: bCNC/plugins/text.py:65
msgid "Text abort: please select a font file"
msgstr "Tekst fout: selecteer alstublieft een lettertype bestand"

#: bCNC/plugins/text.py:38
msgid "Text to generate"
msgstr "Te genereren tekst"

#: bCNC/Utils.py:573
#, fuzzy
#| msgid "The following report is about to be send to the author of {}"
msgid "The following report is about to be send to the author of {}"
msgstr ""
"Het volgende rapport staat klaar om naar de auteur van {} te worden gestuurd"

#: bCNC/plugins/sketch.py:239
msgid "The maximum illumination shouldn't be more than 250!"
msgstr ""

#: bCNC/Utils.py:695
msgid "There was a problem connecting to the web site"
msgstr "Er trad een probleem op bij het verbinding maken met de website"

#: bCNC/ToolsPage.py:742
msgid "Thickness"
msgstr "Dikte"

#: bCNC/plugins/Helical_Descent.py:57
msgid "This is my Helical Descent"
msgstr ""

#: bCNC/plugins/tile.py:13
msgid "Tile"
msgstr "Tegel"

#: bCNC/plugins/Random.py:44 bCNC/plugins/simpleRotate.py:48
#: bCNC/plugins/simpleTranslate.py:46 bCNC/plugins/tile.py:44
msgid "Tile error"
msgstr "Tegel fout"

#: bCNC/plugins/Random.py:80 bCNC/plugins/tile.py:85
msgid "Tiled selected blocks"
msgstr ""

#: bCNC/bmain.py:1222 bCNC/bmain.py:1301
msgid "Time:"
msgstr "Tijd:"

#: bCNC/CNCCanvas.py:2437
#, fuzzy
msgid "Timeout:"
msgstr "Tijd:"

#: bCNC/CNCCanvas.py:2347
msgid "Toggle display of axes"
msgstr "Schakel tussen weergave van assen"

#: bCNC/CNCCanvas.py:2418
msgid "Toggle display of camera"
msgstr "Schakel tussen weergave van camera"

#: bCNC/CNCCanvas.py:2357
msgid "Toggle display of grid lines"
msgstr "Schakel tussen weergave van rasterlijnen"

#: bCNC/CNCCanvas.py:2367
msgid "Toggle display of margins"
msgstr "Schakel tussen weergave van marges"

#: bCNC/CNCCanvas.py:2388
msgid "Toggle display of paths (G1,G2,G3)"
msgstr "Schakel tussen weergave van paden (G1,G2,G3)"

#: bCNC/CNCCanvas.py:2378
msgid "Toggle display of probe"
msgstr "Schakel tussen weergave van sonde"

#: bCNC/CNCCanvas.py:2398
#, fuzzy
msgid "Toggle display of rapid motion (G0)"
msgstr "Schakel tussen weergave van rapid motion (G0)"

#: bCNC/CNCCanvas.py:2408
msgid "Toggle display of workarea"
msgstr "Schakel tussen weergave van werkgebied"

#: bCNC/EditorPage.py:316
msgid "Toggle enable/disable block of g-code [Ctrl-L]"
msgstr "Schakel gcode blok in/uit"

#: bCNC/EditorPage.py:359
msgid "Toggle expand/collapse blocks of gcode [Ctrl-E]"
msgstr "Blokken gcode uitschakelen/samenvouwen  [Ctrl-E]"

#: bCNC/ToolsPage.py:1662
msgid "Toggle island"
msgstr ""

#: bCNC/ProbePage.py:158
msgid "Tool"
msgstr "Gereedschap"

#: bCNC/Utils.py:852
msgid "Tool Tip:"
msgstr "Hint:"

#: bCNC/ProbePage.py:1979
msgid "Tool change policy"
msgstr "Gereedschapswijzigingsbeleid"

#: bCNC/ControlPage.py:2041
msgid "Tool length offset [G43.1#]"
msgstr ""

#: bCNC/ControlPage.py:1957
msgid "Tool number [T#]"
msgstr "Gereedschap nummer [T#]"

#: bCNC/ProbePage.py:2127
msgid "Tool probe height"
msgstr "Gereedschap sonde hoogte"

#: bCNC/CNCCanvas.py:2273 bCNC/ControlPage.py:1950
msgid "Tool:"
msgstr "Gereedschap:"

#: bCNC/Utils.py:857
msgid "Tooltip for button"
msgstr "Hint voor knop"

#: bCNC/EditorPage.py:462
msgid "Top"
msgstr "Boven"

#: bCNC/EditorPage.py:459
msgid "Top-Left"
msgstr "Linksboven"

#: bCNC/EditorPage.py:465
msgid "Top-Right"
msgstr "Rechtsboven"

#: bCNC/EditorPage.py:558
msgid "Transform"
msgstr "Transformeren"

#: bCNC/plugins/simpleTranslate.py:19
msgid "Translates a block to a new position"
msgstr ""

#: bCNC/ToolsPage.py:647
msgid "Travel x"
msgstr "Beweging x"

#: bCNC/ToolsPage.py:648
msgid "Travel y"
msgstr "Beweging y"

#: bCNC/ToolsPage.py:649
msgid "Travel z"
msgstr "Beweging z"

#: bCNC/plugins/trochoidal_3D.py:47
msgid "Trochoid Cut Diameter"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:402
#, fuzzy
msgid "Trochoid Cut Diameter has to be greater than End mill"
msgstr "Halftone fout: Minimum diameter moet kleiner zijn dan Maximum"

#: bCNC/plugins/trochoidal_3D.py:1672
msgid "Trochoid Generated"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:397
#, fuzzy
#| msgid "Driller abort: Please select some path"
msgid "Trochoid abort: Please select some path"
msgstr "Driller fout: Selecteer alstublieft een pad"

#: bCNC/plugins/trochoidal.py:52
msgid "Trochoid diameter (<= cutter D)"
msgstr ""

#: bCNC/plugins/trochoidal.py:49
msgid "Trochoid entry (prepare for helicut)"
msgstr ""

#: bCNC/plugins/trochoidal.py:18
msgid "Trochoidal"
msgstr ""

#: bCNC/plugins/trochoidPath.py:20
msgid "Trochoidal Path"
msgstr ""

#: bCNC/plugins/trochoidal.py:24
msgid "Trochoidal g-code postprocessor"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:30
msgid "Trochoidcut"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:50
msgid "Trochoids Advance"
msgstr ""

#: bCNC/ProbePage.py:1568
msgid "Turn on/off edge detection"
msgstr "Schakel rand detectie aan/uit"

#: bCNC/ProbePage.py:1583
msgid "Turn on/off freeze image"
msgstr "Schakel beeld bevriezing aan/uit"

#: bCNC/lib/bFileDialog.py:305 bCNC/ToolsPage.py:709
msgid "Type"
msgstr "Type"

#: bCNC/plugins/trochoidal_3D.py:56
msgid "Type of Splice"
msgstr ""

#: bCNC/plugins/drillmark.py:56
#, fuzzy
#| msgid "Shape of the tap"
msgid "Type of the mark"
msgstr "Vorm van de tap"

#: bCNC/Ribbon.py:604
msgid "Undo [Ctrl-Z]"
msgstr "Ongedaan maken [Ctrl-Z]"

#: bCNC/ToolsPage.py:636
msgid "Units (inches)"
msgstr "Eenheden (inches)"

#: bCNC/ControlPage.py:1942
msgid "Units [G20, G21]"
msgstr "Eenheden [G20, G21]"

#: bCNC/ControlPage.py:1931
msgid "Units:"
msgstr "Eenheden:"

#: bCNC/ControlPage.py:108
msgid "Unlock"
msgstr "Ontgrendel"

#: bCNC/ControlPage.py:115
msgid "Unlock controller [$X]"
msgstr "Ontgrendel controller [$X]"

#: bCNC/EditorPage.py:128
msgid "Unselect all blocks [Ctrl-Shift-A]"
msgstr "Deselecteer alle blokken [Ctrl-Shift-A]"

#: bCNC/controllers/_GenericGRBL.py:60
msgid "Unsupported or invalid g-code command found in block."
msgstr "Niet-ondersteunde of ongeldige g-code opdracht gevonden in blok."

#: bCNC/EditorPage.py:505
msgid "Up"
msgstr "Omhoog"

#: bCNC/FilePage.py:203
msgid "Updates"
msgstr "Updates"

#: bCNC/plugins/heightmap.py:56
#, fuzzy
#| msgid "Use a brightess map to create a variable Z path"
msgid "Use a brightness map to create a variable Z path"
msgstr "Gebruik een helderheidskaart om een variabel Z pad te maken"

#: bCNC/plugins/driller.py:49
msgid "Use anchor"
msgstr ""

#: bCNC/ToolsPage.py:1921
msgid "User File"
msgstr "Gebruikersbestand"

#: bCNC/Utils.py:823
msgid "User configurable button"
msgstr "Door gebruiker instelbare knop"

#: bCNC/ToolsPage.py:1981
msgid "Value"
msgstr "Waarde"

#: bCNC/controllers/_GenericGRBL.py:107
msgid "Value out of range. (grblHAL)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:118
msgid "Value word conflict. (grblHAL)"
msgstr ""

#: bCNC/EditorPage.py:627
msgid "Vertical"
msgstr "Verticaal"

#: bCNC/plugins/endmilloffset.py:567 bCNC/bmain.py:2146
#, fuzzy
#| msgid "WARNING: {}"
msgid "WARNING: {}"
msgstr "WAARSCHUWING: {}"

#: bCNC/ControlPage.py:261 bCNC/ProbePage.py:765
#, fuzzy
msgid "WPos:"
msgstr "WPos:"

#: bCNC/bmain.py:2524
msgid "Warning"
msgstr "Waarschuwing"

#: bCNC/plugins/function_plot.py:110
#, fuzzy
#| msgid "Warning"
msgid "Warning: "
msgstr "Waarschuwing"

#: bCNC/ToolsPage.py:621
msgid "Web Camera"
msgstr ""

#: bCNC/ToolsPage.py:624
msgid "Web Camera Angle"
msgstr "Web Camera Hoek"

#: bCNC/ToolsPage.py:623
msgid "Web Camera Height"
msgstr "Web Camera Hoogte"

#: bCNC/ToolsPage.py:622
msgid "Web Camera Width"
msgstr "Web Camera Breedte"

#: bCNC/plugins/box.py:348
msgid "Width Dx"
msgstr "Breedte Dx"

#: bCNC/plugins/flatten.py:291
msgid "Width to flatten"
msgstr "Breedte om af te platten"

#: bCNC/ProbePage.py:149
msgid "Work surface camera view and alignment"
msgstr "Werkvlak camera weergave en uitlijning"

#: bCNC/plugins/halftone.py:52 bCNC/plugins/sketch.py:43
#: bCNC/plugins/text.py:39 bCNC/plugins/heightmap.py:65
#: bCNC/plugins/pyrograph.py:39
msgid "Working Depth"
msgstr "Werkdiepte"

#: bCNC/plugins/simpleDrill.py:67 bCNC/ControlPage.py:1015
msgid "X"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:80
msgid "X Initial"
msgstr ""

#: bCNC/plugins/scaling.py:39
msgid "X Scale"
msgstr ""

#: bCNC/plugins/scaling.py:42
#, fuzzy
#| msgid "Center"
msgid "X Y Center"
msgstr "Centrum"

#: bCNC/ProbePage.py:1275
msgid "X bins"
msgstr "X bins"

#: bCNC/plugins/function_plot.py:30
#, fuzzy
#| msgid "Internal Dimensions"
msgid "X dimension"
msgstr "Interne dimensies"

#: bCNC/ProbePage.py:1255
msgid "X maximum"
msgstr "X maximum"

#: bCNC/ProbePage.py:1247
msgid "X minimum"
msgstr "X minimum"

#: bCNC/plugins/function_plot.py:32
msgid "X number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:289 bCNC/plugins/spiral.py:472
msgid "X start"
msgstr "X start"

#: bCNC/ProbePage.py:1263
msgid "X step"
msgstr "X stap"

#: bCNC/ControlPage.py:274
msgid "X work position (click to set)"
msgstr "X werkpositie (klik op in te stellen)"

#: bCNC/ProbePage.py:1241
msgid "X:"
msgstr ""

#: bCNC/ControlPage.py:349
msgid "X=0"
msgstr ""

#: bCNC/ControlPage.py:399
msgid "XY=0"
msgstr ""

#: bCNC/ControlPage.py:415
msgid "XYZ=0"
msgstr ""

#: bCNC/plugins/simpleDrill.py:68 bCNC/ControlPage.py:936
msgid "Y"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:81
msgid "Y Initial"
msgstr ""

#: bCNC/plugins/scaling.py:40
msgid "Y Scale"
msgstr ""

#: bCNC/ProbePage.py:1315
msgid "Y bins"
msgstr "Y bins"

#: bCNC/plugins/function_plot.py:31
#, fuzzy
#| msgid "Internal Dimensions"
msgid "Y dimension"
msgstr "Interne dimensies"

#: bCNC/ProbePage.py:1295
msgid "Y maximum"
msgstr "Y maximum"

#: bCNC/ProbePage.py:1287
msgid "Y minimum"
msgstr "Y minimum"

#: bCNC/plugins/function_plot.py:33
msgid "Y number line xpacing"
msgstr ""

#: bCNC/plugins/flatten.py:290 bCNC/plugins/spiral.py:473
msgid "Y start"
msgstr "Y start"

#: bCNC/ProbePage.py:1303
msgid "Y step"
msgstr "Y stap"

#: bCNC/ControlPage.py:290
msgid "Y work position (click to set)"
msgstr "Y werkpositie (klik op in te stellen)"

#: bCNC/ProbePage.py:1281
msgid "Y:"
msgstr ""

#: bCNC/ControlPage.py:365
msgid "Y=0"
msgstr ""

#: bCNC/ToolsPage.py:1154
msgid "You cannot have both the number of tabs or distance equal to zero"
msgstr "U kunt niet zowel het aantal tabs als de afstand gelijk aan nul hebben"

#: bCNC/ToolsPage.py:807
msgid "You should probably always use 'on path', unless you are threadmilling!"
msgstr ""

#: bCNC/Utils.py:591
msgid "Your email"
msgstr "Uw e-mail adres"

#: bCNC/ControlPage.py:933 bCNC/ProbePage.py:462
msgid "Z"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:88
msgid "Z Feed Multiplier"
msgstr ""

#: bCNC/plugins/Helical_Descent.py:82
msgid "Z Initial"
msgstr ""

#: bCNC/ProbePage.py:1328
msgid "Z Minimum depth to scan"
msgstr "Minimum Z diepte om te scannen"

#: bCNC/plugins/scaling.py:41
msgid "Z Scale"
msgstr ""

#: bCNC/plugins/simpleDrill.py:72
msgid "Z feed for drilling"
msgstr ""

#: bCNC/ProbePage.py:1336
msgid "Z safe to move"
msgstr "Z veilig om te bewegen"

#: bCNC/plugins/spiral.py:474
#, fuzzy
#| msgid "X start"
msgid "Z start"
msgstr "X start"

#: bCNC/ControlPage.py:306
msgid "Z work position (click to set)"
msgstr "Z werkpositie (klik op in te stellen)"

#: bCNC/ProbePage.py:1322
msgid "Z:"
msgstr ""

#: bCNC/ControlPage.py:381
msgid "Z=0"
msgstr ""

#: bCNC/ProbePage.py:202
msgid "Zero"
msgstr "Nul"

#: bCNC/plugins/zigzag.py:15
msgid "Zig-Zag"
msgstr "Zig-Zag"

#: bCNC/plugins/zigzag.py:148
msgid "Zig-Zag abort: depth must be minor or equal to zero"
msgstr "Zig-Zag fout: diepte moet kleiner of gelijk aan 0 zijn"

#: bCNC/plugins/zigzag.py:143
msgid "Zig-Zag abort: verify CornerRes >= 0"
msgstr "Zig-Zag fout: controleer CornerRes >= 0"

#: bCNC/plugins/zigzag.py:135
msgid "Zig-Zag abort: verify LineLen > 0"
msgstr "Zig-Zag fout: controleer LineLen > 0"

#: bCNC/plugins/zigzag.py:131
msgid "Zig-Zag abort: verify Nlines > 0"
msgstr "Zig-Zag fout: controleer Nlines > 0"

#: bCNC/plugins/zigzag.py:139
msgid "Zig-Zag abort: verify Step > 0"
msgstr "Zig-Zag fout: controleer Stap > 0"

#: bCNC/CNCCanvas.py:2256
msgid "Zoom In [Ctrl-=]"
msgstr "Inzoomen [Ctrl-=]"

#: bCNC/CNCCanvas.py:2263
msgid "Zoom Out [Ctrl--]"
msgstr "Uitzoomen [Ctrl--]"

#: bCNC/ControlPage.py:1400
#, fuzzy
#| msgid "Control"
msgid "abcControl"
msgstr "Controle"

#: bCNC/ControlPage.py:625
msgid "abcDRO"
msgstr ""

#: bCNC/ControlPage.py:637
#, fuzzy
msgid "abcWPos:"
msgstr "WPos:"

#: bCNC/plugins/endmilloffset.py:479
msgid "acts like a tool corrector inside the profile"
msgstr ""

#: bCNC/plugins/simpleRotate.py:28
#, fuzzy
#| msgid "Resolution (degrees)"
msgid "angle step (degrees)"
msgstr "Resolutie (graden)"

#: bCNC/plugins/dragknife.py:54
msgid "angle threshold"
msgstr ""

#: bCNC/plugins/arcfit.py:38
msgid "arc precision (mm)"
msgstr ""

#: bCNC/controllers/_GenericGRBL.py:12
msgid "bCNC is currently sending a gcode program to Grbl"
msgstr "bCNC is momenteel een gcode programma aan het verzenden naar Grbl"

#: bCNC/plugins/simpleRectangle.py:97
msgid "clockwise"
msgstr ""

#: bCNC/plugins/dragknife.py:48
msgid "distance from dragknife rotation center to the tip of the blade"
msgstr ""

#: bCNC/plugins/dragknife.py:55
msgid "do not perform pivot action for angles smaller than this"
msgstr ""

#: bCNC/plugins/dragknife.py:47
msgid "dragknife offset"
msgstr ""

#: bCNC/CNCCanvas.py:678
#, fuzzy
#| msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgid "dx={:g}  dy={:g}  dz={:g}  length={:g}  angle={:g}"
msgstr "dx={:g}  dy={:g}  dz={:g}  lengte={:g}  hoek={:g}"

#: bCNC/plugins/dragknife.py:75
msgid "feedrate"
msgstr ""

#: bCNC/ProbePage.py:2047 bCNC/ProbePage.py:2092
msgid "get"
msgstr "verkrijg"

#: bCNC/plugins/arcfit.py:39
msgid "how precisely must arc fit. set to 0 to disable arc fitting"
msgstr ""

#: bCNC/plugins/endmilloffset.py:463
msgid "indicates the direction (inside / outside) for making profiles"
msgstr ""

#: bCNC/plugins/dragknife.py:70
msgid "initial direction"
msgstr ""

#: bCNC/plugins/slicemesh.py:83
msgid "layer height (0 = only single zmin)"
msgstr ""

#: bCNC/plugins/arcfit.py:45
msgid "line precision (mm)"
msgstr ""

#: bCNC/plugins/slicemesh.py:88
msgid "maximum Z height"
msgstr ""

#: bCNC/plugins/arcfit.py:50
msgid "minimal number of segments to create arc"
msgstr ""

#: bCNC/plugins/slicemesh.py:86
msgid "minimum Z height"
msgstr ""

#: bCNC/plugins/simpleRotate.py:29 bCNC/plugins/simpleTranslate.py:28
msgid "nb repeat including original"
msgstr ""

#: bCNC/CNCCanvas.py:627
#, fuzzy
#| msgid "origin {:g} {:g} {:g}"
msgid "origin {:g} {:g} {:g}"
msgstr "oorsprong {:g} {:g} {:g}"

#: bCNC/bmain.py:3097
msgid "python serial missing"
msgstr "python serial mist"

#: bCNC/plugins/slicemesh.py:74
msgid "scale factor"
msgstr ""

#: bCNC/plugins/linearize.py:39
msgid "segment size"
msgstr ""

#: bCNC/ControlPage.py:1961 bCNC/ControlPage.py:2003 bCNC/ControlPage.py:2045
#: bCNC/ProbePage.py:360
msgid "set"
msgstr "stel in"

#: bCNC/plugins/dragknife.py:80
msgid "simulate"
msgstr ""

#: bCNC/plugins/dragknife.py:89
msgid "simulation precision"
msgstr ""

#: bCNC/plugins/slicemesh.py:23
msgid "slicemesh"
msgstr ""

#: bCNC/plugins/trochoidal_3D.py:101
msgid "splice steps every 360 degrees"
msgstr ""

#: bCNC/plugins/linearize.py:48
msgid "subdiv lines"
msgstr ""

#: bCNC/plugins/dragknife.py:61
msgid "swivel height"
msgstr ""

#: bCNC/Sender.py:355
#, fuzzy
msgid "unknown command"
msgstr "onbekend commando"

#: bCNC/plugins/simpleRotate.py:26
#, fuzzy
#| msgid "Center"
msgid "x center"
msgstr "Centrum"

#: bCNC/plugins/simpleTranslate.py:26
#, fuzzy
#| msgid "Depth Increment"
msgid "x increment"
msgstr "Toename van diepte"

#: bCNC/plugins/simpleLine.py:52 bCNC/plugins/simpleRectangle.py:93
msgid "xEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:51 bCNC/plugins/simpleRectangle.py:92
#, fuzzy
#| msgid "Start"
msgid "xStart"
msgstr "Start"

#: bCNC/plugins/simpleRotate.py:27
#, fuzzy
#| msgid "Center"
msgid "y center"
msgstr "Centrum"

#: bCNC/plugins/simpleTranslate.py:27
#, fuzzy
#| msgid "Depth Increment"
msgid "y increment"
msgstr "Toename van diepte"

#: bCNC/plugins/simpleLine.py:54 bCNC/plugins/simpleRectangle.py:95
msgid "yEnd"
msgstr ""

#: bCNC/plugins/simpleLine.py:53 bCNC/plugins/simpleRectangle.py:94
#, fuzzy
#| msgid "Start"
msgid "yStart"
msgstr "Start"

#: bCNC/plugins/slicemesh.py:77
msgid "z offset"
msgstr ""

#~ msgid ""
#~ "ERROR: Please install the python pyserial module\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tor yum install python-serial\n"
#~ "\tor dnf install python-pyserial"
#~ msgstr ""
#~ "ERROR: Installeer alstublieft de python pyserial module\n"
#~ "Windows:\n"
#~ "\tC:\\PythonXX\\Scripts\\easy_install pyserial\n"
#~ "Mac:\tpip install pyserial\n"
#~ "Linux:\tsudo apt-get install python-serial\n"
#~ "\tof yum install python-serial\n"
#~ "\tof dnf install python-pyserial"

#~ msgid "Color configuration"
#~ msgstr "Kleur configuratie"

#~ msgid "Tools"
#~ msgstr "Gereedschappen"

#~ msgid "Set current location as Z-zero for leveling"
#~ msgstr "Stel huidige locatie in als Z-nul"

#~ msgid "Inkscape"
#~ msgstr "Inkscape"
