#!/usr/bin/env sh

echo "###########################################
WARNING! THIS IS LEGACY MODE!
DO NOT USE THIS IF YOU ARE A BCNC DEVELOPER
GO TO REPOSITORY ROOT AND <PERSON><PERSON>CH BCNC USING
FOLLOWING COMMAND:

python2 -m bCNC
###########################################
"


DIR=`dirname $0`
PYTHONPATH=${DIR}:${DIR}/lib:${DIR}/plugins:${DIR}/controllers
export DIR PYTHONPATH

#Autodetect python version
[ .$PYTHON = . ] && PYTHON=`which python2`
[ .$PYTHON = . ] && PYTHON=python

${PYTHON} ${DIR}/__main__.py $*
#python -m cProfile -o bCNC.out ${DIR}/__main__.py $*
