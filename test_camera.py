#!/usr/bin/env python3
"""
Camera Test Script for bCNC
Tests if camera is working and shows available cameras
"""

import sys
import os

try:
    import cv2
    print("✅ OpenCV is installed")
except ImportError:
    print("❌ OpenCV not found. Install with: pip install opencv-python")
    sys.exit(1)

def test_cameras():
    """Test available cameras"""
    print("\n🎥 Testing available cameras...")
    
    working_cameras = []
    
    # Test cameras 0-4
    for i in range(5):
        print(f"Testing camera {i}...", end=" ")
        
        try:
            cap = cv2.VideoCapture(i)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"✅ Working - Resolution: {width}x{height}")
                    working_cameras.append(i)
                else:
                    print("❌ Can't read frame")
            else:
                print("❌ Can't open")
                
            cap.release()
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return working_cameras

def test_camera_detailed(camera_id):
    """Test specific camera with detailed info"""
    print(f"\n📹 Detailed test for camera {camera_id}:")
    
    try:
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            print("❌ Cannot open camera")
            return False
        
        # Get camera properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"Resolution: {width}x{height}")
        print(f"FPS: {fps}")
        
        # Test frame capture
        ret, frame = cap.read()
        if ret:
            print("✅ Frame capture successful")
            
            # Save test image
            test_image_path = f"camera_{camera_id}_test.jpg"
            cv2.imwrite(test_image_path, frame)
            print(f"📸 Test image saved as: {test_image_path}")
            
        else:
            print("❌ Cannot capture frame")
            
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_bCNC_config(camera_id):
    """Show bCNC configuration for the camera"""
    print(f"\n⚙️  bCNC Configuration for camera {camera_id}:")
    print("Add these lines to your bCNC.ini file under [Camera] section:")
    print(f"aligncam = {camera_id}")
    print("aligncam_width = 640")
    print("aligncam_height = 480")
    print("aligncam_angle = 0")
    print("aligncam_r = 3.175")
    print("aligncam_scale = 10.0")
    print("aligncam_anchor = Center")

def main():
    print("🎯 bCNC Camera Test Utility")
    print("=" * 40)
    
    # Test all cameras
    working_cameras = test_cameras()
    
    if not working_cameras:
        print("\n❌ No working cameras found!")
        print("Make sure:")
        print("- Camera is connected")
        print("- Camera drivers are installed")
        print("- Camera is not being used by another application")
        return
    
    print(f"\n✅ Found {len(working_cameras)} working camera(s): {working_cameras}")
    
    # Test first working camera in detail
    first_camera = working_cameras[0]
    test_camera_detailed(first_camera)
    
    # Show bCNC configuration
    show_bCNC_config(first_camera)
    
    print(f"\n🎯 Recommended camera for bCNC: {first_camera}")
    print("\nNext steps:")
    print("1. Update your bCNC.ini file with the configuration above")
    print("2. Restart bCNC")
    print("3. Go to Probe → Camera → Switch To")
    print("4. Use the Two Point Alignment feature!")

if __name__ == "__main__":
    main()
