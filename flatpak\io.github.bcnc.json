{"app-id": "io.github.bcnc", "runtime": "org.freedesktop.Platform", "runtime-version": "24.08", "sdk": "org.freedesktop.Sdk", "build-options": {"build-args": ["--share=network"]}, "finish-args": ["--share=network", "--share=ipc", "--socket=x11", "--filesystem=home:rw", "--device=all"], "command": "bCNC", "modules": [{"name": "bcnc", "buildsystem": "simple", "build-commands": ["pip3 install --prefix=${FLATPAK_DEST} pyserial", "pip3 install --prefix=${FLATPAK_DEST} shxparser", "pip3 install --prefix=${FLATPAK_DEST} svgelements", "pip3 install --prefix=${FLATPAK_DEST} Pillow", "pip3 install --prefix=${FLATPAK_DEST} numpy", "pip3 install --prefix=${FLATPAK_DEST} opencv-python==********", "pip3 install --prefix=/app --upgrade .", "install -p -m644 -D bCNC/bCNC.png ${FLATPAK_DEST}/share/icons/hicolor/192x192/apps/io.github.bcnc.png", "install -p -m644 -D bCNC/bCNC.desktop ${FLATPAK_DEST}/share/applications/io.github.bcnc.desktop", "desktop-file-install --dir=${FLATPAK_DEST}/share/applications/ --set-key=Icon --set-value=io.github.bcnc ${FLATPAK_DEST}/share/applications/io.github.bcnc.desktop"], "sources": [{"type": "git", "path": ".."}]}, {"name": "tcl", "buildsystem": "autotools", "subdir": "unix", "post-install": ["chmod 755 /app/lib/libtcl*.so"], "cleanup": ["/bin", "/lib/pkgconfig", "/man"], "sources": [{"type": "archive", "url": "https://prdownloads.sourceforge.net/tcl/tcl8.6.13-src.tar.gz", "sha256": "43a1fae7412f61ff11de2cfd05d28cfc3a73762f354a417c62370a54e2caf066"}]}, {"name": "tk", "buildsystem": "autotools", "subdir": "unix", "post-install": ["chmod 755 /app/lib/libtk*.so"], "cleanup": ["/bin", "/lib/pkgconfig", "/man"], "sources": [{"type": "archive", "url": "https://prdownloads.sourceforge.net/tcl/tk8.6.13-src.tar.gz", "sha256": "2e65fa069a23365440a3c56c556b8673b5e32a283800d8d9b257e3f584ce0675"}]}, {"name": "tkinter", "buildsystem": "simple", "build-commands": ["pip3 install --prefix=${FLATPAK_DEST} ."], "sources": [{"type": "git", "url": "https://github.com/iwalton3/tkinter-standalone", "commit": "20950173c51b7d4b67cfb5d765d41c2600f9bee3"}]}]}